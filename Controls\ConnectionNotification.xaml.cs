using System;
using System.Media;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Threading;
using SafeLink.Models;

namespace SafeLink.Controls
{
    public partial class ConnectionNotification : UserControl
    {
        private DispatcherTimer _hideTimer;
        private Storyboard _slideInAnimation;
        private Storyboard _slideOutAnimation;
        private Storyboard _glowAnimation;

        public ConnectionNotification()
        {
            InitializeComponent();
            InitializeAnimations();
            InitializeTimer();
        }

        private void InitializeAnimations()
        {
            _slideInAnimation = (Storyboard)Resources["SlideInAnimation"];
            _slideOutAnimation = (Storyboard)Resources["SlideOutAnimation"];
            _glowAnimation = (Storyboard)Resources["GlowAnimation"];

            _slideOutAnimation.Completed += (s, e) =>
            {
                Visibility = Visibility.Collapsed;
            };
        }

        private void InitializeTimer()
        {
            _hideTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(4)
            };
            _hideTimer.Tick += (s, e) =>
            {
                _hideTimer.Stop();
                HideNotification();
            };
        }

        public void ShowConnectionNotification(User user, bool isConnected)
        {
            SetUserInfo(user, isConnected);
            PlayNotificationSound(isConnected);
            ShowNotification();
        }

        private void SetUserInfo(User user, bool isConnected)
        {
            // Set username
            UsernameText.Text = user.Username;

            // Set status text
            StatusText.Text = isConnected ? "متصل الآن" : "قطع الاتصال";

            // Set rank info
            var (rankIcon, rankText, rankColor) = GetRankInfo(user.Role);
            RankIcon.Text = rankIcon;
            RankText.Text = rankText;

            // Set colors based on connection status and rank
            var connectionColor = isConnected ? rankColor : "#F44336";
            
            // Update rank container background
            var gradientBrush = new LinearGradientBrush();
            gradientBrush.StartPoint = new Point(0, 0);
            gradientBrush.EndPoint = new Point(1, 1);
            gradientBrush.GradientStops.Add(new GradientStop(
                (Color)ColorConverter.ConvertFromString(connectionColor), 0));
            gradientBrush.GradientStops.Add(new GradientStop(
                (Color)ColorConverter.ConvertFromString(DarkenColor(connectionColor, 0.1)), 1));
            
            RankContainer.Background = gradientBrush;

            // Update glow and status indicator
            GlowBorder.Background = new SolidColorBrush(
                (Color)ColorConverter.ConvertFromString(connectionColor));
            StatusIndicator.Background = new SolidColorBrush(
                (Color)ColorConverter.ConvertFromString(connectionColor));
            
            // Update status indicator effect
            StatusIndicator.Effect = new DropShadowEffect
            {
                Color = (Color)ColorConverter.ConvertFromString(connectionColor),
                BlurRadius = 8,
                ShadowDepth = 0,
                Opacity = 0.8
            };

            // Update status text color
            StatusText.Foreground = new SolidColorBrush(
                isConnected ? Colors.LightGray : (Color)ColorConverter.ConvertFromString("#FFCDD2"));
            
            RankText.Foreground = new SolidColorBrush(
                (Color)ColorConverter.ConvertFromString(connectionColor));
        }

        private (string icon, string text, string color) GetRankInfo(UserRole role)
        {
            return role switch
            {
                UserRole.Developer => ("👑", "مطور", "#FFD700"),
                UserRole.Admin => ("⚡", "إدارة", "#FF5722"),
                UserRole.Moderator => ("🛡️", "مشرف", "#2196F3"),
                UserRole.User => ("👤", "مستخدم", "#4CAF50"),
                _ => ("👤", "مستخدم", "#4CAF50")
            };
        }

        private string DarkenColor(string hexColor, double factor)
        {
            var color = (Color)ColorConverter.ConvertFromString(hexColor);
            var r = (byte)(color.R * (1 - factor));
            var g = (byte)(color.G * (1 - factor));
            var b = (byte)(color.B * (1 - factor));
            return $"#{r:X2}{g:X2}{b:X2}";
        }

        private void PlayNotificationSound(bool isConnected)
        {
            try
            {
                // Play different sounds for connect/disconnect
                if (isConnected)
                {
                    // Connection sound - pleasant notification
                    SafeLink.Core.SoundManager.PlayConnectionSound();
                }
                else
                {
                    // Disconnection sound - subtle warning
                    SafeLink.Core.SoundManager.PlayDisconnectionSound();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing notification sound: {ex.Message}");
            }
        }

        private void ShowNotification()
        {
            Visibility = Visibility.Visible;
            Opacity = 0;

            // Start glow animation
            _glowAnimation.Begin();

            // Start slide in animation
            _slideInAnimation.Begin();

            // Start hide timer
            _hideTimer.Start();
        }

        private void HideNotification()
        {
            _glowAnimation.Stop();
            _slideOutAnimation.Begin();
        }

        public void ForceHide()
        {
            _hideTimer.Stop();
            HideNotification();
        }
    }
}
