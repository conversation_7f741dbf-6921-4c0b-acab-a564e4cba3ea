﻿#pragma checksum "..\..\..\..\Views\DeveloperWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "14CD66C20A54E3755616F7B6A5ACA3836A76F271"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Views {
    
    
    /// <summary>
    /// DeveloperWindow
    /// </summary>
    public partial class DeveloperWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 141 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshUsersButton;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CreateUserButton;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid UsersDataGrid;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshSystemButton;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectedUsersText;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UptimeText;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryUsageText;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuUsageText;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Views\DeveloperWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SystemDetailsPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/views/developerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DeveloperWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RefreshUsersButton = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\Views\DeveloperWindow.xaml"
            this.RefreshUsersButton.Click += new System.Windows.RoutedEventHandler(this.RefreshUsersButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CreateUserButton = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\..\Views\DeveloperWindow.xaml"
            this.CreateUserButton.Click += new System.Windows.RoutedEventHandler(this.CreateUserButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.UsersDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 6:
            this.RefreshSystemButton = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\..\Views\DeveloperWindow.xaml"
            this.RefreshSystemButton.Click += new System.Windows.RoutedEventHandler(this.RefreshSystemButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ConnectedUsersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.UptimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.MemoryUsageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CpuUsageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.SystemDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            
            #line 339 "..\..\..\..\Views\DeveloperWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateUpdatePackage_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 347 "..\..\..\..\Views\DeveloperWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SendUpdateCommand_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 4:
            
            #line 178 "..\..\..\..\Views\DeveloperWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetDeviceButton_Click);
            
            #line default
            #line hidden
            break;
            case 5:
            
            #line 185 "..\..\..\..\Views\DeveloperWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteUserButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

