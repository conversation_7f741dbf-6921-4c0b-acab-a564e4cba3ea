using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

class SimpleNetworkTest
{
    static async Task Main()
    {
        Console.WriteLine("🔍 Simple SafeLink Network Test");
        Console.WriteLine("===============================");
        
        // Test ports 8888-8895
        for (int port = 8888; port <= 8895; port++)
        {
            await TestPort(port);
        }
        
        Console.WriteLine("\n✅ Test completed!");
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
    
    static async Task TestPort(int port)
    {
        try
        {
            using var client = new UdpClient();
            client.Client.ReceiveTimeout = 500;
            
            // Send probe
            var probe = "SAFELINK_NETWORK_PROBE";
            var data = Encoding.UTF8.GetBytes(probe);
            var endpoint = new IPEndPoint(IPAddress.Broadcast, port);
            
            await client.SendAsync(data, data.Length, endpoint);
            
            // Listen for response
            try
            {
                var result = await client.ReceiveAsync();
                var response = Encoding.UTF8.GetString(result.Buffer);
                
                Console.WriteLine($"📡 Port {port}: ACTIVE - Response: {response}");
            }
            catch (SocketException)
            {
                Console.WriteLine($"⚪ Port {port}: No response");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Port {port}: Error - {ex.Message}");
        }
    }
}
