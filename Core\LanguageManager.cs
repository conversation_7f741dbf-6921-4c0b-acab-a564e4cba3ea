using System;
using System.Collections.Generic;
using System.Globalization;
using System.Windows;

namespace SafeLink.Core
{
    public static class LanguageManager
    {
        private static Dictionary<string, Dictionary<string, string>> _translations;
        private static string _currentLanguage = "en";

        static LanguageManager()
        {
            InitializeTranslations();
        }

        private static void InitializeTranslations()
        {
            _translations = new Dictionary<string, Dictionary<string, string>>
            {
                ["en"] = new Dictionary<string, string>
                {
                    // Main Window
                    ["AppTitle"] = "SafeLink - Professional Security Platform",
                    ["LoginTitle"] = "SafeLink - Authentication",
                    ["SecureAccess"] = "SECURE ACCESS",
                    ["AuthenticationPortal"] = "SafeLink Authentication Portal",
                    ["Username"] = "👤 Username:",
                    ["Password"] = "🔐 Password:",
                    ["RememberLogin"] = "🔒 Remember my login",
                    ["Login"] = "LOGIN",
                    ["Register"] = "REGISTER",
                    ["DeviceId"] = "🔧 Device ID:",
                    ["VersionInfo"] = "Version 1.0 - Developed by <PERSON><PERSON><PERSON>",
                    ["EstablishingNetwork"] = "Establishing Secure Network...",
                    ["SystemReady"] = "Ready",
                    ["SelectUser"] = "Select a user to start communication",
                    ["DeveloperMode"] = "Developer Mode",
                    ["ProfessionalPlatform"] = "Professional Security Platform",
                    ["Version"] = "Version 1.0 - Developed by Drikon",

                    // Settings
                    ["Settings"] = "Settings",
                    ["Language"] = "Language",
                    ["English"] = "English",
                    ["Arabic"] = "العربية",
                    ["Theme"] = "Theme",
                    ["Notifications"] = "Notifications",
                    ["Save"] = "Save",
                    ["Cancel"] = "Cancel",
                    ["OK"] = "OK",
                    ["SettingsSaved"] = "Settings saved successfully!",

                    // Chat
                    ["SecureChat"] = "Secure Chat",
                    ["EndToEndEncrypted"] = "End-to-End Encrypted",
                    ["ChatWith"] = "Chat with",
                    ["TypeMessage"] = "Type your message...",
                    ["Send"] = "SEND",

                    // Notifications
                    ["Welcome"] = "Welcome",
                    ["InvalidCredentials"] = "Invalid username or password",
                    ["LoginFailed"] = "Login failed",
                    ["RegistrationSuccessful"] = "Registration successful! Please login.",
                    ["RegistrationFailed"] = "Registration failed",
                    ["UsernameExists"] = "Username already exists",
                    ["FillAllFields"] = "Please fill in all required fields",
                    ["PasswordTooShort"] = "Password must be at least 4 characters long",
                    ["Yes"] = "Yes",
                    ["No"] = "No",
                    ["ConfirmClose"] = "Confirm Close",
                    ["DoYouWantToClose"] = "Do you want to close the program?",
                    ["AllConnectionsWillBeClosed"] = "All active connections will be closed",
                    ["LoginFailed"] = "Login Failed",
                    ["RegistrationFailed"] = "Registration Failed",

                    // Connection Status
                    ["Connected"] = "Connected",
                    ["Disconnected"] = "Disconnected",
                    ["ClickToConnect"] = "Click to connect",
                    ["ClickToDisconnect"] = "Click to disconnect",
                    ["InvalidCredentials"] = "Invalid username or password",
                    ["RegistrationSuccessful"] = "Registration successful! Please login with your credentials",

                    // Welcome Screen
                    ["SafeLinkBrand"] = "SAFELINK",
                    ["SecureCommunicationPlatform"] = "SECURE COMMUNICATION PLATFORM",
                    ["EnterpriseGradeSecurity"] = "ENTERPRISE GRADE SECURITY",
                    ["InitializingSecurityEnvironment"] = "Initializing Security Environment...",
                    ["SecureConnectionActive"] = "SECURE CONNECTION ACTIVE",
                    ["SafeLinkVersion"] = "SafeLink v1.0.0 | Enterprise Edition",
                    ["CopyrightText"] = "© 2025 Drikon Technologies. All rights reserved.",

                    // Main Application
                    ["SafeLinkTitle"] = "SafeLink",
                    ["User"] = "User",
                    ["Role"] = "Role",
                    ["Connected"] = "Connected",
                    ["ActiveUsers"] = "ACTIVE USERS",
                    ["OnlineCount"] = "● {0} online",
                    ["DeveloperModeActive"] = "Developer Mode Active",

                    // Buttons and Actions
                    ["Report"] = "Report",
                    ["Diagnostic"] = "Diagnostic",
                    ["Logout"] = "Logout",
                    ["Close"] = "Close",
                    ["ShareFile"] = "Share File",
                    ["CloseChat"] = "Close Chat",

                    // Tooltips
                    ["SettingsTooltip"] = "Settings",
                    ["ReportTooltip"] = "Report Issue",
                    ["DiagnosticTooltip"] = "Network Diagnostic",
                    ["LogoutTooltip"] = "Logout",
                    ["PowerOffTooltip"] = "Exit Application",

                    // Exit Confirmation
                    ["ExitConfirmation"] = "Exit Confirmation",
                    ["ExitMessage"] = "Do you want to exit the application?",

                    // System Tray
                    ["Show"] = "Show",
                    ["Exit"] = "Exit",
                    ["MinimizedToTray"] = "Application minimized to system tray",

                    // Loading Messages
                    ["InitializingSecurityProtocols"] = "Initializing Security Protocols...",
                    ["LoadingEncryptionModules"] = "Loading Encryption Modules...",
                    ["EstablishingSecureNetwork"] = "Establishing Secure Network...",
                    ["ConfiguringAuthenticationSystems"] = "Configuring Authentication Systems...",
                    ["ActivatingSecurityLayers"] = "Activating Security Layers...",
                    ["FinalizingSecurityEnvironment"] = "Finalizing Security Environment...",
                    ["SystemReadyLaunchingInterface"] = "System Ready - Launching Interface...",

                    // Settings Window
                    ["LanguageSettings"] = "Language / اللغة",
                    ["InterfaceLanguage"] = "Interface Language:",
                    ["LanguageRestartNote"] = "Language changes will take effect after restart",
                    ["NetworkSettings"] = "Network Settings",
                    ["DiscoveryPort"] = "Discovery Port:",
                    ["EnableAutoDiscovery"] = "Enable automatic user discovery",
                    ["BroadcastPresence"] = "Broadcast my presence to network",
                    ["PrinterSharing"] = "Printer Sharing",
                    ["EnablePrinterSharing"] = "Enable printer sharing",
                    ["AvailablePrinters"] = "Available Printers:",
                    ["AuthorizedUsers"] = "Authorized Users:",
                    ["SecuritySettings"] = "Security Settings",
                    ["EncryptMessages"] = "Encrypt all messages (AES-256)",
                    ["LogActivity"] = "Log user activity",
                    ["ReportShortcut"] = "Report Problem Shortcut:",
                    ["DeviceInformation"] = "Device Information",
                    ["DeviceID"] = "Device ID:",
                    ["MachineName"] = "Machine Name:",
                    ["OSVersion"] = "OS Version:",
                    ["IPAddress"] = "IP Address:",

                    // Dialog Messages
                    ["FactoryResetQuestion"] = "Do you want to perform factory reset?",
                    ["FactoryResetDetails"] = "Details:",
                    ["FactoryResetExplanation"] = "• All data and settings will be deleted\n• All options will be reset to default\n• All saved files will be removed\n• The program will be like a fresh installation\n\n⚠️ This action cannot be undone!",
                    ["ClearDataQuestion"] = "Do you want to clear temporary data?",
                    ["ClearDataExplanation"] = "• Saved login information will be deleted\n• Temporary messages will be cleared\n• Cache files will be removed\n• You will need to log in again",
                    ["Clear"] = "Clear",
                    ["Yes"] = "Yes",
                    ["No"] = "No",

                    // Exit Confirmation
                    ["ConfirmExit"] = "Confirm Exit",
                    ["ConfirmExitTitle"] = "SafeLink - Confirm Exit",
                    ["ExitQuestion"] = "Do you want to close the application?",
                    ["ExitMessage"] = "Do you want to close the application?",

                    // Report Window
                    ["SendReport"] = "SafeLink - Send Report",
                    ["ReportHeader"] = "Send Report",
                    ["ReportSubHeader"] = "Choose the type of report to send",
                    ["ScreenshotReport"] = "Message with Screenshot",
                    ["ScreenshotReportDesc"] = "Send a screenshot with descriptive message",
                    ["SystemStatusReport"] = "System Status Report",
                    ["SystemStatusReportDesc"] = "Send technical information about system status",
                    ["CustomMessageReport"] = "Custom Message",
                    ["CustomMessageReportDesc"] = "Write a custom message with required details",
                    ["CustomReportAdvanced"] = "Advanced Custom Report",
                    ["CustomReportAdvancedDesc"] = "Comprehensive report with custom details option",
                    ["WriteCustomDetails"] = "Write custom details:",
                    ["ReportSent"] = "Report sent successfully",
                    ["ReportFailed"] = "Failed to send report"
                },

                ["ar"] = new Dictionary<string, string>
                {
                    // Main Window
                    ["AppTitle"] = "SafeLink - منصة الأمان الاحترافية",
                    ["LoginTitle"] = "SafeLink - المصادقة",
                    ["SecureAccess"] = "دخول آمن",
                    ["AuthenticationPortal"] = "بوابة المصادقة SafeLink",
                    ["Username"] = "👤 اسم المستخدم:",
                    ["Password"] = "🔐 كلمة المرور:",
                    ["RememberLogin"] = "🔒 تذكر تسجيل دخولي",
                    ["Login"] = "دخول",
                    ["Register"] = "تسجيل",
                    ["DeviceId"] = "🔧 معرف الجهاز:",
                    ["VersionInfo"] = "الإصدار 1.0 - تطوير دريكون",
                    ["EstablishingNetwork"] = "إنشاء شبكة آمنة...",
                    ["SystemReady"] = "جاهز",
                    ["SelectUser"] = "اختر مستخدم لبدء التواصل",
                    ["DeveloperMode"] = "وضع المطور",
                    ["ProfessionalPlatform"] = "منصة الأمان الاحترافية",
                    ["Version"] = "الإصدار 1.0 - تطوير دريكون",

                    // Settings
                    ["Settings"] = "الإعدادات",
                    ["Language"] = "اللغة",
                    ["English"] = "English",
                    ["Arabic"] = "العربية",
                    ["Theme"] = "المظهر",
                    ["Notifications"] = "الإشعارات",
                    ["Save"] = "حفظ",
                    ["Cancel"] = "إلغاء",
                    ["OK"] = "موافق",
                    ["SettingsSaved"] = "تم حفظ الإعدادات بنجاح!",

                    // Chat
                    ["SecureChat"] = "محادثة آمنة",
                    ["EndToEndEncrypted"] = "مشفرة من طرف لطرف",
                    ["ChatWith"] = "محادثة مع",
                    ["TypeMessage"] = "اكتب رسالتك...",
                    ["Send"] = "إرسال",

                    // Notifications
                    ["Welcome"] = "مرحباً",
                    ["InvalidCredentials"] = "اسم المستخدم أو كلمة المرور غير صحيحة",
                    ["LoginFailed"] = "فشل تسجيل الدخول",
                    ["RegistrationSuccessful"] = "تم التسجيل بنجاح! يرجى تسجيل الدخول.",
                    ["RegistrationFailed"] = "فشل التسجيل",
                    ["UsernameExists"] = "اسم المستخدم موجود بالفعل",
                    ["FillAllFields"] = "يرجى ملء جميع الحقول المطلوبة",
                    ["PasswordTooShort"] = "كلمة المرور يجب أن تكون 4 أحرف على الأقل",
                    ["Yes"] = "نعم",
                    ["No"] = "لا",
                    ["ConfirmClose"] = "تأكيد الإغلاق",
                    ["DoYouWantToClose"] = "هل تريد إغلاق البرنامج؟",
                    ["AllConnectionsWillBeClosed"] = "سيتم قطع جميع الاتصالات النشطة",
                    ["LoginFailed"] = "فشل تسجيل الدخول",
                    ["RegistrationFailed"] = "فشل التسجيل",

                    // Connection Status
                    ["Connected"] = "متصل",
                    ["Disconnected"] = "غير متصل",
                    ["ClickToConnect"] = "اضغط للاتصال",
                    ["ClickToDisconnect"] = "اضغط لقطع الاتصال",
                    ["InvalidCredentials"] = "اسم المستخدم أو كلمة المرور غير صحيحة",
                    ["RegistrationSuccessful"] = "تم التسجيل بنجاح! يرجى تسجيل الدخول ببياناتك",

                    // Welcome Screen
                    ["SafeLinkBrand"] = "SAFELINK",
                    ["SecureCommunicationPlatform"] = "منصة التواصل الآمن",
                    ["EnterpriseGradeSecurity"] = "أمان على مستوى المؤسسات",
                    ["InitializingSecurityEnvironment"] = "تهيئة البيئة الأمنية...",
                    ["SecureConnectionActive"] = "الاتصال الآمن نشط",
                    ["SafeLinkVersion"] = "SafeLink الإصدار 1.0.0 | إصدار المؤسسات",
                    ["CopyrightText"] = "© 2025 تقنيات دريكون. جميع الحقوق محفوظة.",

                    // Main Application
                    ["SafeLinkTitle"] = "SafeLink",
                    ["User"] = "المستخدم",
                    ["Role"] = "الدور",
                    ["Connected"] = "متصل",
                    ["ActiveUsers"] = "المستخدمون النشطون",
                    ["OnlineCount"] = "● {0} متصل",
                    ["DeveloperModeActive"] = "وضع المطور نشط",

                    // Buttons and Actions
                    ["Report"] = "الإبلاغ",
                    ["Diagnostic"] = "التشخيص",
                    ["Logout"] = "تسجيل الخروج",
                    ["Close"] = "إغلاق",
                    ["ShareFile"] = "مشاركة ملف",
                    ["CloseChat"] = "إغلاق المحادثة",

                    // Tooltips
                    ["SettingsTooltip"] = "الإعدادات",
                    ["ReportTooltip"] = "الإبلاغ عن مشكلة",
                    ["DiagnosticTooltip"] = "تشخيص الشبكة",
                    ["LogoutTooltip"] = "تسجيل الخروج",
                    ["PowerOffTooltip"] = "إغلاق البرنامج",

                    // Exit Confirmation
                    ["ExitConfirmation"] = "تأكيد الإغلاق",
                    ["ExitMessage"] = "هل تريد إغلاق البرنامج؟",

                    // System Tray
                    ["Show"] = "إظهار",
                    ["Exit"] = "خروج",
                    ["MinimizedToTray"] = "تم تصغير البرنامج إلى شريط المهام",

                    // Loading Messages
                    ["InitializingSecurityProtocols"] = "تهيئة بروتوكولات الأمان...",
                    ["LoadingEncryptionModules"] = "تحميل وحدات التشفير...",
                    ["EstablishingSecureNetwork"] = "إنشاء شبكة آمنة...",
                    ["ConfiguringAuthenticationSystems"] = "تكوين أنظمة المصادقة...",
                    ["ActivatingSecurityLayers"] = "تفعيل طبقات الأمان...",
                    ["FinalizingSecurityEnvironment"] = "إنهاء البيئة الأمنية...",
                    ["SystemReadyLaunchingInterface"] = "النظام جاهز - تشغيل الواجهة...",

                    // Settings Window
                    ["LanguageSettings"] = "اللغة / Language",
                    ["InterfaceLanguage"] = "لغة الواجهة:",
                    ["LanguageRestartNote"] = "تغييرات اللغة ستصبح فعالة بعد إعادة التشغيل",
                    ["NetworkSettings"] = "إعدادات الشبكة",
                    ["DiscoveryPort"] = "منفذ الاكتشاف:",
                    ["EnableAutoDiscovery"] = "تفعيل اكتشاف المستخدمين التلقائي",
                    ["BroadcastPresence"] = "بث حضوري على الشبكة",
                    ["PrinterSharing"] = "مشاركة الطابعات",
                    ["EnablePrinterSharing"] = "تفعيل مشاركة الطابعات",
                    ["AvailablePrinters"] = "الطابعات المتاحة:",
                    ["AuthorizedUsers"] = "المستخدمون المخولون:",
                    ["SecuritySettings"] = "إعدادات الأمان",
                    ["EncryptMessages"] = "تشفير جميع الرسائل (AES-256)",
                    ["LogActivity"] = "تسجيل نشاط المستخدم",
                    ["ReportShortcut"] = "اختصار الإبلاغ عن مشكلة:",
                    ["DeviceInformation"] = "معلومات الجهاز",
                    ["DeviceID"] = "معرف الجهاز:",
                    ["MachineName"] = "اسم الجهاز:",
                    ["OSVersion"] = "إصدار النظام:",
                    ["IPAddress"] = "عنوان IP:",

                    // Dialog Messages
                    ["FactoryResetQuestion"] = "هل تريد إعادة ضبط المصنع؟",
                    ["FactoryResetDetails"] = "تفاصيل:",
                    ["FactoryResetExplanation"] = "• سيتم حذف جميع البيانات والإعدادات\n• سيتم إعادة تعيين جميع الخيارات للافتراضية\n• سيتم حذف جميع الملفات المحفوظة\n• سيصبح البرنامج كما لو تم تثبيته للمرة الأولى\n\n⚠️ هذا الإجراء لا يمكن التراجع عنه!",
                    ["ClearDataQuestion"] = "هل تريد مسح البيانات المؤقتة؟",
                    ["ClearDataExplanation"] = "• سيتم حذف معلومات تسجيل الدخول المحفوظة\n• سيتم مسح الرسائل المؤقتة\n• سيتم حذف ملفات التخزين المؤقت\n• ستحتاج لإعادة تسجيل الدخول",
                    ["Clear"] = "مسح",
                    ["Yes"] = "نعم",
                    ["No"] = "لا",

                    // Exit Confirmation
                    ["ConfirmExit"] = "تأكيد الإغلاق",
                    ["ConfirmExitTitle"] = "SafeLink - تأكيد الإغلاق",
                    ["ExitQuestion"] = "هل تريد إغلاق التطبيق؟",
                    ["ExitMessage"] = "هل تريد إغلاق التطبيق؟",

                    // Report Window
                    ["SendReport"] = "SafeLink - إرسال تقرير",
                    ["ReportHeader"] = "إرسال تقرير",
                    ["ReportSubHeader"] = "اختر نوع التقرير المطلوب إرساله",
                    ["ScreenshotReport"] = "رسالة مع صورة الشاشة",
                    ["ScreenshotReportDesc"] = "إرسال لقطة شاشة مع رسالة وصفية",
                    ["SystemStatusReport"] = "تقرير حالة النظام",
                    ["SystemStatusReportDesc"] = "إرسال معلومات تقنية عن حالة النظام",
                    ["CustomMessageReport"] = "رسالة مخصصة",
                    ["CustomMessageReportDesc"] = "كتابة رسالة مخصصة بالتفصيل المطلوب",
                    ["CustomReportAdvanced"] = "تقرير مخصص متقدم",
                    ["CustomReportAdvancedDesc"] = "تقرير شامل مع إمكانية إضافة تفاصيل مخصصة",
                    ["WriteCustomDetails"] = "اكتب التفاصيل المخصصة:",
                    ["ReportSent"] = "تم إرسال التقرير بنجاح",
                    ["ReportFailed"] = "فشل في إرسال التقرير"
                }
            };
        }

        public static string GetString(string key)
        {
            if (_translations.ContainsKey(_currentLanguage) &&
                _translations[_currentLanguage].ContainsKey(key))
            {
                return _translations[_currentLanguage][key];
            }

            // Fallback to English if key not found
            if (_translations.ContainsKey("en") &&
                _translations["en"].ContainsKey(key))
            {
                return _translations["en"][key];
            }

            return key; // Return key itself if not found
        }

        public static void SetLanguage(string languageCode)
        {
            if (_translations.ContainsKey(languageCode))
            {
                _currentLanguage = languageCode;

                // Set culture for right-to-left languages
                if (languageCode == "ar")
                {
                    var culture = new CultureInfo("ar-SA");
                    CultureInfo.DefaultThreadCurrentCulture = culture;
                    CultureInfo.DefaultThreadCurrentUICulture = culture;

                    // Set flow direction for Arabic if MainWindow exists
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.FlowDirection = FlowDirection.RightToLeft;
                    }
                }
                else
                {
                    var culture = new CultureInfo("en-US");
                    CultureInfo.DefaultThreadCurrentCulture = culture;
                    CultureInfo.DefaultThreadCurrentUICulture = culture;

                    // Set flow direction for English if MainWindow exists
                    if (Application.Current.MainWindow != null)
                    {
                        Application.Current.MainWindow.FlowDirection = FlowDirection.LeftToRight;
                    }
                }

                // Notify all windows to update their text
                LanguageChanged?.Invoke();
            }
        }

        public static string CurrentLanguage => _currentLanguage;

        public static event Action LanguageChanged;
    }
}
