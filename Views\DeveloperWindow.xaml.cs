using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SafeLink.Core;
using SafeLink.Models;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Text;
using System.IO;

namespace SafeLink.Views
{
    public partial class DeveloperWindow : Window
    {
        private DatabaseManager _databaseManager;
        private NetworkManager _networkManager;
        private List<User> _allUsers;
        private List<ProblemReport> _problemReports;
        private List<RemoteDevice> _remoteDevices;

        public DeveloperWindow()
        {
            InitializeComponent();
            InitializeServices();
            LoadInitialData();
        }

        private void InitializeServices()
        {
            _databaseManager = App.GetDatabaseManager();
            _networkManager = App.GetNetworkManager();
            _allUsers = new List<User>();
            _problemReports = new List<ProblemReport>();
            _remoteDevices = new List<RemoteDevice>();
        }

        private async void LoadInitialData()
        {
            await RefreshUsers();
            await RefreshSystemInfo();
            await RefreshReports();
            LoadTargetDevices();
        }

        private async Task RefreshUsers()
        {
            try
            {
                // This would be implemented in DatabaseManager
                // For now, create sample data
                _allUsers = new List<User>
                {
                    new User
                    {
                        Id = 1,
                        Username = "Drikon",
                        Role = UserRole.Developer,
                        Status = UserStatus.Online,
                        LastLogin = DateTime.Now
                    }
                };

                UsersDataGrid.ItemsSource = _allUsers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load users: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RefreshSystemInfo()
        {
            try
            {
                var systemInfo = DeviceManager.GetSystemInfo();

                // Update stats
                ConnectedUsersText.Text = "1"; // This would come from NetworkManager
                UptimeText.Text = FormatUptime(Environment.TickCount);
                MemoryUsageText.Text = FormatBytes(GC.GetTotalMemory(false));
                CpuUsageText.Text = "Normal";

                // Update system details
                SystemDetailsPanel.Children.Clear();

                AddSystemInfoSection("Operating System", new Dictionary<string, string>
                {
                    ["Platform"] = systemInfo.OSPlatform,
                    ["Version"] = systemInfo.WindowsVersion,
                    ["Build"] = systemInfo.WindowsBuild,
                    ["Machine Name"] = systemInfo.MachineName
                });

                AddSystemInfoSection("Hardware", new Dictionary<string, string>
                {
                    ["Processor"] = systemInfo.ProcessorName,
                    ["Cores"] = systemInfo.ProcessorCount.ToString(),
                    ["Total Memory"] = FormatBytes(systemInfo.TotalMemory)
                });

                AddSystemInfoSection("Network", new Dictionary<string, string>
                {
                    ["IP Address"] = DeviceManager.GetLocalIPAddress(),
                    ["Adapters"] = systemInfo.NetworkAdapters.Count.ToString()
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load system info: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddSystemInfoSection(string title, Dictionary<string, string> items)
        {
            var groupBox = new GroupBox
            {
                Header = title,
                Style = (Style)Resources["AtariGroupBoxStyle"],
                Margin = new Thickness(0, 0, 0, 10)
            };

            var stackPanel = new StackPanel { Margin = new Thickness(10) };

            foreach (var item in items)
            {
                var grid = new Grid();
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(150) });
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

                var label = new TextBlock
                {
                    Text = $"{item.Key}:",
                    Foreground = System.Windows.Media.Brushes.Cyan,
                    FontWeight = FontWeights.Bold,
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetColumn(label, 0);

                var value = new TextBlock
                {
                    Text = item.Value,
                    Foreground = System.Windows.Media.Brushes.LightGreen,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(10, 0, 0, 0)
                };
                Grid.SetColumn(value, 1);

                grid.Children.Add(label);
                grid.Children.Add(value);
                stackPanel.Children.Add(grid);
            }

            groupBox.Content = stackPanel;
            SystemDetailsPanel.Children.Add(groupBox);
        }

        private async Task RefreshReports()
        {
            try
            {
                // This would load from database
                _problemReports = new List<ProblemReport>();
                // ReportsDataGrid.ItemsSource = _problemReports; // Removed - not in simplified UI
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load reports: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadTargetDevices()
        {
            // Removed - not in simplified UI
            // TargetDeviceComboBox.Items.Clear();
            // CommandTypeComboBox.SelectedIndex = 0;
        }

        private string FormatUptime(int milliseconds)
        {
            var timeSpan = TimeSpan.FromMilliseconds(milliseconds);
            return $"{timeSpan.Days}d {timeSpan.Hours}h {timeSpan.Minutes}m";
        }

        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        // Event Handlers
        private async void RefreshUsersButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshUsers();
        }

        private async void CreateUserButton_Click(object sender, RoutedEventArgs e)
        {
            await CreateUserDialogAsync();
        }

        private async void ResetDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int userId)
            {
                await ResetUserDeviceAsync(userId);
            }
        }

        private async void DeleteUserButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int userId)
            {
                await DeleteUserWithConfirmationAsync(userId);
            }
        }

        private async void RefreshSystemButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshSystemInfo();
        }

        private async void CheckFirewallButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CommandOutputTextBlock.Text += "\n🔥 Checking firewall rules...\n";

                var status = await FirewallManager.GetFirewallStatusAsync();
                CommandOutputTextBlock.Text += status + "\n";

                CommandOutputTextBlock.Text += "✅ Firewall check completed\n";
            }
            catch (Exception ex)
            {
                CommandOutputTextBlock.Text += $"❌ Error checking firewall: {ex.Message}\n";
            }
        }

        private async void AddFirewallRulesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                CommandOutputTextBlock.Text += "\n🔥 Adding firewall rules (Inbound + Outbound)...\n";

                var success = await FirewallManager.AddFirewallRulesAsync();

                if (success)
                {
                    CommandOutputTextBlock.Text += "✅ All firewall rules added successfully\n";
                }
                else
                {
                    CommandOutputTextBlock.Text += "⚠️ Some firewall rules failed to add\n";
                }

                // عرض الحالة الحديثة
                var status = await FirewallManager.GetFirewallStatusAsync();
                CommandOutputTextBlock.Text += status + "\n";
            }
            catch (Exception ex)
            {
                CommandOutputTextBlock.Text += $"❌ Error adding firewall rules: {ex.Message}\n";
            }
        }

        private async void ExecuteCommandButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var command = CommandTextBox.Text?.Trim();
                var commandType = (CommandTypeComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();

                if (string.IsNullOrEmpty(command) || string.IsNullOrEmpty(commandType))
                {
                    MessageBox.Show("Please enter a command and select command type", "Warning",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                await ExecuteRemoteCommand(command, commandType);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error executing command: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearOutputButton_Click(object sender, RoutedEventArgs e)
        {
            // Show delete confirmation dialog with detailed explanation
            bool shouldClear = UnifiedMessageDialog.ShowDialog(this, MessageDialogType.DeleteData,
                "SafeLink Developer",
                "هل تريد مسح جميع سجلات الإخراج؟",
                "• سيتم حذف جميع سجلات الأوامر\n• سيتم مسح تاريخ العمليات\n• ستفقد جميع المعلومات المسجلة");

            if (shouldClear)
            {
                // Clear output logs here
                if (CommandOutputTextBlock != null)
                {
                    CommandOutputTextBlock.Text = "Command output will appear here...";
                }
                System.Diagnostics.Debug.WriteLine("✅ Output logs cleared by developer");

                // Show success message
                CustomMessageBox.Show("تم مسح السجلات بنجاح", "SafeLink Developer",
                    CustomMessageBox.MessageBoxType.Information,
                    CustomMessageBox.MessageBoxButtons.OK, this);
            }
        }

        private async void RefreshReportsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (ReportsDataGrid != null)
                {
                    // TODO: Load problem reports from database
                    _problemReports.Clear();

                    // Sample data for testing
                    _problemReports.Add(new ProblemReport
                    {
                        Id = 1,
                        UserId = 1,
                        Description = "Connection Issue - User unable to connect to network",
                        Status = ReportStatus.Pending,
                        Timestamp = DateTime.Now.AddHours(-2)
                    });

                    ReportsDataGrid.ItemsSource = null;
                    ReportsDataGrid.ItemsSource = _problemReports;
                }
                else
                {
                    await RefreshReports();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing reports: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ViewReportButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int reportId)
            {
                // TODO: Implement report viewing
                MessageBox.Show($"Viewing report {reportId}", "Info",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ResolveReportButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int reportId)
            {
                var result = MessageBox.Show(
                    "Mark this report as resolved?",
                    "Confirm Resolution",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // TODO: Implement report resolution
                        MessageBox.Show("Report marked as resolved", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to resolve report: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void SendUpdateCommand_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "🔄 إرسال أمر التحديث لجميع الأجهزة\n\n" +
                "سيتم إرسال أمر التحديث عبر الشبكة المحلية لجميع الأجهزة المتصلة.\n" +
                "تأكد من وجود ملفات التحديث في مجلد Update.\n\n" +
                "هل تريد المتابعة؟",
                "إرسال أمر التحديث",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // إرسال أمر التحديث لجميع الأجهزة
                    await UpdateManager.SendUpdateCommandAsync("ALL_DEVICES");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"❌ فشل في إرسال أمر التحديث:\n{ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        private async void CreateUpdatePackage_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "📦 إنشاء حزمة تحديث جديدة\n\n" +
                "سيتم إنشاء ملف version.json في مجلد Update.\n" +
                "تأكد من وضع ملفات البرنامج المحدثة في مجلد Update.\n\n" +
                "هل تريد المتابعة؟",
                "إنشاء حزمة التحديث",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await UpdateManager.CreateUpdatePackageAsync();
            }
        }

        // New Remote Control Functions
        private void RefreshDevicesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TargetDeviceComboBox.Items.Clear();

                // Get connected devices from NetworkManager
                var connectedUsers = _networkManager?.GetConnectedUsers() ?? new List<User>();

                foreach (var user in connectedUsers)
                {
                    var deviceInfo = $"{user.Username} ({user.DeviceId?.Substring(0, 8)}...)";
                    var item = new ComboBoxItem
                    {
                        Content = deviceInfo,
                        Tag = user
                    };
                    TargetDeviceComboBox.Items.Add(item);
                }

                if (TargetDeviceComboBox.Items.Count > 0)
                {
                    TargetDeviceComboBox.SelectedIndex = 0;
                }

                System.Diagnostics.Debug.WriteLine($"✅ Refreshed devices: {TargetDeviceComboBox.Items.Count} found");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to refresh devices: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ExecuteRemoteCommand(string command, string commandType)
        {
            try
            {
                if (TargetDeviceComboBox.SelectedItem is ComboBoxItem selectedItem &&
                    selectedItem.Tag is User targetUser)
                {
                    CommandOutputTextBlock.Text += $"\n[{DateTime.Now:HH:mm:ss}] Executing {commandType} on {targetUser.Username}:\n";
                    CommandOutputTextBlock.Text += $"Command: {command}\n";
                    CommandOutputTextBlock.Text += "Sending command...\n";

                    // Send command via NetworkManager
                    var success = await _networkManager.SendRemoteCommandAsync(targetUser.DeviceId, commandType, command);

                    if (success)
                    {
                        CommandOutputTextBlock.Text += "✅ Command sent successfully\n";
                        CommandOutputTextBlock.Text += "Waiting for response...\n";
                    }
                    else
                    {
                        CommandOutputTextBlock.Text += "❌ Failed to send command\n";
                    }
                }
                else
                {
                    CommandOutputTextBlock.Text += "\n❌ No device selected\n";
                }
            }
            catch (Exception ex)
            {
                CommandOutputTextBlock.Text += $"\n❌ Error: {ex.Message}\n";
            }
        }

        // Duplicate methods removed - using original implementations above

        // Enhanced User Management Functions
        private async Task CreateUserDialogAsync()
        {
            try
            {
                var dialog = new CreateUserDialog();
                dialog.Owner = this;

                if (dialog.ShowDialog() == true)
                {
                    var newUser = new User
                    {
                        Username = dialog.Username,
                        ArabicUsername = dialog.ArabicUsername,
                        Role = dialog.SelectedRole,
                        DeviceId = DeviceManager.GenerateDeviceId(),
                        Status = UserStatus.Offline,
                        CreatedDate = DateTime.Now
                    };

                    var success = await _databaseManager.CreateUserAsync(newUser, dialog.Password);

                    if (success)
                    {
                        await RefreshUsers();
                        MessageBox.Show("User created successfully", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("Failed to create user", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating user: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task DeleteUserWithConfirmationAsync(int userId)
        {
            try
            {
                var user = _allUsers.FirstOrDefault(u => u.Id == userId);
                if (user == null) return;

                var result = MessageBox.Show(
                    $"⚠️ Delete User: {user.Username}\n\n" +
                    "This will permanently delete:\n" +
                    "• User account and credentials\n" +
                    "• All chat history\n" +
                    "• Device authorization\n" +
                    "• User settings and preferences\n\n" +
                    "This action cannot be undone!\n\n" +
                    "Are you sure you want to continue?",
                    "Confirm User Deletion",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    var success = await _databaseManager.DeleteUserAsync(userId);

                    if (success)
                    {
                        await RefreshUsers();
                        MessageBox.Show("User deleted successfully", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("Failed to delete user", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error deleting user: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ResetUserDeviceAsync(int userId)
        {
            try
            {
                var user = _allUsers.FirstOrDefault(u => u.Id == userId);
                if (user == null) return;

                var result = MessageBox.Show(
                    $"🔄 Reset Device Key for: {user.Username}\n\n" +
                    "This will:\n" +
                    "• Generate a new device key\n" +
                    "• Force user to re-authenticate\n" +
                    "• Disconnect user from current session\n\n" +
                    "Continue?",
                    "Confirm Device Reset",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var newDeviceId = DeviceManager.GenerateDeviceId();
                    var success = await _databaseManager.UpdateUserDeviceIdAsync(userId, newDeviceId);

                    if (success)
                    {
                        // Disconnect user if online
                        await _networkManager.DisconnectUserAsync(user.DeviceId);

                        await RefreshUsers();
                        MessageBox.Show("Device key reset successfully", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("Failed to reset device key", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error resetting device: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
