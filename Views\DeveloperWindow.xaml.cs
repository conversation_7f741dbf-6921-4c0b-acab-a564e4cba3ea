using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SafeLink.Core;
using SafeLink.Models;
using System.Threading.Tasks;

namespace SafeLink.Views
{
    public partial class DeveloperWindow : Window
    {
        private DatabaseManager _databaseManager;
        private NetworkManager _networkManager;
        private List<User> _allUsers;
        private List<ProblemReport> _problemReports;

        public DeveloperWindow()
        {
            InitializeComponent();
            InitializeServices();
            LoadInitialData();
        }

        private void InitializeServices()
        {
            _databaseManager = App.GetDatabaseManager();
            _networkManager = App.GetNetworkManager();
            _allUsers = new List<User>();
            _problemReports = new List<ProblemReport>();
        }

        private async void LoadInitialData()
        {
            await RefreshUsers();
            await RefreshSystemInfo();
            await RefreshReports();
            LoadTargetDevices();
        }

        private async Task RefreshUsers()
        {
            try
            {
                // This would be implemented in DatabaseManager
                // For now, create sample data
                _allUsers = new List<User>
                {
                    new User
                    {
                        Id = 1,
                        Username = "Drikon",
                        Role = UserRole.Developer,
                        Status = UserStatus.Online,
                        LastLogin = DateTime.Now
                    }
                };

                UsersDataGrid.ItemsSource = _allUsers;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load users: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task RefreshSystemInfo()
        {
            try
            {
                var systemInfo = DeviceManager.GetSystemInfo();

                // Update stats
                ConnectedUsersText.Text = "1"; // This would come from NetworkManager
                UptimeText.Text = FormatUptime(Environment.TickCount);
                MemoryUsageText.Text = FormatBytes(GC.GetTotalMemory(false));
                CpuUsageText.Text = "Normal";

                // Update system details
                SystemDetailsPanel.Children.Clear();

                AddSystemInfoSection("Operating System", new Dictionary<string, string>
                {
                    ["Platform"] = systemInfo.OSPlatform,
                    ["Version"] = systemInfo.WindowsVersion,
                    ["Build"] = systemInfo.WindowsBuild,
                    ["Machine Name"] = systemInfo.MachineName
                });

                AddSystemInfoSection("Hardware", new Dictionary<string, string>
                {
                    ["Processor"] = systemInfo.ProcessorName,
                    ["Cores"] = systemInfo.ProcessorCount.ToString(),
                    ["Total Memory"] = FormatBytes(systemInfo.TotalMemory)
                });

                AddSystemInfoSection("Network", new Dictionary<string, string>
                {
                    ["IP Address"] = DeviceManager.GetLocalIPAddress(),
                    ["Adapters"] = systemInfo.NetworkAdapters.Count.ToString()
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load system info: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddSystemInfoSection(string title, Dictionary<string, string> items)
        {
            var groupBox = new GroupBox
            {
                Header = title,
                Style = (Style)Resources["AtariGroupBoxStyle"],
                Margin = new Thickness(0, 0, 0, 10)
            };

            var stackPanel = new StackPanel { Margin = new Thickness(10) };

            foreach (var item in items)
            {
                var grid = new Grid();
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(150) });
                grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });

                var label = new TextBlock
                {
                    Text = $"{item.Key}:",
                    Foreground = System.Windows.Media.Brushes.Cyan,
                    FontWeight = FontWeights.Bold,
                    VerticalAlignment = VerticalAlignment.Center
                };
                Grid.SetColumn(label, 0);

                var value = new TextBlock
                {
                    Text = item.Value,
                    Foreground = System.Windows.Media.Brushes.LightGreen,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(10, 0, 0, 0)
                };
                Grid.SetColumn(value, 1);

                grid.Children.Add(label);
                grid.Children.Add(value);
                stackPanel.Children.Add(grid);
            }

            groupBox.Content = stackPanel;
            SystemDetailsPanel.Children.Add(groupBox);
        }

        private async Task RefreshReports()
        {
            try
            {
                // This would load from database
                _problemReports = new List<ProblemReport>();
                // ReportsDataGrid.ItemsSource = _problemReports; // Removed - not in simplified UI
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Failed to load reports: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadTargetDevices()
        {
            // Removed - not in simplified UI
            // TargetDeviceComboBox.Items.Clear();
            // CommandTypeComboBox.SelectedIndex = 0;
        }

        private string FormatUptime(int milliseconds)
        {
            var timeSpan = TimeSpan.FromMilliseconds(milliseconds);
            return $"{timeSpan.Days}d {timeSpan.Hours}h {timeSpan.Minutes}m";
        }

        private string FormatBytes(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        // Event Handlers
        private async void RefreshUsersButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshUsers();
        }

        private void CreateUserButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement user creation dialog
            MessageBox.Show("User creation feature coming soon", "Info",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ResetDeviceButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int userId)
            {
                var result = MessageBox.Show(
                    "Are you sure you want to reset this user's device key?",
                    "Confirm Reset",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // TODO: Implement device reset
                        MessageBox.Show("Device key reset successfully", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to reset device: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private void DeleteUserButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int userId)
            {
                var result = MessageBox.Show(
                    "Are you sure you want to delete this user?",
                    "Confirm Delete",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // TODO: Implement user deletion
                        MessageBox.Show("User deleted successfully", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to delete user: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void RefreshSystemButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshSystemInfo();
        }

        private void ExecuteCommandButton_Click(object sender, RoutedEventArgs e)
        {
            // Remote command execution not available in simplified UI
            MessageBox.Show("Remote command execution feature coming soon", "Info",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ClearOutputButton_Click(object sender, RoutedEventArgs e)
        {
            // Show delete confirmation dialog with detailed explanation
            bool shouldClear = UnifiedMessageDialog.ShowDialog(this, MessageDialogType.DeleteData,
                "SafeLink Developer",
                "هل تريد مسح جميع سجلات الإخراج؟",
                "• سيتم حذف جميع سجلات الأوامر\n• سيتم مسح تاريخ العمليات\n• ستفقد جميع المعلومات المسجلة");

            if (shouldClear)
            {
                // Clear output logs here
                System.Diagnostics.Debug.WriteLine("✅ Output logs cleared by developer");

                // Show success message
                CustomMessageBox.Show("تم مسح السجلات بنجاح", "SafeLink Developer",
                    CustomMessageBox.MessageBoxType.Information,
                    CustomMessageBox.MessageBoxButtons.OK, this);
            }
        }

        private async void RefreshReportsButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshReports();
        }

        private void ViewReportButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int reportId)
            {
                // TODO: Implement report viewing
                MessageBox.Show($"Viewing report {reportId}", "Info",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ResolveReportButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is int reportId)
            {
                var result = MessageBox.Show(
                    "Mark this report as resolved?",
                    "Confirm Resolution",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // TODO: Implement report resolution
                        MessageBox.Show("Report marked as resolved", "Success",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Failed to resolve report: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private async void SendUpdateCommand_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "🔄 إرسال أمر التحديث لجميع الأجهزة\n\n" +
                "سيتم إرسال أمر التحديث عبر الشبكة المحلية لجميع الأجهزة المتصلة.\n" +
                "تأكد من وجود ملفات التحديث في مجلد Update.\n\n" +
                "هل تريد المتابعة؟",
                "إرسال أمر التحديث",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // إرسال أمر التحديث لجميع الأجهزة
                    await UpdateManager.SendUpdateCommandAsync("ALL_DEVICES");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(
                        $"❌ فشل في إرسال أمر التحديث:\n{ex.Message}",
                        "خطأ",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            }
        }

        private async void CreateUpdatePackage_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "📦 إنشاء حزمة تحديث جديدة\n\n" +
                "سيتم إنشاء ملف version.json في مجلد Update.\n" +
                "تأكد من وضع ملفات البرنامج المحدثة في مجلد Update.\n\n" +
                "هل تريد المتابعة؟",
                "إنشاء حزمة التحديث",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await UpdateManager.CreateUpdatePackageAsync();
            }
        }
    }
}
