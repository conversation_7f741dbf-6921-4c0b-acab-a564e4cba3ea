# 🚀 SafeLink - نسخة الاختبار المشتركة

## 📋 معلومات النسخة
- **التاريخ:** 2025-01-06
- **الإصدار:** Release Build
- **الميزات:** جدار الحماية المحسن + تشخيص متقدم

---

## 🎯 كيفية التشغيل

### **الطريقة الأولى (مبسطة):**
```
🖱️ انقر نقراً مزدوجاً على: تشغيل_SafeLink.bat
```

### **الطريقة الثانية (مباشرة):**
```
🖱️ انقر نقراً مزدوجاً على: SafeLink.exe
```

---

## 🔧 متطلبات التشغيل

### **✅ مطلوب:**
- **Windows 10/11** (مُوصى به)
- **.NET 8.0 Runtime** (سيتم التحقق تلقائياً)
- **صلاحيات المدير** (لإضافة قواعد جدار الحماية)

### **🌐 الشبكة:**
- **نفس الشبكة المحلية** (WiFi أو Ethernet)
- **جدار الحماية** سيطلب إذن تلقائياً

---

## 🧪 خطوات الاختبار

### **1️⃣ على الجهاز الأول:**
```
🚀 شغل SafeLink
🔐 سجل دخول (أي اسم مستخدم وكلمة مرور)
⏳ انتظر 10-15 ثانية
👀 راقب قائمة المستخدمين
```

### **2️⃣ على الجهاز الثاني:**
```
🚀 شغل SafeLink
🔐 سجل دخول (اسم مستخدم مختلف)
⏳ انتظر 10-15 ثانية
👀 يجب أن ترى المستخدم الأول
```

### **3️⃣ اختبار الرسائل:**
```
💬 اكتب رسالة
📤 اضغط إرسال
✅ يجب أن تصل للجهاز الآخر
```

---

## 🔍 أدوات التشخيص

### **فحص جدار الحماية:**
```
🖱️ انقر نقراً مزدوجاً على: SimpleFirewallTest.ps1
```

### **لوحة تحكم المطور:**
```
⌨️ في SafeLink اضغط: Ctrl + Shift + D
🔥 اختبر: Check Firewall
🔥 اختبر: Add Firewall Rules
```

---

## 🐛 إذا لم يعمل

### **❌ مشاكل شائعة:**

1. **لا يظهر المستخدمون:**
   - تأكد من نفس الشبكة
   - شغل SimpleFirewallTest.ps1
   - تحقق من جدار الحماية

2. **خطأ في التشغيل:**
   - تأكد من .NET 8.0
   - شغل كمدير
   - تحقق من مكافح الفيروسات

3. **مشاكل الشبكة:**
   - تحقق من WiFi/Ethernet
   - جرب إعادة تشغيل الراوتر
   - تأكد من عدم حجب البورتات

---

## 📊 معلومات تقنية

### **البورتات المستخدمة:**
- **UDP 8888-8938:** اكتشاف المستخدمين
- **TCP 8889-8939:** الرسائل المباشرة

### **قواعد جدار الحماية:**
- **SafeLink_UDP_Inbound:** استقبال UDP
- **SafeLink_UDP_Outbound:** إرسال UDP
- **SafeLink_TCP_Inbound:** استقبال TCP
- **SafeLink_TCP_Outbound:** إرسال TCP

---

## 📝 تسجيل الأخطاء

### **إذا واجهت مشاكل:**
1. **التقط لقطة شاشة** للخطأ
2. **شغل SimpleFirewallTest.ps1** وانسخ النتائج
3. **افتح لوحة المطور** (Ctrl+Shift+D) وانسخ الإخراج
4. **أرسل جميع المعلومات** للمطور

---

## ✅ النجاح المتوقع

### **يجب أن ترى:**
- **🔐 نافذة تسجيل دخول** عند البدء
- **💬 واجهة الدردشة** بعد تسجيل الدخول
- **👥 قائمة المستخدمين** تظهر المستخدمين الآخرين
- **📡 رسائل تصل وتُرسل** بنجاح

---

**🎉 حظاً موفقاً في الاختبار! 🚀✨**
