﻿#pragma checksum "..\..\..\..\Views\UnifiedMessageDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0ACF4F3EC65E900CDD9BBFF5033588D8E56335E1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Views {
    
    
    /// <summary>
    /// UnifiedMessageDialog
    /// </summary>
    public partial class UnifiedMessageDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 36 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.ShieldIcon MessageShield;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MainQuestionText;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExplanationTitle;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExplanationText;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubMessageText;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button YesButton;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon YesIcon;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YesText;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NoButton;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon NoIcon;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/views/unifiedmessagedialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MessageShield = ((SafeLink.Controls.ShieldIcon)(target));
            return;
            case 2:
            this.MainQuestionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ExplanationTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.ExplanationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.SubMessageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.YesButton = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
            this.YesButton.Click += new System.Windows.RoutedEventHandler(this.YesButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.YesIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 8:
            this.YesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.NoButton = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\..\Views\UnifiedMessageDialog.xaml"
            this.NoButton.Click += new System.Windows.RoutedEventHandler(this.NoButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.NoIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 11:
            this.NoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

