using System;
using System.IO;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace SafeLink.Utils
{
    public static class IconManager
    {
        private static readonly string IconsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Icons");

        public static ImageSource GetIcon(string iconName)
        {
            try
            {
                var iconPath = Path.Combine(IconsPath, $"{iconName}.svg");
                
                if (File.Exists(iconPath))
                {
                    return LoadSvgIcon(iconPath);
                }
                
                // Fallback to embedded resources or default icons
                return CreateDefaultIcon(iconName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load icon {iconName}: {ex.Message}");
                return CreateDefaultIcon(iconName);
            }
        }

        private static ImageSource LoadSvgIcon(string svgPath)
        {
            try
            {
                // For now, we'll create a simple colored rectangle as placeholder
                // In a full implementation, you'd use a proper SVG rendering library
                var drawingGroup = new DrawingGroup();
                var geometryDrawing = new GeometryDrawing();
                
                // Create a simple rectangle geometry
                geometryDrawing.Geometry = new RectangleGeometry(new Rect(0, 0, 24, 24));
                geometryDrawing.Brush = new SolidColorBrush(Color.FromRgb(0, 212, 170)); // SafeLink green
                
                drawingGroup.Children.Add(geometryDrawing);
                
                return new DrawingImage(drawingGroup);
            }
            catch
            {
                return CreateDefaultIcon("default");
            }
        }

        private static ImageSource CreateDefaultIcon(string iconName)
        {
            var drawingGroup = new DrawingGroup();
            var geometryDrawing = new GeometryDrawing();
            
            // Create different shapes based on icon name
            Geometry geometry = iconName.ToLower() switch
            {
                "users" => CreateUsersGeometry(),
                "chat" => CreateChatGeometry(),
                "files" => CreateFilesGeometry(),
                "printer" => CreatePrinterGeometry(),
                "settings" => CreateSettingsGeometry(),
                "developer" => CreateDeveloperGeometry(),
                "send" => CreateSendGeometry(),
                "attach" => CreateAttachGeometry(),
                "eye" => CreateEyeGeometry(),
                "eye-off" => CreateEyeOffGeometry(),
                "lock" => CreateLockGeometry(),
                "unlock" => CreateUnlockGeometry(),
                "network" => CreateNetworkGeometry(),
                "warning" => CreateWarningGeometry(),
                "diagnostic" => CreateDiagnosticGeometry(),
                "minimize" => CreateMinimizeGeometry(),
                "maximize" => CreateMaximizeGeometry(),
                "close" => CreateCloseGeometry(),
                "logout" => CreateLogoutGeometry(),
                "user" => CreateUserGeometry(),
                _ => new RectangleGeometry(new Rect(2, 2, 20, 20))
            };
            
            geometryDrawing.Geometry = geometry;
            geometryDrawing.Brush = new SolidColorBrush(Colors.White);
            geometryDrawing.Pen = new Pen(new SolidColorBrush(Color.FromRgb(0, 212, 170)), 1.5);
            
            drawingGroup.Children.Add(geometryDrawing);
            
            return new DrawingImage(drawingGroup);
        }

        private static Geometry CreateUsersGeometry()
        {
            var group = new GeometryGroup();
            // User 1
            group.Children.Add(new EllipseGeometry(new Point(8, 8), 3, 3));
            group.Children.Add(new RectangleGeometry(new Rect(5, 14, 6, 8)));
            // User 2
            group.Children.Add(new EllipseGeometry(new Point(16, 8), 3, 3));
            group.Children.Add(new RectangleGeometry(new Rect(13, 14, 6, 8)));
            return group;
        }

        private static Geometry CreateChatGeometry()
        {
            return new RectangleGeometry(new Rect(2, 6, 16, 10), 2, 2);
        }

        private static Geometry CreateFilesGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new RectangleGeometry(new Rect(4, 2, 12, 16), 1, 1));
            group.Children.Add(new RectangleGeometry(new Rect(6, 6, 8, 1)));
            group.Children.Add(new RectangleGeometry(new Rect(6, 9, 8, 1)));
            group.Children.Add(new RectangleGeometry(new Rect(6, 12, 6, 1)));
            return group;
        }

        private static Geometry CreatePrinterGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new RectangleGeometry(new Rect(2, 8, 20, 8), 2, 2));
            group.Children.Add(new RectangleGeometry(new Rect(6, 4, 12, 4)));
            group.Children.Add(new RectangleGeometry(new Rect(6, 16, 12, 4)));
            return group;
        }

        private static Geometry CreateSettingsGeometry()
        {
            return new EllipseGeometry(new Point(12, 12), 8, 8);
        }

        private static Geometry CreateDeveloperGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new RectangleGeometry(new Rect(2, 2, 20, 20), 2, 2));
            group.Children.Add(new RectangleGeometry(new Rect(6, 8, 4, 1)));
            group.Children.Add(new RectangleGeometry(new Rect(14, 8, 4, 1)));
            group.Children.Add(new RectangleGeometry(new Rect(6, 12, 12, 1)));
            return group;
        }

        private static Geometry CreateSendGeometry()
        {
            var pathGeometry = new PathGeometry();
            var figure = new PathFigure { StartPoint = new Point(2, 12) };
            figure.Segments.Add(new LineSegment(new Point(22, 2), true));
            figure.Segments.Add(new LineSegment(new Point(11, 13), true));
            figure.Segments.Add(new LineSegment(new Point(22, 22), true));
            figure.IsClosed = true;
            pathGeometry.Figures.Add(figure);
            return pathGeometry;
        }

        private static Geometry CreateAttachGeometry()
        {
            return new EllipseGeometry(new Point(12, 12), 6, 6);
        }

        private static Geometry CreateEyeGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new EllipseGeometry(new Point(12, 12), 10, 6));
            group.Children.Add(new EllipseGeometry(new Point(12, 12), 3, 3));
            return group;
        }

        private static Geometry CreateEyeOffGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new EllipseGeometry(new Point(12, 12), 10, 6));
            group.Children.Add(new LineGeometry(new Point(4, 4), new Point(20, 20)));
            return group;
        }

        private static Geometry CreateLockGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new RectangleGeometry(new Rect(6, 11, 12, 9), 2, 2));
            group.Children.Add(new RectangleGeometry(new Rect(9, 4, 6, 7)));
            return group;
        }

        private static Geometry CreateUnlockGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new RectangleGeometry(new Rect(6, 11, 12, 9), 2, 2));
            group.Children.Add(new RectangleGeometry(new Rect(9, 4, 6, 4)));
            return group;
        }

        private static Geometry CreateNetworkGeometry()
        {
            var group = new GeometryGroup();
            for (int i = 0; i < 3; i++)
            {
                group.Children.Add(new EllipseGeometry(new Point(12, 12), 3 + i * 3, 3 + i * 3));
            }
            return group;
        }

        private static Geometry CreateWarningGeometry()
        {
            var pathGeometry = new PathGeometry();
            var figure = new PathFigure { StartPoint = new Point(12, 2) };
            figure.Segments.Add(new LineSegment(new Point(22, 20), true));
            figure.Segments.Add(new LineSegment(new Point(2, 20), true));
            figure.IsClosed = true;
            pathGeometry.Figures.Add(figure);
            return pathGeometry;
        }

        private static Geometry CreateDiagnosticGeometry()
        {
            return new EllipseGeometry(new Point(12, 12), 8, 8);
        }

        private static Geometry CreateMinimizeGeometry()
        {
            return new RectangleGeometry(new Rect(6, 11, 12, 2));
        }

        private static Geometry CreateMaximizeGeometry()
        {
            return new RectangleGeometry(new Rect(4, 4, 16, 16));
        }

        private static Geometry CreateCloseGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new LineGeometry(new Point(6, 6), new Point(18, 18)));
            group.Children.Add(new LineGeometry(new Point(18, 6), new Point(6, 18)));
            return group;
        }

        private static Geometry CreateLogoutGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new RectangleGeometry(new Rect(3, 3, 12, 18), 2, 2));
            group.Children.Add(new LineGeometry(new Point(15, 12), new Point(21, 12)));
            group.Children.Add(new LineGeometry(new Point(18, 9), new Point(21, 12)));
            group.Children.Add(new LineGeometry(new Point(18, 15), new Point(21, 12)));
            return group;
        }

        private static Geometry CreateUserGeometry()
        {
            var group = new GeometryGroup();
            group.Children.Add(new EllipseGeometry(new Point(12, 8), 4, 4));
            group.Children.Add(new RectangleGeometry(new Rect(6, 16, 12, 6), 3, 3));
            return group;
        }
    }
}
