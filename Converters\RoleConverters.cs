using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using SafeLink.Models;

namespace SafeLink.Converters
{
    public class RoleToColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is UserRole role)
            {
                return role switch
                {
                    UserRole.Developer => Color.FromRgb(0xFF, 0xD7, 0x00), // ذهبي للمطور
                    UserRole.Admin => Color.FromRgb(0xFF, 0x52, 0x52), // أحمر للأدمن
                    UserRole.Moderator => Color.FromRgb(0xFF, 0xB7, 0x4D), // برتقالي للمشرف
                    UserRole.User => Color.FromRgb(0x52, 0x9C, 0xFF), // أزرق للمستخدم
                    _ => Color.FromRgb(0x52, 0x9C, 0xFF)
                };
            }
            return Color.FromRgb(0x52, 0x9C, 0xFF); // أزرق افتراضي
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class RoleToTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is UserRole role)
            {
                return role switch
                {
                    UserRole.Developer => "DEV",
                    UserRole.Admin => "ADMIN",
                    UserRole.Moderator => "MOD",
                    UserRole.User => "USER",
                    _ => "USER"
                };
            }
            return "USER";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
