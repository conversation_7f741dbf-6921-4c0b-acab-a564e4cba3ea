using System;

namespace SafeLink.Models
{
    public class ProblemReport
    {
        public int Id { get; set; }
        public string Username { get; set; }
        public string DeviceId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string ScreenshotPath { get; set; }
        public DateTime ReportDate { get; set; }
        public ReportStatus Status { get; set; }
        public string Resolution { get; set; }
        public DateTime? ResolvedDate { get; set; }
        public ReportPriority Priority { get; set; }
        public string SystemInfo { get; set; }
    }

    public enum ReportStatus
    {
        Open,
        InProgress,
        Resolved,
        Closed
    }

    public enum ReportPriority
    {
        Low,
        Medium,
        High,
        Critical
    }
}
