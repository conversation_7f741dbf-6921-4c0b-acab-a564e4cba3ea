using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using SafeLink.Core;
using SafeLink.Models;

namespace SafeLink.Views
{
    public partial class LoginWindow : Window
    {
        private bool _isPasswordVisible = false;
        private AuthenticationService _authService;
        private string _deviceId;

        public User AuthenticatedUser { get; private set; }
        public bool LoginSuccessful { get; private set; } = false;

        public LoginWindow()
        {
            InitializeComponent();
            InitializeServices();
            LoadDeviceId();

            // Load saved language
            LoadSavedLanguage();

            // Subscribe to language changes
            LanguageManager.LanguageChanged += UpdateLanguage;
            UpdateLanguage(); // Initial language setup

            // Load saved credentials
            LoadSavedCredentials();

            // Set focus to username
            Loaded += (s, e) => UsernameTextBox.Focus();

            // Handle Enter key for login
            KeyDown += LoginWindow_KeyDown;

            // Handle window closing to unsubscribe from events
            Closing += LoginWindow_Closing;
            LanguageManager.LanguageChanged += UpdateLanguage;
        }

        private void InitializeServices()
        {
            _authService = new AuthenticationService();
        }

        private void LoadDeviceId()
        {
            try
            {
                _deviceId = DeviceManager.GetDeviceId();
                System.Diagnostics.Debug.WriteLine($"✅ Device ID generated: {_deviceId}");

                // Format device ID for display: "Device ID: FirstChars..."
                if (!string.IsNullOrEmpty(_deviceId) && _deviceId.Length > 8)
                {
                    var displayId = $"Device ID: {_deviceId.Substring(0, 8)}...";
                    DeviceIdDisplay.Text = displayId;
                    System.Diagnostics.Debug.WriteLine($"✅ Device ID display set: {displayId}");
                }
                else if (!string.IsNullOrEmpty(_deviceId))
                {
                    DeviceIdDisplay.Text = $"Device ID: {_deviceId}";
                    System.Diagnostics.Debug.WriteLine($"✅ Device ID display set (short): Device ID: {_deviceId}");
                }
                else
                {
                    DeviceIdDisplay.Text = "Device ID: Not available";
                    System.Diagnostics.Debug.WriteLine("⚠️ Device ID is empty");
                }
            }
            catch (Exception ex)
            {
                DeviceIdDisplay.Text = "Device ID: Error loading";
                System.Diagnostics.Debug.WriteLine($"❌ Failed to load device ID: {ex.Message}");
            }
        }

        private void LoginWindow_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                LoginButton_Click(sender, e);
            }
            else if (e.Key == Key.Escape)
            {
                Application.Current.Shutdown();
            }
        }

        private void LoginWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            // Unsubscribe from events
            LanguageManager.LanguageChanged -= UpdateLanguage;

            // If this is the main window and no main window is set, shutdown the application
            if (Application.Current.MainWindow == this)
            {
                Application.Current.Shutdown();
            }
        }

        private void TogglePasswordButton_Click(object sender, RoutedEventArgs e)
        {
            _isPasswordVisible = !_isPasswordVisible;

            if (_isPasswordVisible)
            {
                // Show password as text
                PasswordTextBox.Text = PasswordBox.Password;
                PasswordBox.Visibility = Visibility.Collapsed;
                PasswordTextBox.Visibility = Visibility.Visible;
                PasswordTextBox.Focus();
                PasswordTextBox.CaretIndex = PasswordTextBox.Text.Length;

                // Change to eye-off icon and green color
                EyeIcon.IconName = "eye-off";
                EyeIcon.IconColor = new SolidColorBrush(Color.FromRgb(0x00, 0xD4, 0xAA));
            }
            else
            {
                // Hide password
                PasswordBox.Password = PasswordTextBox.Text;
                PasswordTextBox.Visibility = Visibility.Collapsed;
                PasswordBox.Visibility = Visibility.Visible;
                PasswordBox.Focus();

                // Change to eye icon and gray color
                EyeIcon.IconName = "eye";
                EyeIcon.IconColor = new SolidColorBrush(Color.FromRgb(0x7F, 0x8C, 0x8D));
            }
        }

        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LoginButton.IsEnabled = false;
                LoginButtonText.Text = "Authenticating...";

                var username = UsernameTextBox.Text.Trim();
                var password = _isPasswordVisible ? PasswordTextBox.Text : PasswordBox.Password;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    CustomMessageBox.Show(LanguageManager.GetString("FillAllFields"),
                        LanguageManager.GetString("LoginTitle"),
                        CustomMessageBox.MessageBoxType.Warning,
                        CustomMessageBox.MessageBoxButtons.OK, this);
                    return;
                }

                // Authenticate user (device ID check temporarily disabled)
                var user = await _authService.AuthenticateAsync(username, password);

                if (user != null)
                {
                    AuthenticatedUser = user;
                    LoginSuccessful = true;

                    // Save login if remember is checked
                    if (RememberLoginCheckBox.IsChecked == true)
                    {
                        SaveLoginCredentials(username, password);
                    }

                    // Create and show main window
                    var mainWindow = new MainWindow();
                    mainWindow.SetAuthenticatedUser(user);

                    // Set as main window
                    Application.Current.MainWindow = mainWindow;
                    mainWindow.Show();

                    // Close login window
                    this.Close();
                }
                else
                {
                    CustomMessageBox.Show(LanguageManager.GetString("InvalidCredentials"),
                        LanguageManager.GetString("LoginFailed"),
                        CustomMessageBox.MessageBoxType.Error,
                        CustomMessageBox.MessageBoxButtons.OK, this);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"🔴 Login error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"🔴 Stack trace: {ex.StackTrace}");

                CustomMessageBox.Show($"Login error: {ex.Message}", "Error",
                    CustomMessageBox.MessageBoxType.Error,
                    CustomMessageBox.MessageBoxButtons.OK, this);
            }
            finally
            {
                LoginButton.IsEnabled = true;
                LoginButtonText.Text = LanguageManager.GetString("Login");
            }
        }

        private async void RegisterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                RegisterButton.IsEnabled = false;
                RegisterButtonText.Text = "Registering...";

                var username = UsernameTextBox.Text.Trim();
                var password = _isPasswordVisible ? PasswordTextBox.Text : PasswordBox.Password;

                if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                {
                    CustomMessageBox.Show(LanguageManager.GetString("FillAllFields"),
                        LanguageManager.GetString("Register"),
                        CustomMessageBox.MessageBoxType.Warning,
                        CustomMessageBox.MessageBoxButtons.OK, this);
                    return;
                }

                if (password.Length < 4)
                {
                    CustomMessageBox.Show(LanguageManager.GetString("PasswordTooShort"),
                        LanguageManager.GetString("Register"),
                        CustomMessageBox.MessageBoxType.Warning,
                        CustomMessageBox.MessageBoxButtons.OK, this);
                    return;
                }

                // Register user
                var success = await _authService.RegisterAsync(username, password, _deviceId, UserRole.User);

                if (success)
                {
                    CustomMessageBox.Show(LanguageManager.GetString("RegistrationSuccessful"),
                        LanguageManager.GetString("Register"),
                        CustomMessageBox.MessageBoxType.Information,
                        CustomMessageBox.MessageBoxButtons.OK, this);

                    // Clear password field for security
                    PasswordBox.Password = "";
                    PasswordTextBox.Text = "";
                    UsernameTextBox.Focus();
                }
                else
                {
                    CustomMessageBox.Show(LanguageManager.GetString("UsernameExists"),
                        LanguageManager.GetString("RegistrationFailed"),
                        CustomMessageBox.MessageBoxType.Error,
                        CustomMessageBox.MessageBoxButtons.OK, this);
                }
            }
            catch (Exception ex)
            {
                CustomMessageBox.Show($"Registration error: {ex.Message}", "Error",
                    CustomMessageBox.MessageBoxType.Error,
                    CustomMessageBox.MessageBoxButtons.OK, this);
            }
            finally
            {
                RegisterButton.IsEnabled = true;
                RegisterButtonText.Text = LanguageManager.GetString("Register");
            }
        }

        private void SaveLoginCredentials(string username, string password)
        {
            try
            {
                // Save encrypted credentials (implement secure storage)
                Properties.Settings.Default.SavedUsername = username;
                Properties.Settings.Default.RememberLogin = true;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                // Log error but don't show to user
                System.Diagnostics.Debug.WriteLine($"Failed to save credentials: {ex.Message}");
            }
        }

        private void LoadSavedLanguage()
        {
            try
            {
                var savedLanguage = Properties.Settings.Default.Language;
                if (!string.IsNullOrEmpty(savedLanguage))
                {
                    LanguageManager.SetLanguage(savedLanguage);
                    System.Diagnostics.Debug.WriteLine($"✅ Loaded saved language: {savedLanguage}");
                }
                else
                {
                    // Default to Arabic
                    LanguageManager.SetLanguage("ar");
                    System.Diagnostics.Debug.WriteLine("🌐 Set default language to Arabic");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Failed to load saved language: {ex.Message}");
                // Fallback to Arabic
                LanguageManager.SetLanguage("ar");
            }
        }

        public void LoadSavedCredentials()
        {
            try
            {
                // Only load if RememberLogin is true AND we have valid saved data
                if (Properties.Settings.Default.RememberLogin &&
                    !string.IsNullOrEmpty(Properties.Settings.Default.SavedUsername))
                {
                    UsernameTextBox.Text = Properties.Settings.Default.SavedUsername;
                    RememberLoginCheckBox.IsChecked = true;
                    System.Diagnostics.Debug.WriteLine($"✅ Loaded saved credentials for: {Properties.Settings.Default.SavedUsername}");
                }
                else
                {
                    // Clear any invalid data
                    UsernameTextBox.Text = "";
                    RememberLoginCheckBox.IsChecked = false;
                    System.Diagnostics.Debug.WriteLine("✅ No valid saved credentials found - starting fresh");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Failed to load saved credentials: {ex.Message}");
                // Clear on error
                UsernameTextBox.Text = "";
                RememberLoginCheckBox.IsChecked = false;
            }
        }

        private void UpdateLanguage()
        {
            try
            {
                // Update window title
                Title = LanguageManager.GetString("LoginTitle");

                // Update UI text elements
                SecureAccessText.Text = LanguageManager.GetString("SecureAccess");
                AuthPortalText.Text = LanguageManager.GetString("AuthenticationPortal");
                UsernameLabel.Text = LanguageManager.GetString("Username");
                PasswordLabel.Text = LanguageManager.GetString("Password");
                RememberLoginCheckBox.Content = LanguageManager.GetString("RememberLogin");
                LoginButtonText.Text = LanguageManager.GetString("Login");
                RegisterButtonText.Text = LanguageManager.GetString("Register");
                VersionText.Text = LanguageManager.GetString("VersionInfo");

                // Update device ID text
                if (!string.IsNullOrEmpty(_deviceId))
                {
                    DeviceIdDisplay.Text = $"{LanguageManager.GetString("DeviceId")} {_deviceId.Substring(0, 8)}...";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update language: {ex.Message}");
            }
        }

        private void LoginWindow_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Force refresh Device ID display
                LoadDeviceId();
                System.Diagnostics.Debug.WriteLine("✅ LoginWindow loaded - Device ID refreshed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in LoginWindow_Loaded: {ex.Message}");
            }
        }

        private void PowerButton_MouseEnter(object sender, MouseEventArgs e)
        {
            try
            {
                PowerIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0x44, 0x44));
                PowerIcon.Effect = new DropShadowEffect
                {
                    Color = System.Windows.Media.Color.FromRgb(0xFF, 0x44, 0x44),
                    BlurRadius = 12,
                    ShadowDepth = 0,
                    Opacity = 1.0
                };
                System.Diagnostics.Debug.WriteLine("🔴 Power icon changed to red on hover");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in PowerButton_MouseEnter: {ex.Message}");
            }
        }

        private void PowerButton_MouseLeave(object sender, MouseEventArgs e)
        {
            try
            {
                PowerIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x00, 0xE5, 0xFF));
                PowerIcon.Effect = new DropShadowEffect
                {
                    Color = System.Windows.Media.Color.FromRgb(0x00, 0xE5, 0xFF),
                    BlurRadius = 8,
                    ShadowDepth = 0,
                    Opacity = 0.8
                };
                System.Diagnostics.Debug.WriteLine("🔵 Power icon changed back to blue");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in PowerButton_MouseLeave: {ex.Message}");
            }
        }

        private void PowerButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔴 Power button clicked - asking for confirmation");

                // Show custom confirmation dialog
                var confirmDialog = new Views.ConfirmExitDialog
                {
                    Owner = this
                };

                var result = confirmDialog.ShowDialog();

                if (result == true && confirmDialog.Result == true)
                {
                    System.Diagnostics.Debug.WriteLine("✅ User confirmed exit - shutting down application");

                    // Close this window
                    this.Close();

                    // Shutdown the entire application
                    System.Windows.Application.Current.Shutdown();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ User cancelled exit");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in power button: {ex.Message}");
                // Force close if normal shutdown fails
                Environment.Exit(0);
            }
        }

    }
}
