# SafeLink Simple Firewall Test
Write-Host "🔥 SafeLink Firewall Test" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""

# تحقق من القواعد
$rules = @("SafeLink_UDP_Inbound", "SafeLink_UDP_Outbound", "SafeLink_TCP_Inbound", "SafeLink_TCP_Outbound")

Write-Host "📋 Checking firewall rules..." -ForegroundColor Yellow
foreach ($rule in $rules) {
    $result = netsh advfirewall firewall show rule name="$rule" 2>$null
    if ($result -match "No rules match the specified criteria") {
        Write-Host "❌ $rule - NOT FOUND" -ForegroundColor Red
    } else {
        Write-Host "✅ $rule - ACTIVE" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "📡 Checking SafeLink ports..." -ForegroundColor Yellow

# تحقق من البورتات (4 أرقام!)
$udpPorts = netstat -an | findstr "UDP" | findstr ":888"
if ($udpPorts) {
    Write-Host "✅ SafeLink UDP ports found:" -ForegroundColor Green
    $udpPorts | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ No SafeLink UDP ports found" -ForegroundColor Red
}

Write-Host ""
Write-Host "🚀 SafeLink Process:" -ForegroundColor Yellow
$processes = Get-Process | Where-Object {$_.ProcessName -like "*SafeLink*"}
if ($processes) {
    Write-Host "✅ SafeLink is running" -ForegroundColor Green
    $processes | Format-Table ProcessName, Id, WorkingSet -AutoSize
} else {
    Write-Host "❌ SafeLink is not running" -ForegroundColor Red
}

Write-Host ""
Write-Host "✅ Test completed!" -ForegroundColor Green
