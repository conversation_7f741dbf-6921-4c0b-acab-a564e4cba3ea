using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace SafeLink.Tools
{
    public static class NetworkDiagnosticTool
    {
        /// <summary>
        /// تشخيص شامل لمشاكل الشبكة
        /// </summary>
        public static async Task<string> RunFullDiagnosticAsync()
        {
            var report = new StringBuilder();
            report.AppendLine("🔍 SafeLink Network Diagnostic Report");
            report.AppendLine("=====================================");
            report.AppendLine($"📅 Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // 1. فحص البورتات النشطة
            report.AppendLine("📡 Active Ports Check:");
            report.AppendLine("---------------------");
            await CheckActivePorts(report);
            report.AppendLine();

            // 2. فحص واجهات الشبكة
            report.AppendLine("🌐 Network Interfaces:");
            report.AppendLine("---------------------");
            CheckNetworkInterfaces(report);
            report.AppendLine();

            // 3. فحص جدار الحماية
            report.AppendLine("🔥 Firewall Rules:");
            report.AppendLine("------------------");
            await CheckFirewallRules(report);
            report.AppendLine();

            // 4. اختبار الاتصال المحلي
            report.AppendLine("🔄 Local Connectivity Test:");
            report.AppendLine("---------------------------");
            await TestLocalConnectivity(report);
            report.AppendLine();

            // 5. فحص عمليات SafeLink
            report.AppendLine("🚀 SafeLink Processes:");
            report.AppendLine("---------------------");
            CheckSafeLinkProcesses(report);
            report.AppendLine();

            return report.ToString();
        }

        private static async Task CheckActivePorts(StringBuilder report)
        {
            try
            {
                // فحص البورتات UDP
                var udpPorts = await GetActiveUdpPorts();
                var safeLinkUdpPorts = udpPorts.FindAll(p => p >= 8888 && p <= 8938);
                
                if (safeLinkUdpPorts.Count > 0)
                {
                    report.AppendLine("✅ SafeLink UDP ports found:");
                    foreach (var port in safeLinkUdpPorts)
                    {
                        report.AppendLine($"   UDP Port {port} - ACTIVE");
                    }
                }
                else
                {
                    report.AppendLine("❌ No SafeLink UDP ports found (8888-8938)");
                }

                // فحص البورتات TCP
                var tcpPorts = await GetActiveTcpPorts();
                var safeLinkTcpPorts = tcpPorts.FindAll(p => p >= 8889 && p <= 8939);
                
                if (safeLinkTcpPorts.Count > 0)
                {
                    report.AppendLine("✅ SafeLink TCP ports found:");
                    foreach (var port in safeLinkTcpPorts)
                    {
                        report.AppendLine($"   TCP Port {port} - ACTIVE");
                    }
                }
                else
                {
                    report.AppendLine("⚠️ No SafeLink TCP ports found (8889-8939)");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Error checking ports: {ex.Message}");
            }
        }

        private static void CheckNetworkInterfaces(StringBuilder report)
        {
            try
            {
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                var activeInterfaces = 0;

                foreach (var ni in interfaces)
                {
                    if (ni.OperationalStatus == OperationalStatus.Up && 
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    {
                        activeInterfaces++;
                        report.AppendLine($"✅ {ni.Name} - {ni.NetworkInterfaceType} - UP");
                        
                        var ipProps = ni.GetIPProperties();
                        foreach (var addr in ipProps.UnicastAddresses)
                        {
                            if (addr.Address.AddressFamily == AddressFamily.InterNetwork)
                            {
                                report.AppendLine($"   IP: {addr.Address} / {addr.IPv4Mask}");
                            }
                        }
                    }
                }

                if (activeInterfaces == 0)
                {
                    report.AppendLine("❌ No active network interfaces found");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Error checking network interfaces: {ex.Message}");
            }
        }

        private static async Task CheckFirewallRules(StringBuilder report)
        {
            try
            {
                var rules = new[] { "SafeLink_UDP_Inbound", "SafeLink_UDP_Outbound", 
                                  "SafeLink_TCP_Inbound", "SafeLink_TCP_Outbound" };
                
                foreach (var rule in rules)
                {
                    var exists = await CheckFirewallRuleExists(rule);
                    if (exists)
                    {
                        report.AppendLine($"✅ {rule} - ACTIVE");
                    }
                    else
                    {
                        report.AppendLine($"❌ {rule} - MISSING");
                    }
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Error checking firewall: {ex.Message}");
            }
        }

        private static async Task TestLocalConnectivity(StringBuilder report)
        {
            try
            {
                // اختبار البث المحلي
                var testPorts = new[] { 8888, 8889, 8890, 8891 };
                
                foreach (var port in testPorts)
                {
                    var canBind = await CanBindToPort(port);
                    if (canBind)
                    {
                        report.AppendLine($"✅ Port {port} - Available for binding");
                    }
                    else
                    {
                        report.AppendLine($"⚠️ Port {port} - Already in use");
                    }
                }

                // اختبار البث
                var broadcastTest = await TestBroadcast();
                if (broadcastTest)
                {
                    report.AppendLine("✅ UDP Broadcast - Working");
                }
                else
                {
                    report.AppendLine("❌ UDP Broadcast - Failed");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Error testing connectivity: {ex.Message}");
            }
        }

        private static void CheckSafeLinkProcesses(StringBuilder report)
        {
            try
            {
                var processes = Process.GetProcessesByName("SafeLink");
                if (processes.Length > 0)
                {
                    foreach (var process in processes)
                    {
                        report.AppendLine($"✅ SafeLink Process - PID: {process.Id}, Memory: {process.WorkingSet64 / 1024 / 1024} MB");
                    }
                }
                else
                {
                    report.AppendLine("❌ No SafeLink processes found");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Error checking processes: {ex.Message}");
            }
        }

        private static async Task<List<int>> GetActiveUdpPorts()
        {
            var ports = new List<int>();
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "netstat",
                    Arguments = "-an",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                var lines = output.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains("UDP") && line.Contains(":888"))
                    {
                        var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 2)
                        {
                            var address = parts[1];
                            var portIndex = address.LastIndexOf(':');
                            if (portIndex > 0 && int.TryParse(address.Substring(portIndex + 1), out var port))
                            {
                                ports.Add(port);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting UDP ports: {ex.Message}");
            }
            return ports;
        }

        private static async Task<List<int>> GetActiveTcpPorts()
        {
            var ports = new List<int>();
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "netstat",
                    Arguments = "-an",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                var lines = output.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains("TCP") && line.Contains(":888"))
                    {
                        var parts = line.Split(new[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length >= 2)
                        {
                            var address = parts[1];
                            var portIndex = address.LastIndexOf(':');
                            if (portIndex > 0 && int.TryParse(address.Substring(portIndex + 1), out var port))
                            {
                                ports.Add(port);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting TCP ports: {ex.Message}");
            }
            return ports;
        }

        private static async Task<bool> CheckFirewallRuleExists(string ruleName)
        {
            try
            {
                var startInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall show rule name=\"{ruleName}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(startInfo);
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                return !output.Contains("No rules match the specified criteria");
            }
            catch
            {
                return false;
            }
        }

        private static async Task<bool> CanBindToPort(int port)
        {
            try
            {
                using var udpClient = new UdpClient();
                udpClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                udpClient.Client.Bind(new IPEndPoint(IPAddress.Any, port));
                return true;
            }
            catch
            {
                return false;
            }
        }

        private static async Task<bool> TestBroadcast()
        {
            try
            {
                using var udpClient = new UdpClient();
                udpClient.EnableBroadcast = true;
                
                var testMessage = "SAFELINK_TEST_BROADCAST";
                var data = Encoding.UTF8.GetBytes(testMessage);
                var endpoint = new IPEndPoint(IPAddress.Broadcast, 8888);
                
                await udpClient.SendAsync(data, data.Length, endpoint);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
