<UserControl x:Class="SafeLink.Controls.ShieldIcon"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Viewbox Stretch="Uniform">
        <Canvas Width="100" Height="120">
            <!-- Classic Shield Outline with Inward Curves -->
            <Path x:Name="ShieldPath"
                  Fill="Transparent"
                  Stroke="{Binding ShieldColor, RelativeSource={RelativeSource AncestorType=UserControl}}"
                  StrokeThickness="3"
                  StrokeLineJoin="Round"
                  StrokeStartLineCap="Round"
                  StrokeEndLineCap="Round">
                <Path.Data>
                    <PathGeometry>
                        <PathFigure StartPoint="50,5" IsClosed="True">
                            <!-- Top point to right shoulder with inward curve -->
                            <QuadraticBezierSegment Point1="65,20" Point2="85,25"/>
                            <!-- Right side down -->
                            <LineSegment Point="85,70"/>
                            <!-- Bottom right curve -->
                            <QuadraticBezierSegment Point1="85,90" Point2="65,100"/>
                            <!-- Bottom point (sharp) -->
                            <LineSegment Point="50,110"/>
                            <!-- Bottom left curve -->
                            <LineSegment Point="35,100"/>
                            <QuadraticBezierSegment Point1="15,90" Point2="15,70"/>
                            <!-- Left side up -->
                            <LineSegment Point="15,25"/>
                            <!-- Left shoulder to top point with inward curve -->
                            <QuadraticBezierSegment Point1="35,20" Point2="50,5"/>
                        </PathFigure>
                    </PathGeometry>
                </Path.Data>
            </Path>
        </Canvas>
    </Viewbox>
</UserControl>
