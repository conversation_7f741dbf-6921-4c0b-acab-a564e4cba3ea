using System;
using System.ComponentModel;

namespace SafeLink.Models
{
    public class User : INotifyPropertyChanged
    {
        private int _id;
        private string _username;
        private string _arabicUsername;
        private string _deviceId;
        private UserRole _role;
        private UserStatus _status;
        private DateTime? _lastLogin;
        private bool _isActive;
        private DateTime _lastSeen = DateTime.Now;
        private bool _isOnline = false;
        private int _port = 8888;
        private bool _isMeshLeader = false;
        private int _meshNetworkPort = 0;

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        public string Username
        {
            get => _username;
            set
            {
                _username = value;
                OnPropertyChanged(nameof(Username));
            }
        }

        public string ArabicUsername
        {
            get => _arabicUsername;
            set
            {
                _arabicUsername = value;
                OnPropertyChanged(nameof(ArabicUsername));
            }
        }

        public string DeviceId
        {
            get => _deviceId;
            set
            {
                _deviceId = value;
                OnPropertyChanged(nameof(DeviceId));
            }
        }

        public UserRole Role
        {
            get => _role;
            set
            {
                _role = value;
                OnPropertyChanged(nameof(Role));
            }
        }

        public UserStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public DateTime? LastLogin
        {
            get => _lastLogin;
            set
            {
                _lastLogin = value;
                OnPropertyChanged(nameof(LastLogin));
            }
        }

        public bool IsActive
        {
            get => _isActive;
            set
            {
                _isActive = value;
                OnPropertyChanged(nameof(IsActive));
            }
        }

        public DateTime LastSeen
        {
            get => _lastSeen;
            set
            {
                _lastSeen = value;
                OnPropertyChanged(nameof(LastSeen));
            }
        }

        public bool IsOnline
        {
            get => _isOnline;
            set
            {
                _isOnline = value;
                OnPropertyChanged(nameof(IsOnline));
            }
        }

        public int Port
        {
            get => _port;
            set
            {
                _port = value;
                OnPropertyChanged(nameof(Port));
            }
        }

        public bool IsMeshLeader
        {
            get => _isMeshLeader;
            set
            {
                _isMeshLeader = value;
                OnPropertyChanged(nameof(IsMeshLeader));
            }
        }

        public int MeshNetworkPort
        {
            get => _meshNetworkPort;
            set
            {
                _meshNetworkPort = value;
                OnPropertyChanged(nameof(MeshNetworkPort));
            }
        }

        public string DisplayName => !string.IsNullOrEmpty(ArabicUsername) ? ArabicUsername : Username;

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public enum UserRole
    {
        User = 0,
        Moderator = 1,
        Admin = 2,
        Developer = 3
    }

    public enum UserStatus
    {
        Offline = 0,
        Online = 1,
        Away = 2,
        Busy = 3
    }
}
