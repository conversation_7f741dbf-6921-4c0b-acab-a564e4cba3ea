<UserControl x:Class="SafeLink.Controls.MessageStatusIndicator"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="20" d:DesignWidth="40">
    
    <UserControl.Resources>
        <!-- Status Colors -->
        <SolidColorBrush x:Key="SendingColor" Color="#9E9E9E"/>
        <SolidColorBrush x:Key="SentColor" Color="#9E9E9E"/>
        <SolidColorBrush x:Key="DeliveredColor" Color="#9E9E9E"/>
        <SolidColorBrush x:Key="ReadColor" Color="#4FC3F7"/>
        <SolidColorBrush x:Key="FailedColor" Color="#F44336"/>
    </UserControl.Resources>
    
    <Grid>
        <!-- Sending Status (Clock) -->
        <Viewbox x:Name="SendingIcon" Width="16" Height="16" Visibility="Collapsed">
            <Canvas Width="24" Height="24">
                <Path Fill="{StaticResource SendingColor}" 
                      Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.7L16.2,16.2Z"/>
            </Canvas>
        </Viewbox>
        
        <!-- Sent Status (Single Check) -->
        <Viewbox x:Name="SentIcon" Width="16" Height="16" Visibility="Collapsed">
            <Canvas Width="24" Height="24">
                <Path Fill="{StaticResource SentColor}" 
                      Data="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
            </Canvas>
        </Viewbox>
        
        <!-- Delivered Status (Double Check) -->
        <Viewbox x:Name="DeliveredIcon" Width="20" Height="16" Visibility="Collapsed">
            <Canvas Width="24" Height="24">
                <Path Fill="{StaticResource DeliveredColor}" 
                      Data="M18,7L16.59,5.59L10.25,11.93L11.66,13.34L18,7M22.24,5.59L11.66,16.17L7.48,12L6.07,13.41L11.66,19L23.66,7L22.24,5.59Z"/>
            </Canvas>
        </Viewbox>
        
        <!-- Read Status (Double Check Blue) -->
        <Viewbox x:Name="ReadIcon" Width="20" Height="16" Visibility="Collapsed">
            <Canvas Width="24" Height="24">
                <Path Fill="{StaticResource ReadColor}" 
                      Data="M18,7L16.59,5.59L10.25,11.93L11.66,13.34L18,7M22.24,5.59L11.66,16.17L7.48,12L6.07,13.41L11.66,19L23.66,7L22.24,5.59Z"/>
            </Canvas>
        </Viewbox>
        
        <!-- Failed Status (X) -->
        <Viewbox x:Name="FailedIcon" Width="16" Height="16" Visibility="Collapsed">
            <Canvas Width="24" Height="24">
                <Path Fill="{StaticResource FailedColor}" 
                      Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </Canvas>
        </Viewbox>
    </Grid>
</UserControl>
