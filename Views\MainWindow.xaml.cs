using System;
using System.Collections.Generic;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Shapes;
using System.Threading.Tasks;
using System.ComponentModel;
using SafeLink.Core;
using SafeLink.Models;
using SafeLink.Tools;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Data;
using System.Globalization;
using System.Windows.Forms;
using System.Drawing;

namespace SafeLink.Views
{
    public partial class MainWindow : Window
    {
        private DatabaseManager _databaseManager;
        private NetworkManager _networkManager;
        private User _currentUser;
        private User _activeChatUser;
        private ObservableCollection<User> _onlineUsers;
        private ObservableCollection<User> _connectedUsers;
        private string _deviceId;
        private bool _isReallyClosing = false;
        private bool _isPasswordVisible = false;
        private NotifyIcon _notifyIcon;
        private bool _isConnected = false;
        private bool _isEmojiPanelOpen = false;
        private bool _silentDiscoveryMode = true; // Silent discovery enabled by default
        private List<string> _pendingAttachments = new List<string>();

        public MainWindow()
        {
            InitializeComponent();

            // Load saved language first
            LoadSavedLanguage();

            InitializeServices();
            InitializeSystemTray();

            // Subscribe to language changes
            LanguageManager.LanguageChanged += UpdateLanguage;

            // Don't start loading sequence here - will be called after authentication
        }

        private void LoadSavedLanguage()
        {
            try
            {
                string savedLanguage = Properties.Settings.Default.Language;
                if (!string.IsNullOrEmpty(savedLanguage))
                {
                    LanguageManager.SetLanguage(savedLanguage);
                    System.Diagnostics.Debug.WriteLine($"✅ Loaded saved language: {savedLanguage}");

                    // Apply RTL for Arabic
                    if (savedLanguage == "ar")
                    {
                        FlowDirection = System.Windows.FlowDirection.RightToLeft;
                    }
                    else
                    {
                        FlowDirection = System.Windows.FlowDirection.LeftToRight;
                    }
                }
                else
                {
                    // Default to English
                    LanguageManager.SetLanguage("en");
                    FlowDirection = System.Windows.FlowDirection.LeftToRight;
                    System.Diagnostics.Debug.WriteLine("✅ Using default language: English");
                }

                // Update UI with loaded language
                UpdateLanguage();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error loading saved language: {ex.Message}");
                // Fallback to English
                LanguageManager.SetLanguage("en");
                FlowDirection = System.Windows.FlowDirection.LeftToRight;
                UpdateLanguage();
            }
        }

        public void SetAuthenticatedUser(User user)
        {
            if (user == null)
            {
                throw new ArgumentNullException(nameof(user), "User cannot be null");
            }

            _currentUser = user;
            _networkManager.SetCurrentUser(user);

            // Update UI with user info
            if (CurrentUserText != null)
            {
                CurrentUserText.Text = $"👤 {user.Username}";
            }

            // Update language immediately after setting user
            UpdateLanguage();

            // Show main application directly (welcome screen removed)
            ShowMainApplication();
        }





        private void InitializeServices()
        {
            _databaseManager = App.GetDatabaseManager();
            _networkManager = App.GetNetworkManager();
            _onlineUsers = new ObservableCollection<User>();
            _connectedUsers = new ObservableCollection<User>();
            UserListBox.ItemsSource = _onlineUsers;

            // Get device ID (temporarily using fixed ID for development)
            _deviceId = "TEMP_DEVICE_ID"; // DeviceManager.GetDeviceId();
            UpdateDeviceIdText();

            // Setup network events
            _networkManager.UserJoined += OnUserJoined;
            // _networkManager.UserLeft += OnUserLeft; // Reserved for future use
            _networkManager.UserWentOffline += OnUserWentOffline;
            _networkManager.MessageReceived += OnMessageReceived;
            _networkManager.ConnectionStatusChanged += OnConnectionStatusChanged;
            _networkManager.MessageStatusChanged += OnMessageStatusChanged;

            // Setup keyboard shortcuts
            KeyDown += MainWindow_KeyDown;

            // Initialize icon hover effects
            InitializeIconHoverEffects();
        }

        private void InitializeSystemTray()
        {
            try
            {
                _notifyIcon = new NotifyIcon();

                // Set icon from SafeLink.ico
                var iconPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Icons", "SafeLink.ico");
                System.Diagnostics.Debug.WriteLine($"🔍 Looking for icon at: {iconPath}");

                if (System.IO.File.Exists(iconPath))
                {
                    _notifyIcon.Icon = new Icon(iconPath);
                    System.Diagnostics.Debug.WriteLine("✅ SafeLink.ico loaded successfully for System Tray");
                }
                else
                {
                    // Try alternative paths
                    var alternativePaths = new[]
                    {
                        System.IO.Path.Combine(Environment.CurrentDirectory, "Icons", "SafeLink.ico"),
                        System.IO.Path.Combine(System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location), "Icons", "SafeLink.ico"),
                        "Icons/SafeLink.ico",
                        "SafeLink.ico"
                    };

                    bool iconLoaded = false;
                    foreach (var altPath in alternativePaths)
                    {
                        if (System.IO.File.Exists(altPath))
                        {
                            _notifyIcon.Icon = new Icon(altPath);
                            System.Diagnostics.Debug.WriteLine($"✅ SafeLink.ico loaded from alternative path: {altPath}");
                            iconLoaded = true;
                            break;
                        }
                    }

                    if (!iconLoaded)
                    {
                        // Fallback to default icon
                        _notifyIcon.Icon = SystemIcons.Application;
                        System.Diagnostics.Debug.WriteLine("⚠️ SafeLink.ico not found, using default system icon");
                    }
                }

                _notifyIcon.Text = "SafeLink";
                _notifyIcon.Visible = false; // Hidden by default

                // Create enhanced context menu
                CreateSystemTrayMenu();

                // Double-click to show
                _notifyIcon.DoubleClick += (s, e) => ShowFromTray();

                System.Diagnostics.Debug.WriteLine("✅ System Tray initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error initializing System Tray: {ex.Message}");
            }
        }

        private void CreateSystemTrayMenu()
        {
            var contextMenu = new ContextMenuStrip();
            contextMenu.BackColor = System.Drawing.Color.FromArgb(45, 45, 48);
            contextMenu.ForeColor = System.Drawing.Color.White;

            // Show/Hide item
            var showHideItem = new ToolStripMenuItem();
            showHideItem.Text = IsVisible ? "إخفاء النافذة" : "إظهار النافذة";
            showHideItem.Image = CreateMenuIcon("👁️");
            showHideItem.Click += (s, e) =>
            {
                if (IsVisible)
                    MinimizeToTray();
                else
                    ShowFromTray();
            };
            contextMenu.Items.Add(showHideItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // Settings item
            var settingsItem = new ToolStripMenuItem("الإعدادات");
            settingsItem.Image = CreateMenuIcon("⚙️");
            settingsItem.Click += (s, e) =>
            {
                ShowFromTray();
                OpenSettings();
            };
            contextMenu.Items.Add(settingsItem);

            // Network status item
            var networkItem = new ToolStripMenuItem();
            networkItem.Text = _isConnected ? "🟢 متصل" : "🔴 غير متصل";
            networkItem.Enabled = false;
            contextMenu.Items.Add(networkItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // About item
            var aboutItem = new ToolStripMenuItem("حول البرنامج");
            aboutItem.Image = CreateMenuIcon("ℹ️");
            aboutItem.Click += (s, e) =>
            {
                ShowFromTray();
                ShowAboutDialog();
            };
            contextMenu.Items.Add(aboutItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // Exit item
            var exitItem = new ToolStripMenuItem("إغلاق البرنامج");
            exitItem.Image = CreateMenuIcon("❌");
            exitItem.Click += (s, e) => ExitApplication();
            contextMenu.Items.Add(exitItem);

            _notifyIcon.ContextMenuStrip = contextMenu;
        }

        private System.Drawing.Image CreateMenuIcon(string emoji)
        {
            try
            {
                var bitmap = new System.Drawing.Bitmap(16, 16);
                using (var graphics = System.Drawing.Graphics.FromImage(bitmap))
                {
                    graphics.Clear(System.Drawing.Color.Transparent);
                    graphics.DrawString(emoji, new System.Drawing.Font("Segoe UI Emoji", 10),
                        System.Drawing.Brushes.White, new System.Drawing.PointF(0, 0));
                }
                return bitmap;
            }
            catch
            {
                return null;
            }
        }

        private void OpenSettings()
        {
            try
            {
                var settingsWindow = new SettingsWindow();
                settingsWindow.Owner = this;
                settingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في فتح الإعدادات: {ex.Message}", NotificationType.Error);
            }
        }

        private void ShowAboutDialog()
        {
            try
            {
                var aboutMessage = $"SafeLink - نظام اتصال آمن\n\n" +
                                 $"الإصدار: 1.0\n" +
                                 $"المطور: Drikon\n" +
                                 $"حقوق الطبع محفوظة © 2024\n\n" +
                                 $"برنامج اتصال آمن ومشفر للشبكات المحلية";

                CustomMessageBox.Show(aboutMessage, "حول SafeLink",
                    CustomMessageBox.MessageBoxType.Information,
                    CustomMessageBox.MessageBoxButtons.OK, this);
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في عرض معلومات البرنامج: {ex.Message}", NotificationType.Error);
            }
        }

        private void InitializeIconHoverEffects()
        {
            try
            {
                // Settings Button Hover Effects
                if (SettingsButton != null && SettingsIcon != null)
                {
                    SettingsButton.MouseEnter += (s, e) => SettingsIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0, 229, 255)); // #00E5FF
                    SettingsButton.MouseLeave += (s, e) => SettingsIcon.IconColor = System.Windows.Media.Brushes.White;
                }

                // Report Button Hover Effects
                if (ReportButton != null && ReportIcon != null)
                {
                    ReportButton.MouseEnter += (s, e) => ReportIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(243, 156, 18)); // #F39C12
                    ReportButton.MouseLeave += (s, e) => ReportIcon.IconColor = System.Windows.Media.Brushes.White;
                }

                // Diagnostic Button Hover Effects
                if (DiagnosticButton != null && DiagnosticIcon != null)
                {
                    DiagnosticButton.MouseEnter += (s, e) => DiagnosticIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(155, 89, 182)); // #9B59B6
                    DiagnosticButton.MouseLeave += (s, e) => DiagnosticIcon.IconColor = System.Windows.Media.Brushes.White;
                }

                // Logout Button Hover Effects
                if (LogoutButton != null && LogoutIcon != null)
                {
                    LogoutButton.MouseEnter += (s, e) => LogoutIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(231, 76, 60)); // #E74C3C
                    LogoutButton.MouseLeave += (s, e) => LogoutIcon.IconColor = System.Windows.Media.Brushes.White;
                }



                // Initialize close button hover effects
                InitializeCloseButtonHoverEffects();

                System.Diagnostics.Debug.WriteLine("✅ Icon hover effects initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error initializing icon hover effects: {ex.Message}");
            }
        }

        private void InitializeCloseButtonHoverEffects()
        {
            try
            {
                // Close Button Hover Effects (copied from LoginWindow)
                if (CloseButton != null && CloseIcon != null)
                {
                    CloseButton.MouseEnter += CloseButton_MouseEnter;
                    CloseButton.MouseLeave += CloseButton_MouseLeave;
                }

                System.Diagnostics.Debug.WriteLine("✅ Close button hover effects initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error initializing close button hover effects: {ex.Message}");
            }
        }

        private void CloseButton_MouseEnter(object sender, System.Windows.Input.MouseEventArgs e)
        {
            try
            {
                if (CloseIcon != null)
                {
                    CloseIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0x44, 0x44));
                    CloseIcon.Effect = new DropShadowEffect
                    {
                        Color = System.Windows.Media.Color.FromRgb(0xFF, 0x44, 0x44),
                        BlurRadius = 12,
                        ShadowDepth = 0,
                        Opacity = 1.0
                    };
                }
                System.Diagnostics.Debug.WriteLine("🔴 Close icon changed to red on hover");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in CloseButton_MouseEnter: {ex.Message}");
            }
        }

        private void CloseButton_MouseLeave(object sender, System.Windows.Input.MouseEventArgs e)
        {
            try
            {
                if (CloseIcon != null)
                {
                    CloseIcon.IconColor = System.Windows.Media.Brushes.White;
                    CloseIcon.Effect = new DropShadowEffect
                    {
                        Color = System.Windows.Media.Colors.White,
                        BlurRadius = 4,
                        ShadowDepth = 0,
                        Opacity = 0.6
                    };
                }
                System.Diagnostics.Debug.WriteLine("⚪ Close icon changed back to white");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in CloseButton_MouseLeave: {ex.Message}");
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔴 Close button clicked - asking for confirmation");

                // Show custom confirmation dialog
                var confirmDialog = new Views.ConfirmExitDialog
                {
                    Owner = this
                };

                var result = confirmDialog.ShowDialog();

                if (result == true && confirmDialog.Result == true)
                {
                    System.Diagnostics.Debug.WriteLine("✅ User confirmed exit - shutting down application");

                    // Close this window
                    this.Close();

                    // Shutdown the entire application
                    System.Windows.Application.Current.Shutdown();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ User cancelled exit");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in CloseButton_Click: {ex.Message}");
                // Fallback: close directly
                this.Close();
            }
        }

        private void StartWelcomeAnimations()
        {
            try
            {
                // Create network grid animation
                CreateNetworkGrid();

                // Security indicator pulse - check if element exists
                if (SecurityIndicator != null)
                {
                    var indicatorPulse = new DoubleAnimation
                    {
                        From = 0.5,
                        To = 1.0,
                        Duration = TimeSpan.FromSeconds(1.5),
                        AutoReverse = true,
                        RepeatBehavior = RepeatBehavior.Forever
                    };

                    SecurityIndicator.BeginAnimation(OpacityProperty, indicatorPulse);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Animation error: {ex.Message}");
            }
        }

        private void CreateNetworkGrid()
        {
            var random = new Random();
            var canvasWidth = 1024;
            var canvasHeight = 720;
            var gridSize = 80;
            var nodes = new List<NetworkNode>();

            // Create network nodes
            for (int x = 0; x < canvasWidth; x += gridSize)
            {
                for (int y = 0; y < canvasHeight; y += gridSize)
                {
                    // Add some randomness to node positions
                    var nodeX = x + random.Next(-20, 20);
                    var nodeY = y + random.Next(-20, 20);

                    var node = new NetworkNode
                    {
                        X = nodeX,
                        Y = nodeY,
                        IsActive = random.NextDouble() > 0.7 // 30% chance to be active
                    };

                    nodes.Add(node);

                    // Create visual node
                    var nodeEllipse = new Ellipse
                    {
                        Width = 4,
                        Height = 4,
                        Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(120, 0, 212, 170)), // Semi-transparent green
                        Opacity = node.IsActive ? 0.8 : 0.3
                    };

                    // Apply depth field effect
                    var distanceFromCenter = Math.Sqrt(Math.Pow(nodeX - canvasWidth/2, 2) + Math.Pow(nodeY - canvasHeight/2, 2));
                    var maxDistance = Math.Sqrt(Math.Pow(canvasWidth/2, 2) + Math.Pow(canvasHeight/2, 2));
                    var depthFactor = 1 - (distanceFromCenter / maxDistance);

                    nodeEllipse.Opacity *= depthFactor;
                    nodeEllipse.Effect = new BlurEffect { Radius = (1 - depthFactor) * 3 };

                    Canvas.SetLeft(nodeEllipse, nodeX);
                    Canvas.SetTop(nodeEllipse, nodeY);
                    NetworkCanvas.Children.Add(nodeEllipse);

                    // Animate active nodes
                    if (node.IsActive)
                    {
                        var pulseAnimation = new DoubleAnimation
                        {
                            From = 0.3,
                            To = 1.0,
                            Duration = TimeSpan.FromSeconds(random.NextDouble() * 2 + 1),
                            AutoReverse = true,
                            RepeatBehavior = RepeatBehavior.Forever
                        };

                        nodeEllipse.BeginAnimation(OpacityProperty, pulseAnimation);
                    }
                }
            }

            // Create connections between nearby nodes
            for (int i = 0; i < nodes.Count; i++)
            {
                for (int j = i + 1; j < nodes.Count; j++)
                {
                    var distance = Math.Sqrt(Math.Pow(nodes[i].X - nodes[j].X, 2) + Math.Pow(nodes[i].Y - nodes[j].Y, 2));

                    if (distance < gridSize * 1.5 && random.NextDouble() > 0.6) // 40% chance for connection
                    {
                        CreateNetworkConnection(nodes[i], nodes[j], canvasWidth, canvasHeight);
                    }
                }
            }
        }

        private void CreateNetworkConnection(NetworkNode node1, NetworkNode node2, double canvasWidth, double canvasHeight)
        {
            var line = new Line
            {
                X1 = node1.X,
                Y1 = node1.Y,
                X2 = node2.X,
                Y2 = node2.Y,
                Stroke = new SolidColorBrush(System.Windows.Media.Color.FromArgb(60, 52, 152, 219)), // Semi-transparent blue
                StrokeThickness = 1
            };

            // Apply depth field effect to connections
            var centerX = canvasWidth / 2;
            var centerY = canvasHeight / 2;
            var midX = (node1.X + node2.X) / 2;
            var midY = (node1.Y + node2.Y) / 2;

            var distanceFromCenter = Math.Sqrt(Math.Pow(midX - centerX, 2) + Math.Pow(midY - centerY, 2));
            var maxDistance = Math.Sqrt(Math.Pow(centerX, 2) + Math.Pow(centerY, 2));
            var depthFactor = 1 - (distanceFromCenter / maxDistance);

            line.Opacity = depthFactor * 0.6;
            line.Effect = new BlurEffect { Radius = (1 - depthFactor) * 2 };

            NetworkCanvas.Children.Add(line);

            // Animate connection if both nodes are active
            if (node1.IsActive && node2.IsActive)
            {
                var random = new Random();
                var pulseAnimation = new DoubleAnimation
                {
                    From = 0.2,
                    To = 0.8,
                    Duration = TimeSpan.FromSeconds(random.NextDouble() * 3 + 2),
                    AutoReverse = true,
                    RepeatBehavior = RepeatBehavior.Forever
                };

                line.BeginAnimation(OpacityProperty, pulseAnimation);
            }
        }

        private void CreateLoginNetworkGrid()
        {
            var random = new Random();
            var canvasWidth = 1024;
            var canvasHeight = 720;
            var gridSize = 120; // Larger grid for subtler effect
            var nodes = new List<NetworkNode>();

            // Create fewer, more subtle network nodes for login
            for (int x = 0; x < canvasWidth; x += gridSize)
            {
                for (int y = 0; y < canvasHeight; y += gridSize)
                {
                    var nodeX = x + random.Next(-30, 30);
                    var nodeY = y + random.Next(-30, 30);

                    var node = new NetworkNode
                    {
                        X = nodeX,
                        Y = nodeY,
                        IsActive = random.NextDouble() > 0.8 // Only 20% active for subtle effect
                    };

                    nodes.Add(node);

                    // Create very subtle visual node
                    var nodeEllipse = new Ellipse
                    {
                        Width = 2,
                        Height = 2,
                        Fill = new SolidColorBrush(System.Windows.Media.Color.FromArgb(80, 0, 212, 170)), // More transparent
                        Opacity = node.IsActive ? 0.4 : 0.1
                    };

                    // Apply depth field effect
                    var distanceFromCenter = Math.Sqrt(Math.Pow(nodeX - canvasWidth/2, 2) + Math.Pow(nodeY - canvasHeight/2, 2));
                    var maxDistance = Math.Sqrt(Math.Pow(canvasWidth/2, 2) + Math.Pow(canvasHeight/2, 2));
                    var depthFactor = 1 - (distanceFromCenter / maxDistance);

                    nodeEllipse.Opacity *= depthFactor * 0.5; // Even more subtle
                    nodeEllipse.Effect = new BlurEffect { Radius = (1 - depthFactor) * 2 };

                    Canvas.SetLeft(nodeEllipse, nodeX);
                    Canvas.SetTop(nodeEllipse, nodeY);
                    LoginNetworkCanvas.Children.Add(nodeEllipse);

                    // Very slow, subtle animations
                    if (node.IsActive)
                    {
                        var pulseAnimation = new DoubleAnimation
                        {
                            From = 0.1,
                            To = 0.4,
                            Duration = TimeSpan.FromSeconds(random.NextDouble() * 4 + 3), // Slower
                            AutoReverse = true,
                            RepeatBehavior = RepeatBehavior.Forever
                        };

                        nodeEllipse.BeginAnimation(OpacityProperty, pulseAnimation);
                    }
                }
            }

            // Create very subtle connections
            for (int i = 0; i < nodes.Count; i++)
            {
                for (int j = i + 1; j < nodes.Count; j++)
                {
                    var distance = Math.Sqrt(Math.Pow(nodes[i].X - nodes[j].X, 2) + Math.Pow(nodes[i].Y - nodes[j].Y, 2));

                    if (distance < gridSize * 1.2 && random.NextDouble() > 0.8) // Fewer connections
                    {
                        CreateLoginNetworkConnection(nodes[i], nodes[j], canvasWidth, canvasHeight);
                    }
                }
            }
        }

        private void CreateLoginNetworkConnection(NetworkNode node1, NetworkNode node2, double canvasWidth, double canvasHeight)
        {
            var line = new Line
            {
                X1 = node1.X,
                Y1 = node1.Y,
                X2 = node2.X,
                Y2 = node2.Y,
                Stroke = new SolidColorBrush(System.Windows.Media.Color.FromArgb(30, 52, 152, 219)), // Very transparent blue
                StrokeThickness = 0.5
            };

            // Apply depth field effect
            var centerX = canvasWidth / 2;
            var centerY = canvasHeight / 2;
            var midX = (node1.X + node2.X) / 2;
            var midY = (node1.Y + node2.Y) / 2;

            var distanceFromCenter = Math.Sqrt(Math.Pow(midX - centerX, 2) + Math.Pow(midY - centerY, 2));
            var maxDistance = Math.Sqrt(Math.Pow(centerX, 2) + Math.Pow(centerY, 2));
            var depthFactor = 1 - (distanceFromCenter / maxDistance);

            line.Opacity = depthFactor * 0.3; // Very subtle
            line.Effect = new BlurEffect { Radius = (1 - depthFactor) * 1.5 };

            LoginNetworkCanvas.Children.Add(line);

            // Very slow connection animations
            if (node1.IsActive && node2.IsActive)
            {
                var random = new Random();
                var pulseAnimation = new DoubleAnimation
                {
                    From = 0.1,
                    To = 0.3,
                    Duration = TimeSpan.FromSeconds(random.NextDouble() * 5 + 4), // Very slow
                    AutoReverse = true,
                    RepeatBehavior = RepeatBehavior.Forever
                };

                line.BeginAnimation(OpacityProperty, pulseAnimation);
            }
        }

        private void UpdateLanguage()
        {
            try
            {
                // Update window title
                Title = LanguageManager.GetString("AppTitle");

                // Update welcome screen texts
                if (AppName != null)
                    AppName.Text = LanguageManager.GetString("SafeLinkBrand");

                if (Tagline != null)
                    Tagline.Text = LanguageManager.GetString("SecureCommunicationPlatform");

                if (WelcomeBrandText != null)
                    WelcomeBrandText.Text = LanguageManager.GetString("SafeLinkBrand");

                if (WelcomeTaglineText != null)
                    WelcomeTaglineText.Text = LanguageManager.GetString("SecureCommunicationPlatform");



                if (SecurityStatus != null)
                    SecurityStatus.Text = LanguageManager.GetString("InitializingSecurityEnvironment");

                if (WelcomeSubText != null)
                    WelcomeSubText.Text = LanguageManager.GetString("SelectUser");

                if (SystemReadyText != null)
                    SystemReadyText.Text = LanguageManager.GetString("SystemReady");

                // DeveloperModeText removed from UI for cleaner interface
                // if (DeveloperModeText != null)
                //     DeveloperModeText.Text = LanguageManager.GetString("DeveloperModeActive");

                if (ActiveUsersText != null)
                    ActiveUsersText.Text = LanguageManager.GetString("ActiveUsers");

                // Update main application texts
                if (CurrentUserText != null && _currentUser != null)
                    CurrentUserText.Text = $"👤 {_currentUser.Username}";

                if (ConnectionText != null)
                    ConnectionText.Text = LanguageManager.GetString("Connected");

                // Update tooltips
                if (SettingsButton != null)
                    SettingsButton.ToolTip = LanguageManager.GetString("SettingsTooltip");

                if (ReportButton != null)
                    ReportButton.ToolTip = LanguageManager.GetString("ReportTooltip");

                if (DiagnosticButton != null)
                    DiagnosticButton.ToolTip = LanguageManager.GetString("DiagnosticTooltip");

                if (LogoutButton != null)
                    LogoutButton.ToolTip = LanguageManager.GetString("LogoutTooltip");



                // Update user count text
                UpdateUserCountText();

                // Update device ID text
                UpdateDeviceIdText();

                System.Diagnostics.Debug.WriteLine($"✅ MainWindow language updated to: {LanguageManager.CurrentLanguage}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating MainWindow language: {ex.Message}");
            }
        }

        private void UpdateUserCountText()
        {
            try
            {
                if (UserCountText != null && _onlineUsers != null)
                {
                    var count = _onlineUsers.Count;
                    UserCountText.Text = string.Format(LanguageManager.GetString("OnlineCount"), count);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating user count text: {ex.Message}");
            }
        }

        private void UpdateDeviceIdText()
        {
            try
            {
                if (DeviceIdText != null && !string.IsNullOrEmpty(_deviceId))
                {
                    DeviceIdText.Text = $"{LanguageManager.GetString("DeviceId")} {_deviceId.Substring(0, 8)}...";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating device ID text: {ex.Message}");
            }
        }

        private class NetworkNode
        {
            public double X { get; set; }
            public double Y { get; set; }
            public bool IsActive { get; set; }
        }

        private async Task AnimateWelcomeSequence()
        {
            // Update language first to ensure correct translations
            UpdateLanguage();

            var statusMessages = new[]
            {
                LanguageManager.GetString("InitializingSecurityProtocols"),
                LanguageManager.GetString("LoadingEncryptionModules"),
                LanguageManager.GetString("EstablishingSecureNetwork"),
                LanguageManager.GetString("ConfiguringAuthenticationSystems"),
                LanguageManager.GetString("ActivatingSecurityLayers"),
                LanguageManager.GetString("FinalizingSecurityEnvironment"),
                LanguageManager.GetString("SystemReadyLaunchingInterface")
            };

            for (int i = 0; i < statusMessages.Length; i++)
            {
                if (SecurityStatus != null)
                    SecurityStatus.Text = statusMessages[i];

                // Animate progress bar with professional timing
                if (ProgressFill != null)
                {
                    var progressAnimation = new DoubleAnimation
                    {
                        From = ProgressFill.Width,
                        To = (i + 1) * (450.0 / statusMessages.Length), // Match new progress bar width
                        Duration = TimeSpan.FromMilliseconds(800),
                        EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                    };

                    ProgressFill.BeginAnimation(WidthProperty, progressAnimation);
                }

                await Task.Delay(1200); // 1.2 seconds per step - total ~8.5 seconds
            }

            await Task.Delay(500); // Final pause
        }





        private void StopWelcomeAnimations()
        {
            // Stop all animations
            SecurityIndicator.BeginAnimation(OpacityProperty, null);

            // Clear network canvases
            NetworkCanvas.Children.Clear();
            LoginNetworkCanvas.Children.Clear();

            // Completely hide the welcome screen to remove background
            WelcomeScreen.Visibility = Visibility.Hidden;
        }

        private void ShowMainApplication()
        {
            try
            {
                // Hide welcome screen and show main application
                WelcomeScreen.Visibility = Visibility.Collapsed;
                if (LoginScreen != null)
                    LoginScreen.Visibility = Visibility.Collapsed;
                MainApplication.Visibility = Visibility.Visible;

                // Update language for main application
                UpdateLanguage();

                // Update user info display
                UpdateUserInfo();

                // Apply developer background if user is developer
                if (_currentUser?.Role == UserRole.Developer)
                {
                    ApplyDeveloperTheme();
                }

                // Initialize notification system
                NotificationManager.Instance.Initialize(NotificationContainer);

                // Start network discovery
                _networkManager.StartDiscovery();

                // Auto-enable online status when application starts (with small delay to ensure UI is loaded)
                _ = Task.Delay(500).ContinueWith(_ => Dispatcher.Invoke(AutoEnableOnlineStatus));

                // Check for updates (non-blocking)
                _ = Task.Run(CheckForUpdatesAsync);

                // Start update listener (non-blocking)
                _ = Task.Run(UpdateManager.StartUpdateListenerAsync);

                // Update system tray with user info
                UpdateSystemTrayMenu();

                System.Diagnostics.Debug.WriteLine($"✅ Main application shown for user: {_currentUser?.Username}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error showing main application: {ex.Message}");
                ShowNotification($"Error initializing main application: {ex.Message}", NotificationType.Error);
            }
        }

        private void ApplyDeveloperTheme()
        {
            // Create starfield background for developer
            var starfieldBrush = new RadialGradientBrush();
            starfieldBrush.GradientStops.Add(new GradientStop(System.Windows.Media.Color.FromRgb(0x00, 0x11, 0x22), 0));
            starfieldBrush.GradientStops.Add(new GradientStop(System.Windows.Media.Color.FromRgb(0x00, 0x00, 0x00), 1));

            Background = starfieldBrush;

            // Show developer welcome message
            // DeveloperWelcome element removed from UI for cleaner interface
            // if (DeveloperWelcome != null)
            //     DeveloperWelcome.Visibility = Visibility.Visible;
            if (WelcomeSubText != null)
                WelcomeSubText.Text = "Developer Console - Full System Access";

            // Update user role display - UserRoleBorder removed from simplified UI
            // UserRoleBorder.Background = (Brush)Resources["AtariDeveloperGradient"];
            // CurrentUserText.Style = (Style)Resources["DeveloperGlowTextStyle"];
        }

        private void UsernameTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                PasswordBox.Focus();
            }
        }

        private void PasswordBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // This is handled by LoginWindow now
            }
        }

        private void PasswordTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // This is handled by LoginWindow now
            }
        }

        private void TogglePasswordButton_Click(object sender, RoutedEventArgs e)
        {
            _isPasswordVisible = !_isPasswordVisible;

            if (_isPasswordVisible)
            {
                // Show password as text
                PasswordTextBox.Text = PasswordBox.Password;
                PasswordBox.Visibility = Visibility.Collapsed;
                PasswordTextBox.Visibility = Visibility.Visible;
                PasswordTextBox.Focus();
                PasswordTextBox.CaretIndex = PasswordTextBox.Text.Length;

                // Change to eye-off icon and green color
                EyeIcon.IconName = "eye-off";
                EyeIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x00, 0xD4, 0xAA));
            }
            else
            {
                // Hide password
                PasswordBox.Password = PasswordTextBox.Text;
                PasswordTextBox.Visibility = Visibility.Collapsed;
                PasswordBox.Visibility = Visibility.Visible;
                PasswordBox.Focus();

                // Change to eye icon and gray color
                EyeIcon.IconName = "eye";
                EyeIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x7F, 0x8C, 0x8D));
            }
        }

        // Custom Title Bar Button Events
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            MinimizeToTray();
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (WindowState == WindowState.Maximized)
            {
                WindowState = WindowState.Normal;
            }
            else
            {
                WindowState = WindowState.Maximized;
            }
        }





        private void MinimizeToTray()
        {
            try
            {
                if (_notifyIcon != null)
                {
                    _notifyIcon.Visible = true;

                    // Show balloon tip with current user info
                    var balloonText = _currentUser != null
                        ? $"SafeLink يعمل في الخلفية\nالمستخدم: {_currentUser.Username}"
                        : "SafeLink يعمل في الخلفية";

                    _notifyIcon.ShowBalloonTip(3000, "SafeLink", balloonText, ToolTipIcon.Info);

                    Hide();

                    // Update context menu
                    UpdateSystemTrayMenu();

                    System.Diagnostics.Debug.WriteLine("✅ Application minimized to system tray");
                }
                else
                {
                    // Fallback to normal minimize
                    WindowState = WindowState.Minimized;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error minimizing to tray: {ex.Message}");
                WindowState = WindowState.Minimized;
            }
        }

        private void UpdateSystemTrayMenu()
        {
            try
            {
                if (_notifyIcon?.ContextMenuStrip != null)
                {
                    // Update tooltip text
                    var tooltipText = "SafeLink";
                    if (_currentUser != null)
                    {
                        tooltipText += $" - {_currentUser.Username}";
                        if (_isConnected)
                            tooltipText += " (متصل)";
                        else
                            tooltipText += " (غير متصل)";
                    }
                    _notifyIcon.Text = tooltipText;

                    // Recreate menu to update dynamic content
                    CreateSystemTrayMenu();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating system tray menu: {ex.Message}");
            }
        }

        private void ShowFromTray()
        {
            try
            {
                if (_notifyIcon != null)
                {
                    _notifyIcon.Visible = false;
                }

                Show();
                WindowState = WindowState.Normal;
                Activate();
                Focus();

                // Update context menu
                UpdateSystemTrayMenu();

                System.Diagnostics.Debug.WriteLine("✅ Application restored from system tray");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error showing from tray: {ex.Message}");
            }
        }

        private void ExitApplication()
        {
            try
            {
                // Show custom confirmation dialog
                var confirmDialog = new Views.ConfirmExitDialog
                {
                    Owner = this
                };

                var result = confirmDialog.ShowDialog();

                if (result == true && confirmDialog.Result == true)
                {
                    _isReallyClosing = true;
                    ClearSensitiveData();

                    if (_notifyIcon != null)
                    {
                        _notifyIcon.Visible = false;
                        _notifyIcon.Dispose();
                    }

                    System.Windows.Application.Current.Shutdown();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in exit application: {ex.Message}");
                Environment.Exit(0);
            }
        }



        private void UpdateUserInfo()
        {
            try
            {
                if (_currentUser != null)
                {
                    if (CurrentUserText != null)
                        CurrentUserText.Text = $"👤 {_currentUser.Username}";

                    if (UserRoleText != null)
                        UserRoleText.Text = _currentUser.Role.ToString();

                    System.Diagnostics.Debug.WriteLine($"✅ User info updated: {_currentUser.Username} ({_currentUser.Role})");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating user info: {ex.Message}");
            }
        }

        private void UserListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (UserListBox.SelectedItem is User selectedUser)
            {
                OpenChat(selectedUser);
            }
        }

        private void OpenChat(User user)
        {
            _activeChatUser = user;

            WelcomeMessage.Visibility = Visibility.Collapsed;
            ChatWindow.Visibility = Visibility.Visible;

            ChatTitleText.Text = $"Chat with {user.Username}";

            // Load chat history
            LoadChatHistory(user);

            MessageTextBox.Focus();
        }

        private async void LoadChatHistory(User user)
        {
            try
            {
                var messages = await _databaseManager.GetChatHistoryAsync(_currentUser.Id, user.Id);
                MessagesPanel.Children.Clear();

                foreach (var message in messages)
                {
                    AddMessageToChat(message);
                }

                // Scroll to bottom
                MessagesScrollViewer.ScrollToBottom();
            }
            catch (Exception ex)
            {
                ShowNotification($"Failed to load chat history: {ex.Message}", NotificationType.Error);
            }
        }

        private void AddMessageToChat(SafeLink.Models.Message message)
        {
            var messageControl = new Border
            {
                Margin = new Thickness(8, 4, 8, 4),
                Padding = new Thickness(15, 12, 15, 12),
                CornerRadius = new CornerRadius(12),
                MaxWidth = 450,
                Tag = message.MessageId // لتتبع الرسالة
            };

            var isOwnMessage = message.FromUserId == _currentUser.Id;
            var senderUser = isOwnMessage ? _currentUser : _onlineUsers.FirstOrDefault(u => u.Id == message.FromUserId);
            var userRole = senderUser?.Role ?? UserRole.User;
            var roleColors = GetRoleColors(userRole);

            // تطبيق ألوان الرتبة والتصميم المحسن
            if (isOwnMessage)
            {
                messageControl.Background = new SolidColorBrush(System.Windows.Media.Color.FromArgb(0x40, roleColors.Primary.R, roleColors.Primary.G, roleColors.Primary.B));
                messageControl.BorderBrush = new SolidColorBrush(roleColors.Primary);
                messageControl.HorizontalAlignment = System.Windows.HorizontalAlignment.Right;
            }
            else
            {
                messageControl.Background = new SolidColorBrush(System.Windows.Media.Color.FromArgb(0x25, 0x2C, 0x3E, 0x50));
                messageControl.BorderBrush = new SolidColorBrush(roleColors.Primary);
                messageControl.HorizontalAlignment = System.Windows.HorizontalAlignment.Left;
            }

            messageControl.BorderThickness = new Thickness(2);

            // إضافة تأثير الظل
            messageControl.Effect = new DropShadowEffect
            {
                Color = roleColors.Primary,
                BlurRadius = 8,
                ShadowDepth = 0,
                Opacity = 0.3
            };

            var stackPanel = new StackPanel();

            // Message header with role indicator
            var headerPanel = new StackPanel { Orientation = System.Windows.Controls.Orientation.Horizontal };

            // Role badge
            var roleBadge = new Border
            {
                Background = new SolidColorBrush(roleColors.Primary),
                CornerRadius = new CornerRadius(8),
                Padding = new Thickness(6, 2, 6, 2),
                Margin = new Thickness(0, 0, 8, 0)
            };

            var roleText = new TextBlock
            {
                Text = GetRoleIcon(userRole),
                FontSize = 10,
                Foreground = new SolidColorBrush(Colors.White),
                FontWeight = FontWeights.Bold
            };

            roleBadge.Child = roleText;
            headerPanel.Children.Add(roleBadge);

            var senderText = new TextBlock
            {
                Text = isOwnMessage ? "You" : message.SenderName,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(roleColors.Primary),
                FontSize = 12
            };

            var timeText = new TextBlock
            {
                Text = message.Timestamp.ToString("HH:mm"),
                Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6)),
                FontSize = 10,
                Margin = new Thickness(10, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            headerPanel.Children.Add(senderText);
            headerPanel.Children.Add(timeText);

            // إضافة أيقونة حالة الرسالة للرسائل المرسلة
            if (isOwnMessage)
            {
                var statusIcon = CreateMessageStatusIcon(message.Status);
                statusIcon.Name = $"StatusIcon_{message.MessageId}";
                statusIcon.Margin = new Thickness(5, 0, 0, 0);
                headerPanel.Children.Add(statusIcon);
            }

            // Message content with emoji support
            var contentText = new TextBlock
            {
                Text = message.Content,
                Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xEC, 0xF0, 0xF1)),
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 8, 0, 0),
                FontSize = 13,
                LineHeight = 18
            };

            stackPanel.Children.Add(headerPanel);
            stackPanel.Children.Add(contentText);

            messageControl.Child = stackPanel;

            // إضافة أنيميشن للرسالة الجديدة
            messageControl.Opacity = 0;
            messageControl.RenderTransform = new TranslateTransform(0, 20);

            MessagesPanel.Children.Add(messageControl);

            // تشغيل أنيميشن الظهور
            var fadeIn = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(300));
            var slideIn = new DoubleAnimation(20, 0, TimeSpan.FromMilliseconds(300));

            messageControl.BeginAnimation(OpacityProperty, fadeIn);
            ((TranslateTransform)messageControl.RenderTransform).BeginAnimation(TranslateTransform.YProperty, slideIn);
        }

        private (System.Windows.Media.Color Primary, System.Windows.Media.Color Secondary) GetRoleColors(UserRole role)
        {
            return role switch
            {
                UserRole.Developer => (System.Windows.Media.Color.FromRgb(0xFF, 0xD7, 0x00), System.Windows.Media.Color.FromRgb(0xFF, 0xE5, 0x4D)), // ذهبي متوهج
                UserRole.Admin => (System.Windows.Media.Color.FromRgb(0xFF, 0x47, 0x57), System.Windows.Media.Color.FromRgb(0xFF, 0x6B, 0x7A)), // أحمر ملكي
                UserRole.Moderator => (System.Windows.Media.Color.FromRgb(0x37, 0x42, 0xFA), System.Windows.Media.Color.FromRgb(0x5A, 0x67, 0xFB)), // أزرق ملكي
                UserRole.User => (System.Windows.Media.Color.FromRgb(0x00, 0xD4, 0xAA), System.Windows.Media.Color.FromRgb(0x1D, 0xE9, 0xB6)), // أخضر البرنامج
                _ => (System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6), System.Windows.Media.Color.FromRgb(0xBD, 0xC3, 0xC7)) // رمادي افتراضي
            };
        }

        private string GetRoleIcon(UserRole role)
        {
            return role switch
            {
                UserRole.Developer => "👑 DEV",
                UserRole.Admin => "⚡ ADMIN",
                UserRole.Moderator => "🛡️ MOD",
                UserRole.User => "👤 USER",
                _ => "👤"
            };
        }

        private TextBlock CreateMessageStatusIcon(MessageStatus status)
        {
            var statusIcon = new TextBlock
            {
                FontSize = 12,
                VerticalAlignment = VerticalAlignment.Center,
                FontFamily = new System.Windows.Media.FontFamily("Segoe UI Symbol")
            };

            switch (status)
            {
                case MessageStatus.Sending:
                    statusIcon.Text = "🕐"; // ساعة - جاري الإرسال
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6));
                    statusIcon.ToolTip = "جاري الإرسال...";
                    break;
                case MessageStatus.Sent:
                    statusIcon.Text = "✓"; // علامة واحدة - تم الإرسال
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6));
                    statusIcon.ToolTip = "تم الإرسال";
                    break;
                case MessageStatus.Delivered:
                    statusIcon.Text = "✓✓"; // علامتان رمادي - تم التسليم
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6));
                    statusIcon.ToolTip = "تم التسليم";
                    break;
                case MessageStatus.Read:
                    statusIcon.Text = "✓✓"; // علامتان أزرق - تم القراءة
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x00, 0x96, 0xFF));
                    statusIcon.ToolTip = "تم القراءة";
                    break;
                case MessageStatus.Failed:
                    statusIcon.Text = "❌"; // علامة خطأ
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0x47, 0x57));
                    statusIcon.ToolTip = "فشل الإرسال";
                    break;
                default:
                    statusIcon.Text = "?";
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6));
                    break;
            }

            return statusIcon;
        }

        private async void SendButton_Click(object sender, RoutedEventArgs e)
        {
            await SendMessage();
        }

        private void EmojiButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء نافذة الإيموجي
                var emojiPicker = new Controls.EmojiPicker();

                // تحديد موقع النافذة بجانب زر الإيموجي
                var button = sender as System.Windows.Controls.Button;
                var buttonPosition = button.PointToScreen(new System.Windows.Point(0, 0));

                var emojiWindow = new Window
                {
                    Content = emojiPicker,
                    WindowStyle = WindowStyle.None,
                    AllowsTransparency = true,
                    Background = System.Windows.Media.Brushes.Transparent,
                    ShowInTaskbar = false,
                    Topmost = true,
                    SizeToContent = SizeToContent.WidthAndHeight,
                    Left = buttonPosition.X,
                    Top = buttonPosition.Y - 350, // فوق الزر
                    Owner = this
                };

                // إضافة معالج لاختيار الإيموجي
                emojiPicker.EmojiSelected += (emoji) =>
                {
                    // إضافة الإيموجي لمربع النص
                    var currentText = MessageTextBox.Text;
                    var caretIndex = MessageTextBox.CaretIndex;

                    MessageTextBox.Text = currentText.Insert(caretIndex, emoji);
                    MessageTextBox.CaretIndex = caretIndex + emoji.Length;
                    MessageTextBox.Focus();

                    // إغلاق نافذة الإيموجي
                    emojiWindow.Close();
                };

                // إغلاق النافذة عند النقر خارجها
                emojiWindow.Deactivated += (s, args) => emojiWindow.Close();

                emojiWindow.Show();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error opening emoji picker: {ex.Message}");
            }
        }

        private void UpdateSendButtonText()
        {
            try
            {
                if (SendButton != null)
                {
                    var stackPanel = SendButton.Content as StackPanel;
                    if (stackPanel != null && stackPanel.Children.Count > 1)
                    {
                        var textBlock = stackPanel.Children[1] as TextBlock;
                        if (textBlock != null)
                        {
                            textBlock.Text = LanguageManager.CurrentLanguage == "ar" ? "إرسال" : "Send";
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating send button text: {ex.Message}");
            }
        }

        private void AutoEnableOnlineStatus()
        {
            try
            {
                // تفعيل الحالة الأونلاين تلقائياً عند بدء التطبيق
                // Auto-enable online status when application starts
                _isConnected = true;
                UpdateToggleSwitch(true);

                System.Diagnostics.Debug.WriteLine("✅ Auto-enabled online status on startup");

                // إظهار إشعار بالتفعيل التلقائي
                // Show notification about auto-enablement
                ShowNotification(
                    LanguageManager.CurrentLanguage == "ar" ?
                    "تم تفعيل الحالة الأونلاين تلقائياً" :
                    "Online status automatically enabled",
                    NotificationType.Success
                );
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error auto-enabling online status: {ex.Message}");
            }
        }



        private async void MessageTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !Keyboard.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                await SendMessage();
            }
        }

        private async Task SendMessage()
        {
            if (_activeChatUser == null || (string.IsNullOrWhiteSpace(MessageTextBox.Text) && !_pendingAttachments.Any()))
                return;

            // Check if connected before sending
            if (!_isConnected)
            {
                ShowNotification("🔴 لا يمكن إرسال الرسالة - الاتصال مقطوع", NotificationType.Warning);
                return;
            }

            var content = MessageTextBox.Text.Trim();
            MessageTextBox.Clear();

            try
            {
                var message = new SafeLink.Models.Message
                {
                    FromUserId = _currentUser.Id,
                    ToUserId = _activeChatUser.Id,
                    Content = content,
                    Timestamp = DateTime.Now,
                    SenderName = _currentUser.Username
                };

                // Save to database
                await _databaseManager.SaveMessageAsync(message);

                // Process attachments if any
                if (_pendingAttachments.Any())
                {
                    await ProcessMessageAttachmentsAsync(message);
                }

                // Add to chat display
                AddMessageToChat(message);

                // Send over network
                await _networkManager.SendMessageAsync(message);

                // Clear attachments after sending
                ClearPendingAttachments();

                // Scroll to bottom
                MessagesScrollViewer.ScrollToBottom();
            }
            catch (Exception ex)
            {
                ShowNotification($"فشل في إرسال الرسالة: {ex.Message}", NotificationType.Error);
            }
        }

        private async Task ProcessMessageAttachmentsAsync(SafeLink.Models.Message message)
        {
            try
            {
                foreach (var filePath in _pendingAttachments.ToList())
                {
                    var fileName = await FileManager.SaveAttachmentAsync(filePath, _currentUser.Id, _activeChatUser.Id);
                    var fileInfo = new FileInfo(filePath);

                    var attachment = new Attachment
                    {
                        MessageId = message.Id,
                        FileName = fileName,
                        OriginalFileName = fileInfo.Name,
                        FileExtension = fileInfo.Extension,
                        FileSize = fileInfo.Length,
                        FilePath = FileManager.GetAttachmentPath(fileName),
                        FileHash = await FileManager.GetFileHashAsync(filePath),
                        UploadedBy = _currentUser.Id,
                        IsImage = FileManager.IsImageFile(fileInfo.Name),
                        IsVideo = FileManager.IsVideoFile(fileInfo.Name),
                        IsAudio = FileManager.IsAudioFile(fileInfo.Name),
                        IsDocument = !FileManager.IsImageFile(fileInfo.Name) && !FileManager.IsVideoFile(fileInfo.Name) && !FileManager.IsAudioFile(fileInfo.Name)
                    };

                    // Save attachment to database
                    await _databaseManager.SaveAttachmentAsync(attachment);

                    // Add to message attachments
                    if (message.Attachments == null)
                        message.Attachments = new List<Attachment>();
                    message.Attachments.Add(attachment);
                }
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في معالجة المرفقات: {ex.Message}", NotificationType.Error);
            }
        }

        private void ClearPendingAttachments()
        {
            _pendingAttachments.Clear();
            AttachmentsPanel.Children.Clear();
            UpdateAttachmentsArea();
        }

        private void CloseChatButton_Click(object sender, RoutedEventArgs e)
        {
            CloseChat();
        }

        private void CloseChat()
        {
            _activeChatUser = null;
            ChatWindow.Visibility = Visibility.Collapsed;
            WelcomeMessage.Visibility = Visibility.Visible;
            UserListBox.SelectedItem = null;
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new SettingsWindow();
            settingsWindow.Owner = this;
            settingsWindow.ShowDialog();
        }

        private async void ReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show report options window
                var reportWindow = new Views.ReportWindow
                {
                    Owner = this
                };

                var result = reportWindow.ShowDialog();

                if (result == true)
                {
                    await HandleReportSubmission(reportWindow.IncludeBasicReport, reportWindow.IncludeCustomDescription, reportWindow.CustomDescription);
                }
            }
            catch (Exception ex)
            {
                ShowNotification(LanguageManager.GetString("ReportFailed") ?? "Failed to send report", NotificationType.Error);
                System.Diagnostics.Debug.WriteLine($"❌ Error in ReportButton_Click: {ex.Message}");
            }
        }

        private async Task HandleReportSubmission(bool includeBasicReport, bool includeCustomDescription, string customDescription)
        {
            try
            {
                var reportParts = new List<string>();

                // Send basic report (screenshot + system info)
                if (includeBasicReport)
                {
                    await _networkManager.SendProblemReportAsync(_currentUser.Id);
                    await _networkManager.SendSystemStatusReportAsync(_currentUser.Id);
                    reportParts.Add("Basic comprehensive report");
                }

                // Send custom description if provided
                if (includeCustomDescription && !string.IsNullOrWhiteSpace(customDescription))
                {
                    await _networkManager.SendCustomMessageReportAsync(_currentUser.Id, customDescription);
                    reportParts.Add("Custom description");
                }

                var reportMessage = $"Report sent successfully: {string.Join(" + ", reportParts)}";
                ShowNotification(LanguageManager.GetString("ReportSent") ?? "Report sent successfully", NotificationType.Success);
                System.Diagnostics.Debug.WriteLine($"✅ {reportMessage}");
            }
            catch (Exception ex)
            {
                ShowNotification(LanguageManager.GetString("ReportFailed") ?? "Failed to send report", NotificationType.Error);
                System.Diagnostics.Debug.WriteLine($"❌ Error sending report: {ex.Message}");
            }
        }

        private async void DiagnosticButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                await SafeLink.Tools.NetworkDiagnostic.ShowDiagnosticWindowAsync();
            }
            catch (Exception ex)
            {
                ShowNotification($"Diagnostic failed: {ex.Message}", NotificationType.Error);
            }
        }

        private async void NetworkTestButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 Starting network test...");
                ShowNotification("Testing network connectivity...", NotificationType.Info);

                // Run network test
                var testResult = await NetworkTester.TestNetworkConnectivity();

                if (testResult)
                {
                    ShowNotification("✅ Network test passed! Connection should work.", NotificationType.Success);
                }
                else
                {
                    ShowNotification("❌ Network test failed. Check firewall settings.", NotificationType.Warning);

                    // Show detailed report
                    var report = await NetworkTester.GetNetworkDiagnosticReport();
                    System.Diagnostics.Debug.WriteLine("📋 Network Diagnostic Report:");
                    System.Diagnostics.Debug.WriteLine(report);

                    // Try quick fix
                    await NetworkTester.RunQuickNetworkFix();
                    ShowNotification("🔧 Attempted automatic network fix. Try again.", NotificationType.Info);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Network test error: {ex.Message}");
                ShowNotification("Network test failed. Check Debug output for details.", NotificationType.Error);
            }
        }

        private void DatabaseCleanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 Database cleanup requested...");

                // Show current database locations
                var locations = DatabaseCleaner.GetDatabaseLocations();
                System.Diagnostics.Debug.WriteLine(locations);

                // Ask for confirmation
                var result = CustomMessageBox.Show(
                    "This will delete all SafeLink databases and chat history. Are you sure?",
                    "Clean Database",
                    CustomMessageBox.MessageBoxType.Question,
                    CustomMessageBox.MessageBoxButtons.YesNo,
                    this);

                if (result == true)
                {
                    DatabaseCleaner.CleanAllDatabases();
                    ShowNotification("🧹 All databases cleaned. Restart SafeLink for fresh start.", NotificationType.Success);

                    // Suggest restart
                    var restartResult = CustomMessageBox.Show(
                        "Database cleaned successfully. Restart SafeLink now?",
                        "Restart Required",
                        CustomMessageBox.MessageBoxType.Question,
                        CustomMessageBox.MessageBoxButtons.YesNo,
                        this);

                    if (restartResult == true)
                    {
                        // Restart application
                        System.Diagnostics.Process.Start(System.Diagnostics.Process.GetCurrentProcess().MainModule.FileName);
                        System.Windows.Application.Current.Shutdown();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Database cleanup error: {ex.Message}");
                ShowNotification("Database cleanup failed. Check Debug output for details.", NotificationType.Error);
            }
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            Logout();
        }

        private void Logout()
        {
            try
            {
                _currentUser = null;
                _activeChatUser = null;
                _onlineUsers.Clear();

                _networkManager.StopDiscovery();
                CloseChat();

                // Close this window without confirmation first
                _isReallyClosing = true;
                this.Hide(); // Hide instead of close to prevent shutdown

                // Create and show login window
                var loginWindow = new LoginWindow();
                loginWindow.LoadSavedCredentials();

                // Set as main window
                System.Windows.Application.Current.MainWindow = loginWindow;
                loginWindow.Show();

                // Now close the main window
                this.Close();

                System.Diagnostics.Debug.WriteLine("✅ Logout completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error during logout: {ex.Message}");
            }
        }

        private void ShareFileButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: Implement file sharing
            ShowNotification("File sharing feature coming soon", NotificationType.Info);
        }

        private async Task CheckForUpdatesAsync()
        {
            try
            {
                await Task.Delay(5000); // انتظار 5 ثوانٍ بعد بدء التشغيل

                var hasUpdate = await UpdateManager.CheckForUpdatesAsync();
                if (hasUpdate)
                {
                    Dispatcher.Invoke(() =>
                    {
                        var result = System.Windows.MessageBox.Show(
                            "🔄 يتوفر تحديث جديد لـ SafeLink!\n\nهل تريد تحميل وتثبيت التحديث الآن؟",
                            "تحديث متاح",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Information);

                        if (result == MessageBoxResult.Yes)
                        {
                            _ = Task.Run(async () =>
                            {
                                var success = await UpdateManager.DownloadAndInstallUpdateAsync();
                                if (!success)
                                {
                                    Dispatcher.Invoke(() =>
                                    {
                                        ShowNotification("فشل في تحديث البرنامج", NotificationType.Error);
                                    });
                                }
                            });
                        }
                    });
                }
            }
            catch
            {
                // تجاهل أخطاء فحص التحديثات
            }
        }

        private void MainWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
        {
            // F8 for problem report
            if (e.Key == Key.F8)
            {
                ReportButton_Click(sender, e);
            }
            // Ctrl+Shift+D for developer panel
            else if (e.Key == Key.D && Keyboard.Modifiers.HasFlag(ModifierKeys.Control | ModifierKeys.Shift))
            {
                if (_currentUser?.Role == UserRole.Developer)
                {
                    var developerWindow = new DeveloperWindow();
                    developerWindow.Owner = this;
                    developerWindow.Show();
                }
            }
        }

        private void ConnectionToggle_Click(object sender, MouseButtonEventArgs e)
        {
            // Toggle connection state
            if (_networkManager != null)
            {
                if (_isConnected)
                {
                    // Disconnect
                    _networkManager.StopDiscovery();
                    _isConnected = false;
                    UpdateToggleSwitch(false);

                    // Clear online users and show disconnection status
                    _onlineUsers.Clear();
                    _connectedUsers.Clear();
                    UpdateUserCount();

                    // Show disconnection message
                    ShowConnectionStatusMessage(false);
                }
                else
                {
                    // Connect
                    _ = Task.Run(async () =>
                    {
                        await _networkManager.StartDiscovery();
                        _isConnected = true;
                        Dispatcher.Invoke(() => {
                            UpdateToggleSwitch(true);
                            ShowConnectionStatusMessage(true);
                        });
                    });
                }
            }
        }

        private void UpdateToggleSwitch(bool isConnected)
        {
            if (ConnectionToggleSwitch == null || ToggleCircle == null || ToggleIcon == null || ToggleText == null)
                return;

            // Create smooth animation
            var duration = TimeSpan.FromMilliseconds(300);
            var easing = new QuadraticEase { EasingMode = EasingMode.EaseInOut };

            if (isConnected)
            {
                // ON State - Green
                var colorAnimation = new ColorAnimation
                {
                    To = System.Windows.Media.Color.FromRgb(0x1D, 0xE9, 0xB6), // Green
                    Duration = duration,
                    EasingFunction = easing
                };

                var borderColorAnimation = new ColorAnimation
                {
                    To = System.Windows.Media.Color.FromRgb(0x00, 0xD4, 0xAA), // Darker green
                    Duration = duration,
                    EasingFunction = easing
                };

                var shadowColorAnimation = new ColorAnimation
                {
                    To = System.Windows.Media.Color.FromRgb(0x1D, 0xE9, 0xB6),
                    Duration = duration,
                    EasingFunction = easing
                };

                // Move circle to right
                var marginAnimation = new ThicknessAnimation
                {
                    To = new Thickness(32, 0, 0, 0), // Move to right
                    Duration = duration,
                    EasingFunction = easing
                };

                // Apply animations
                ConnectionToggleSwitch.Background.BeginAnimation(SolidColorBrush.ColorProperty, colorAnimation);
                ConnectionToggleSwitch.BorderBrush.BeginAnimation(SolidColorBrush.ColorProperty, borderColorAnimation);
                ((DropShadowEffect)ConnectionToggleSwitch.Effect).BeginAnimation(DropShadowEffect.ColorProperty, shadowColorAnimation);
                ToggleCircle.BeginAnimation(MarginProperty, marginAnimation);

                // Update content
                ToggleIcon.IconName = "wifi";
                ToggleIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x1D, 0xE9, 0xB6));
                ToggleText.Text = "ON";
                ToggleText.HorizontalAlignment = System.Windows.HorizontalAlignment.Left;
                ToggleText.Margin = new Thickness(8, 0, 0, 0);
            }
            else
            {
                // OFF State - Red
                var colorAnimation = new ColorAnimation
                {
                    To = System.Windows.Media.Color.FromRgb(0xFF, 0x6B, 0x6B), // Red
                    Duration = duration,
                    EasingFunction = easing
                };

                var borderColorAnimation = new ColorAnimation
                {
                    To = System.Windows.Media.Color.FromRgb(0xFF, 0x52, 0x52), // Darker red
                    Duration = duration,
                    EasingFunction = easing
                };

                var shadowColorAnimation = new ColorAnimation
                {
                    To = System.Windows.Media.Color.FromRgb(0xFF, 0x6B, 0x6B),
                    Duration = duration,
                    EasingFunction = easing
                };

                // Move circle to left
                var marginAnimation = new ThicknessAnimation
                {
                    To = new Thickness(4, 0, 0, 0), // Move to left
                    Duration = duration,
                    EasingFunction = easing
                };

                // Apply animations
                ConnectionToggleSwitch.Background.BeginAnimation(SolidColorBrush.ColorProperty, colorAnimation);
                ConnectionToggleSwitch.BorderBrush.BeginAnimation(SolidColorBrush.ColorProperty, borderColorAnimation);
                ((DropShadowEffect)ConnectionToggleSwitch.Effect).BeginAnimation(DropShadowEffect.ColorProperty, shadowColorAnimation);
                ToggleCircle.BeginAnimation(MarginProperty, marginAnimation);

                // Update content
                ToggleIcon.IconName = "wifi-off";
                ToggleIcon.IconColor = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0x6B, 0x6B));
                ToggleText.Text = "OFF";
                ToggleText.HorizontalAlignment = System.Windows.HorizontalAlignment.Right;
                ToggleText.Margin = new Thickness(0, 0, 8, 0);
            }

            // Update connection status
            OnConnectionStatusChanged(isConnected);
        }

        // Network event handlers
        private void OnUserJoined(User user)
        {
            try
            {
                if (user == null || string.IsNullOrEmpty(user.Username)) return;

                Dispatcher.Invoke(() =>
                {
                    try
                    {
                        // Don't add current user to the list
                        if (user.Username == _currentUser?.Username)
                        {
                            System.Diagnostics.Debug.WriteLine($"Ignoring self-discovery: {user.Username}");
                            return;
                        }

                        if (!_onlineUsers.Any(u => u.Username == user.Username))
                        {
                            _onlineUsers.Add(user);
                            UpdateUserCount();

                            if (_silentDiscoveryMode)
                            {
                                // Silent discovery - no notifications shown to user
                                System.Diagnostics.Debug.WriteLine($"🔇 Silent user discovery: {user.Username}");
                            }
                            else
                            {
                                // Show notification only if silent mode is disabled
                                NotificationManager.Instance.ShowConnectionNotification(user, true);
                                System.Diagnostics.Debug.WriteLine($"✅ User joined: {user.Username}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔴 Error in OnUserJoined UI update: {ex.Message}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"🔴 Error in OnUserJoined: {ex.Message}");
            }
        }

        private void OnUserLeft(int userId)
        {
            Dispatcher.Invoke(() =>
            {
                var user = _onlineUsers.FirstOrDefault(u => u.Id == userId);
                if (user != null)
                {
                    _onlineUsers.Remove(user);
                    UpdateUserCount();
                    // Silent user departure - no notification shown
                    System.Diagnostics.Debug.WriteLine($"🔇 Silent user departure: {user.Username}");
                }
            });
        }

        private void OnMessageReceived(SafeLink.Models.Message message)
        {
            Dispatcher.Invoke(() =>
            {
                if (_activeChatUser?.Id == message.FromUserId)
                {
                    AddMessageToChat(message);
                    MessagesScrollViewer.ScrollToBottom();
                }
            });
        }

        private void OnConnectionStatusChanged(bool isConnected)
        {
            Dispatcher.Invoke(() =>
            {
                if (isConnected)
                {
                    ConnectionIndicator.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x00, 0xD4, 0xAA));
                    ConnectionText.Text = "متصل";
                    ConnectionStatusBorder.BorderBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x1D, 0xE9, 0xB6));
                }
                else
                {
                    ConnectionIndicator.Fill = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0x6B, 0x6B));
                    ConnectionText.Text = "مقطوع الاتصال";
                    ConnectionStatusBorder.BorderBrush = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0x6B, 0x6B));
                }
            });
        }

        private void ShowConnectionStatusMessage(bool isConnected)
        {
            try
            {
                _isConnected = isConnected;

                // Silent connection status updates - no user notifications
                if (isConnected)
                {
                    System.Diagnostics.Debug.WriteLine("🔇 Silent network connection established");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("🔇 Silent network disconnection");
                }

                // Update system tray menu to reflect connection status
                UpdateSystemTrayMenu();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in silent connection status update: {ex.Message}");
            }
        }

        // Attachment handling methods
        private void AttachmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "اختر الملفات للإرسال",
                    Multiselect = true,
                    Filter = CreateAttachmentFilter()
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var files = openFileDialog.FileNames.ToList();
                    _ = ProcessAttachmentsAsync(files);
                }
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في فتح نافذة اختيار الملفات: {ex.Message}", NotificationType.Error);
            }
        }

        private string CreateAttachmentFilter()
        {
            var filters = new List<string>
            {
                "جميع الملفات المدعومة|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.pdf;*.doc;*.docx;*.txt;*.mp3;*.mp4;*.zip",
                "الصور|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.webp",
                "المستندات|*.pdf;*.doc;*.docx;*.txt;*.rtf",
                "الصوت|*.mp3;*.wav;*.flac;*.aac",
                "الفيديو|*.mp4;*.avi;*.mkv;*.mov",
                "المحفوظات|*.zip;*.rar;*.7z",
                "جميع الملفات|*.*"
            };

            return string.Join("|", filters);
        }

        private void ChatDragDropArea_FilesDropped(object sender, Controls.FilesDroppedEventArgs e)
        {
            _ = ProcessAttachmentsAsync(e.FilePaths);
        }

        private void ChatDragDropArea_ErrorOccurred(object sender, string e)
        {
            ShowNotification($"خطأ في معالجة الملفات: {e}", NotificationType.Error);
        }

        private async Task ProcessAttachmentsAsync(List<string> filePaths)
        {
            try
            {
                var validFiles = new List<string>();
                var errors = new List<string>();

                foreach (var filePath in filePaths)
                {
                    var validation = await FileManager.ValidateFileAsync(filePath);
                    if (validation.IsValid)
                    {
                        validFiles.Add(filePath);
                    }
                    else
                    {
                        errors.Add($"{System.IO.Path.GetFileName(filePath)}: {validation.ErrorMessage}");
                    }
                }

                // Show errors if any
                if (errors.Any())
                {
                    var errorMessage = "بعض الملفات لم يتم قبولها:\n" + string.Join("\n", errors);
                    ShowNotification(errorMessage, NotificationType.Warning);
                }

                // Add valid files to pending attachments
                foreach (var validFile in validFiles)
                {
                    if (!_pendingAttachments.Contains(validFile))
                    {
                        _pendingAttachments.Add(validFile);
                        AddAttachmentPreview(validFile);
                    }
                }

                UpdateAttachmentsArea();
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في معالجة المرفقات: {ex.Message}", NotificationType.Error);
            }
        }

        private void AddAttachmentPreview(string filePath)
        {
            try
            {
                var preview = new Controls.AttachmentPreview();
                preview.SetPendingFile(filePath);
                preview.RemoveRequested += AttachmentPreview_RemoveRequested;
                preview.ErrorOccurred += (s, e) => ShowNotification(e, NotificationType.Error);

                AttachmentsPanel.Children.Add(preview);
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في إضافة معاينة المرفق: {ex.Message}", NotificationType.Error);
            }
        }

        private void AttachmentPreview_RemoveRequested(object sender, Controls.AttachmentPreview e)
        {
            try
            {
                var filePath = e.GetFilePath();
                _pendingAttachments.Remove(filePath);
                AttachmentsPanel.Children.Remove(e);
                UpdateAttachmentsArea();
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في إزالة المرفق: {ex.Message}", NotificationType.Error);
            }
        }

        private void ClearAttachmentsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _pendingAttachments.Clear();
                AttachmentsPanel.Children.Clear();
                UpdateAttachmentsArea();
            }
            catch (Exception ex)
            {
                ShowNotification($"خطأ في مسح المرفقات: {ex.Message}", NotificationType.Error);
            }
        }

        private void UpdateAttachmentsArea()
        {
            try
            {
                var hasAttachments = _pendingAttachments.Any();
                AttachmentsArea.Visibility = hasAttachments ? Visibility.Visible : Visibility.Collapsed;
                AttachmentsCountText.Text = $"المرفقات ({_pendingAttachments.Count})";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating attachments area: {ex.Message}");
            }
        }

        private void UpdateUserCount()
        {
            if (_currentUser?.Role == UserRole.Developer)
            {
                UserCountText.Text = $"🔧 Developer Mode - {_onlineUsers.Count} users online";
            }
            else
            {
                UserCountText.Text = $"🟢 {_onlineUsers.Count} users online";
            }
        }

        private void UpdateOnlineUsersCount()
        {
            var onlineCount = _connectedUsers.Count;
            Dispatcher.Invoke(() =>
            {
                UserCountText.Text = $"● {onlineCount} online";
            });
        }

        private void ShowNotification(string message, NotificationType type)
        {
            var messageType = type switch
            {
                NotificationType.Error => CustomMessageBox.MessageBoxType.Error,
                NotificationType.Warning => CustomMessageBox.MessageBoxType.Warning,
                NotificationType.Success => CustomMessageBox.MessageBoxType.Information,
                _ => CustomMessageBox.MessageBoxType.Information
            };

            CustomMessageBox.Show(message, "SafeLink", messageType,
                CustomMessageBox.MessageBoxButtons.OK, this);
        }

        private void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            if (!_isReallyClosing)
            {
                e.Cancel = true;

                // Show custom confirmation dialog
                var confirmDialog = new Views.ConfirmExitDialog
                {
                    Owner = this
                };

                var result = confirmDialog.ShowDialog();

                if (result == true && confirmDialog.Result == true)
                {
                    ExitApplication();
                }
            }
        }

        private void MainWindow_StateChanged(object sender, EventArgs e)
        {
            // Simple state change handling
        }

        private void ClearSensitiveData()
        {
            try
            {
                // Clear any sensitive data from memory
                System.Diagnostics.Debug.WriteLine("🧹 Clearing sensitive data...");

                // Clear user list
                _connectedUsers?.Clear();

                // Clear any cached passwords or tokens
                // Add any other sensitive data clearing here

                System.Diagnostics.Debug.WriteLine("✅ Sensitive data cleared");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing sensitive data: {ex.Message}");
            }
        }



        private void OnUserWentOffline(string username)
        {
            Dispatcher.Invoke(() =>
            {
                var userToRemove = _connectedUsers.FirstOrDefault(u => u.Username == username);
                if (userToRemove != null)
                {
                    _connectedUsers.Remove(userToRemove);
                    UpdateOnlineUsersCount();

                    // Show disconnection notification
                    NotificationManager.Instance.ShowDisconnectionNotification(userToRemove);

                    System.Diagnostics.Debug.WriteLine($"🔴 User {username} went offline and removed from list");
                }

                // Also check _onlineUsers list
                var onlineUserToRemove = _onlineUsers.FirstOrDefault(u => u.Username == username);
                if (onlineUserToRemove != null)
                {
                    _onlineUsers.Remove(onlineUserToRemove);
                    UpdateUserCount();

                    // Show disconnection notification if not already shown
                    if (userToRemove == null)
                    {
                        NotificationManager.Instance.ShowDisconnectionNotification(onlineUserToRemove);
                    }
                }
            });
        }

        private void OnMessageStatusChanged(string messageId, MessageStatus status)
        {
            Dispatcher.Invoke(async () =>
            {
                try
                {
                    // Update message status in database
                    await _databaseManager.UpdateMessageStatusAsync(messageId, status);

                    // Update UI status icon
                    UpdateMessageStatusInUI(messageId, status);

                    System.Diagnostics.Debug.WriteLine($"✅ Message {messageId} status updated to {status}");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Failed to update message status: {ex.Message}");
                }
            });
        }

        private void UpdateMessageStatusInUI(string messageId, MessageStatus status)
        {
            try
            {
                // البحث عن أيقونة الحالة في الرسائل المعروضة
                foreach (Border messageContainer in MessagesPanel.Children.OfType<Border>())
                {
                    if (messageContainer.Tag?.ToString() == messageId)
                    {
                        // البحث عن أيقونة الحالة داخل الرسالة
                        var statusIcon = FindStatusIcon(messageContainer, $"StatusIcon_{messageId}");
                        if (statusIcon != null)
                        {
                            UpdateStatusIcon(statusIcon, status);
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating message status in UI: {ex.Message}");
            }
        }

        private TextBlock FindStatusIcon(DependencyObject parent, string name)
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is TextBlock textBlock && textBlock.Name == name)
                {
                    return textBlock;
                }

                var result = FindStatusIcon(child, name);
                if (result != null)
                {
                    return result;
                }
            }
            return null;
        }

        private void UpdateStatusIcon(TextBlock statusIcon, MessageStatus status)
        {
            switch (status)
            {
                case MessageStatus.Sending:
                    statusIcon.Text = "🕐";
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6));
                    statusIcon.ToolTip = "جاري الإرسال...";
                    break;
                case MessageStatus.Sent:
                    statusIcon.Text = "✓";
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6));
                    statusIcon.ToolTip = "تم الإرسال";
                    break;
                case MessageStatus.Delivered:
                    statusIcon.Text = "✓✓";
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x95, 0xA5, 0xA6));
                    statusIcon.ToolTip = "تم التسليم";
                    break;
                case MessageStatus.Read:
                    statusIcon.Text = "✓✓";
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0x00, 0x96, 0xFF));
                    statusIcon.ToolTip = "تم القراءة";
                    break;
                case MessageStatus.Failed:
                    statusIcon.Text = "❌";
                    statusIcon.Foreground = new SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0x47, 0x57));
                    statusIcon.ToolTip = "فشل الإرسال";
                    break;
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            try
            {
                // Clean up system tray icon
                if (_notifyIcon != null)
                {
                    _notifyIcon.Visible = false;
                    _notifyIcon.Dispose();
                    _notifyIcon = null;
                }

                // Clean up other resources
                if (_networkManager != null)
                {
                    _networkManager.StopDiscovery();
                }

                System.Diagnostics.Debug.WriteLine("✅ MainWindow resources cleaned up");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error cleaning up MainWindow: {ex.Message}");
            }

            base.OnClosed(e);
        }
    }

    public enum NotificationType
    {
        Info,
        Success,
        Warning,
        Error
    }

    // Converters for user list display
    public class RoleToIconConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is UserRole role)
            {
                return role switch
                {
                    UserRole.Developer => "🛡️", // درع ذهبي للمطور
                    UserRole.Admin => "🛡️", // درع للمدير
                    UserRole.Moderator => "⭐", // نجمة للمشرف
                    UserRole.User => "👤", // شخص للمستخدم العادي
                    _ => "👤"
                };
            }
            return "👤";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class RoleToArabicConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is UserRole role)
            {
                return role switch
                {
                    UserRole.Developer => "🔧 مطور النظام",
                    UserRole.Admin => "👨‍💼 مدير",
                    UserRole.Moderator => "👮‍♂️ مشرف",
                    UserRole.User => "👤 مستخدم",
                    _ => "👤 مستخدم"
                };
            }
            return "👤 مستخدم";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // Converter for role icon colors (especially golden glow for developer)
    public class RoleToIconColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is UserRole role)
            {
                return role switch
                {
                    UserRole.Developer => "#FFD700", // Golden color for developer
                    UserRole.Admin => "#FF5252", // Red for admin
                    UserRole.Moderator => "#FFB74D", // Orange for moderator
                    UserRole.User => "#0D1421", // Dark for regular user
                    _ => "#0D1421"
                };
            }
            return "#0D1421";
        }



        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
