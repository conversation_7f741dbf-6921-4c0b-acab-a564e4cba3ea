﻿#pragma checksum "..\..\..\..\Views\ReportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7D7C90A154CA84D89F8FAE55E974B0FB96DEDF94"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Views {
    
    
    /// <summary>
    /// ReportWindow
    /// </summary>
    public partial class ReportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 39 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderText;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubHeaderText;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon BasicReportIcon;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BasicReportTitle;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BasicReportDesc;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BasicReportCheckBox;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon CustomDescIcon;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomDescTitle;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomDescDesc;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox CustomDescCheckBox;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CustomDescPanel;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomDescLabel;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomDescTextBox;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendButton;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\Views\ReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/views/reportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ReportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.SubHeaderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.BasicReportIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 4:
            this.BasicReportTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.BasicReportDesc = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.BasicReportCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 98 "..\..\..\..\Views\ReportWindow.xaml"
            this.BasicReportCheckBox.Checked += new System.Windows.RoutedEventHandler(this.BasicReportCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 99 "..\..\..\..\Views\ReportWindow.xaml"
            this.BasicReportCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.BasicReportCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CustomDescIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 8:
            this.CustomDescTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.CustomDescDesc = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CustomDescCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 143 "..\..\..\..\Views\ReportWindow.xaml"
            this.CustomDescCheckBox.Checked += new System.Windows.RoutedEventHandler(this.CustomDescCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 144 "..\..\..\..\Views\ReportWindow.xaml"
            this.CustomDescCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.CustomDescCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CustomDescPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.CustomDescLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.CustomDescTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.SendButton = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\..\Views\ReportWindow.xaml"
            this.SendButton.Click += new System.Windows.RoutedEventHandler(this.SendButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 226 "..\..\..\..\Views\ReportWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

