using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using Microsoft.Win32;
using SafeLink.Core;

namespace SafeLink.Controls
{
    public partial class DragDropArea : UserControl
    {
        private Storyboard _dragEnterAnimation;
        private Storyboard _dragLeaveAnimation;
        private Storyboard _pulseAnimation;

        public event EventHandler<FilesDroppedEventArgs> FilesDropped;
        public event EventHandler<string> ErrorOccurred;

        public DragDropArea()
        {
            InitializeComponent();
            InitializeAnimations();
            
            // Make the entire area clickable
            MouseLeftButtonUp += DragDropArea_MouseLeftButtonUp;
            Cursor = Cursors.Hand;
        }

        private void InitializeAnimations()
        {
            _dragEnterAnimation = (Storyboard)Resources["DragEnterAnimation"];
            _dragLeaveAnimation = (Storyboard)Resources["DragLeaveAnimation"];
            _pulseAnimation = (Storyboard)Resources["PulseAnimation"];
        }

        private void DragDropArea_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            OpenFileDialog();
        }

        private void OpenFileDialog()
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "اختر الملفات للإرسال",
                    Multiselect = true,
                    Filter = CreateFileFilter()
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var files = openFileDialog.FileNames.ToList();
                    _ = ProcessFilesAsync(files);
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في فتح نافذة اختيار الملفات: {ex.Message}");
            }
        }

        private string CreateFileFilter()
        {
            var filters = new List<string>
            {
                "جميع الملفات المدعومة|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.pdf;*.doc;*.docx;*.txt;*.mp3;*.mp4;*.zip",
                "الصور|*.jpg;*.jpeg;*.png;*.gif;*.bmp;*.webp",
                "المستندات|*.pdf;*.doc;*.docx;*.txt;*.rtf",
                "الصوت|*.mp3;*.wav;*.flac;*.aac",
                "الفيديو|*.mp4;*.avi;*.mkv;*.mov",
                "المحفوظات|*.zip;*.rar;*.7z",
                "جميع الملفات|*.*"
            };

            return string.Join("|", filters);
        }

        private void DragDropArea_DragEnter(object sender, DragEventArgs e)
        {
            try
            {
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                {
                    e.Effects = DragDropEffects.Copy;
                    ShowDragOverState();
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في معالجة السحب: {ex.Message}");
            }
        }

        private void DragDropArea_DragLeave(object sender, DragEventArgs e)
        {
            try
            {
                ShowDefaultState();
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في معالجة ترك السحب: {ex.Message}");
            }
        }

        private void DragDropArea_DragOver(object sender, DragEventArgs e)
        {
            try
            {
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                {
                    e.Effects = DragDropEffects.Copy;
                }
                else
                {
                    e.Effects = DragDropEffects.None;
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في معالجة السحب: {ex.Message}");
            }
        }

        private async void DragDropArea_Drop(object sender, DragEventArgs e)
        {
            try
            {
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                {
                    var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                    await ProcessFilesAsync(files.ToList());
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في معالجة الملفات المسحوبة: {ex.Message}");
            }
            finally
            {
                ShowDefaultState();
            }
        }

        private async Task ProcessFilesAsync(List<string> filePaths)
        {
            try
            {
                ShowProcessingState();

                var validFiles = new List<string>();
                var errors = new List<string>();

                foreach (var filePath in filePaths)
                {
                    var validation = await FileManager.ValidateFileAsync(filePath);
                    if (validation.IsValid)
                    {
                        validFiles.Add(filePath);
                    }
                    else
                    {
                        errors.Add($"{Path.GetFileName(filePath)}: {validation.ErrorMessage}");
                    }
                }

                // Show errors if any
                if (errors.Any())
                {
                    var errorMessage = "بعض الملفات لم يتم قبولها:\n" + string.Join("\n", errors);
                    ErrorOccurred?.Invoke(this, errorMessage);
                }

                // Process valid files
                if (validFiles.Any())
                {
                    FilesDropped?.Invoke(this, new FilesDroppedEventArgs(validFiles));
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في معالجة الملفات: {ex.Message}");
            }
            finally
            {
                ShowDefaultState();
            }
        }

        private void ShowDefaultState()
        {
            Dispatcher.Invoke(() =>
            {
                DefaultContent.Visibility = Visibility.Visible;
                DragOverContent.Visibility = Visibility.Collapsed;
                ProcessingContent.Visibility = Visibility.Collapsed;
                
                _dragLeaveAnimation?.Begin();
                _pulseAnimation?.Stop();
            });
        }

        private void ShowDragOverState()
        {
            Dispatcher.Invoke(() =>
            {
                DefaultContent.Visibility = Visibility.Collapsed;
                DragOverContent.Visibility = Visibility.Visible;
                ProcessingContent.Visibility = Visibility.Collapsed;
                
                _dragEnterAnimation?.Begin();
                _pulseAnimation?.Begin();
            });
        }

        private void ShowProcessingState()
        {
            Dispatcher.Invoke(() =>
            {
                DefaultContent.Visibility = Visibility.Collapsed;
                DragOverContent.Visibility = Visibility.Collapsed;
                ProcessingContent.Visibility = Visibility.Visible;
                
                _pulseAnimation?.Stop();
            });
        }

        public void Reset()
        {
            ShowDefaultState();
        }

        public void SetProcessingText(string text)
        {
            Dispatcher.Invoke(() =>
            {
                ProcessingText.Text = text;
            });
        }
    }

    public class FilesDroppedEventArgs : EventArgs
    {
        public List<string> FilePaths { get; }

        public FilesDroppedEventArgs(List<string> filePaths)
        {
            FilePaths = filePaths ?? new List<string>();
        }
    }
}
