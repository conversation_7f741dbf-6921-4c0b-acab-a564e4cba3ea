using System;
using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using SafeLink.Core;
using SafeLink.Models;

namespace SafeLink.Controls
{
    public partial class AttachmentPreview : UserControl
    {
        private Storyboard _hoverEnterAnimation;
        private Storyboard _hoverExitAnimation;
        private string _filePath;
        private Attachment _attachment;
        private bool _isPendingAttachment;

        public event EventHandler<AttachmentPreview> RemoveRequested;
        public event EventHandler<string> ErrorOccurred;

        public AttachmentPreview()
        {
            InitializeComponent();
            InitializeAnimations();
        }

        private void InitializeAnimations()
        {
            _hoverEnterAnimation = (Storyboard)Resources["HoverEnterAnimation"];
            _hoverExitAnimation = (Storyboard)Resources["HoverExitAnimation"];
        }

        public void SetPendingFile(string filePath)
        {
            try
            {
                _filePath = filePath;
                _isPendingAttachment = true;
                
                var fileInfo = new FileInfo(filePath);
                
                // Set file info
                FileNameText.Text = fileInfo.Name;
                FileSizeText.Text = FileManager.FormatFileSize(fileInfo.Length);
                FileIcon.Text = FileManager.GetFileIcon(fileInfo.Name);
                
                // Show remove button for pending files
                RemoveButton.Visibility = Visibility.Visible;
                
                // Load image preview if it's an image
                if (FileManager.IsImageFile(fileInfo.Name))
                {
                    LoadImagePreview(filePath);
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في تحميل معاينة الملف: {ex.Message}");
            }
        }

        public void SetAttachment(Attachment attachment)
        {
            try
            {
                _attachment = attachment;
                _isPendingAttachment = false;
                _filePath = FileManager.GetAttachmentPath(attachment.FileName);
                
                // Set file info
                FileNameText.Text = attachment.OriginalFileName;
                FileSizeText.Text = attachment.FormattedSize;
                FileIcon.Text = attachment.FileIcon;
                
                // Hide remove button for sent attachments
                RemoveButton.Visibility = Visibility.Collapsed;
                
                // Load image preview if it's an image
                if (attachment.IsImage && File.Exists(_filePath))
                {
                    LoadImagePreview(_filePath);
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في تحميل معاينة المرفق: {ex.Message}");
            }
        }

        private void LoadImagePreview(string imagePath)
        {
            try
            {
                if (!File.Exists(imagePath)) return;

                var bitmap = new BitmapImage();
                bitmap.BeginInit();
                bitmap.UriSource = new Uri(imagePath);
                bitmap.DecodePixelWidth = 60; // Optimize for small preview
                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();
                bitmap.Freeze();

                ImagePreview.Source = bitmap;
                ImagePreview.Visibility = Visibility.Visible;
                FileIcon.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading image preview: {ex.Message}");
                // Keep the file icon if image loading fails
            }
        }

        private void MainBorder_MouseEnter(object sender, MouseEventArgs e)
        {
            _hoverEnterAnimation?.Begin();
        }

        private void MainBorder_MouseLeave(object sender, MouseEventArgs e)
        {
            _hoverExitAnimation?.Begin();
        }

        private void MainBorder_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            OpenFile();
        }

        private void DownloadButton_Click(object sender, RoutedEventArgs e)
        {
            DownloadFile();
        }

        private void OpenButton_Click(object sender, RoutedEventArgs e)
        {
            OpenFile();
        }

        private void RemoveButton_Click(object sender, RoutedEventArgs e)
        {
            RemoveRequested?.Invoke(this, this);
        }

        private void DownloadFile()
        {
            try
            {
                if (!File.Exists(_filePath))
                {
                    ErrorOccurred?.Invoke(this, "الملف غير موجود");
                    return;
                }

                var saveFileDialog = new SaveFileDialog
                {
                    FileName = _isPendingAttachment ? Path.GetFileName(_filePath) : _attachment?.OriginalFileName,
                    Filter = "جميع الملفات|*.*"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    File.Copy(_filePath, saveFileDialog.FileName, true);
                    
                    // Show success message
                    MessageBox.Show("تم تحميل الملف بنجاح", "تحميل مكتمل", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في تحميل الملف: {ex.Message}");
            }
        }

        private void OpenFile()
        {
            try
            {
                if (!File.Exists(_filePath))
                {
                    ErrorOccurred?.Invoke(this, "الملف غير موجود");
                    return;
                }

                // Open file with default application
                var startInfo = new ProcessStartInfo
                {
                    FileName = _filePath,
                    UseShellExecute = true
                };

                Process.Start(startInfo);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"خطأ في فتح الملف: {ex.Message}");
            }
        }

        public string GetFilePath()
        {
            return _filePath;
        }

        public Attachment GetAttachment()
        {
            return _attachment;
        }

        public bool IsPendingAttachment()
        {
            return _isPendingAttachment;
        }
    }
}
