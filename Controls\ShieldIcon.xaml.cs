using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace SafeLink.Controls
{
    public partial class ShieldIcon : UserControl
    {
        public static readonly DependencyProperty ShieldColorProperty =
            DependencyProperty.Register("ShieldColor", typeof(Brush), typeof(ShieldIcon),
                new PropertyMetadata(Brushes.White));

        public static readonly DependencyProperty StrokeColorProperty =
            DependencyProperty.Register("StrokeColor", typeof(Brush), typeof(ShieldIcon),
                new PropertyMetadata(Brushes.Transparent));

        public static readonly DependencyProperty StrokeThicknessProperty =
            DependencyProperty.Register("StrokeThickness", typeof(double), typeof(ShieldIcon),
                new PropertyMetadata(0.0));

        public Brush ShieldColor
        {
            get { return (Brush)GetValue(ShieldColorProperty); }
            set { SetValue(ShieldColorProperty, value); }
        }

        public Brush StrokeColor
        {
            get { return (Brush)GetValue(StrokeColorProperty); }
            set { SetValue(StrokeColorProperty, value); }
        }

        public double StrokeThickness
        {
            get { return (double)GetValue(StrokeThicknessProperty); }
            set { SetValue(StrokeThicknessProperty, value); }
        }

        public ShieldIcon()
        {
            InitializeComponent();
        }
    }
}
