using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using SafeLink.Models;

namespace SafeLink.Core
{
    public class NetworkManager : IDisposable
    {
        private UdpClient _udpClient;
        private TcpListener _tcpListener;
        private bool _isRunning;
        private CancellationTokenSource _cancellationTokenSource;
        private int _currentPort = 8888; // Dynamic port
        private readonly int _basePort = 8888;
        private readonly int _maxPortRange = 50; // Try ports 8888-8938
        private readonly Dictionary<string, IPEndPoint> _discoveredPeers;
        private readonly Dictionary<string, User> _connectedUsers; // Track connected users
        private readonly Dictionary<string, DateTime> _userLastSeen;
        private readonly Dictionary<string, string> _pendingMessages; // MessageId -> Username
        private readonly Dictionary<int, DateTime> _activeNetworks; // Track active networks by port
        private bool _isMeshLeader = false; // Is this device the mesh leader?
        private int _meshNetworkPort = 0; // The established mesh network port
        private readonly Dictionary<int, List<string>> _portUsers; // Track users per port

        public event Action<User> UserJoined;
        // public event Action<int> UserLeft; // Reserved for future use
        public event Action<string> UserWentOffline; // Username
        public event Action<Message> MessageReceived;
        public event Action<bool> ConnectionStatusChanged;
        public event Action<string, MessageStatus> MessageStatusChanged; // MessageId, Status

        // Properties to expose network information
        public int CurrentPort => _currentPort;
        public bool IsMeshLeader => _isMeshLeader;
        public int MeshNetworkPort => _meshNetworkPort;
        public bool IsRunning => _isRunning;

        public NetworkManager()
        {
            _discoveredPeers = new Dictionary<string, IPEndPoint>();
            _connectedUsers = new Dictionary<string, User>();
            _userLastSeen = new Dictionary<string, DateTime>();
            _pendingMessages = new Dictionary<string, string>();
            _activeNetworks = new Dictionary<int, DateTime>();
            _portUsers = new Dictionary<int, List<string>>();
        }

        public void Initialize()
        {
            _cancellationTokenSource = new CancellationTokenSource();
        }

        public async Task StartDiscovery()
        {
            if (_isRunning)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ Network discovery already running - ignoring duplicate start request");
                return;
            }

            // Check if discovery is enabled in settings
            if (!SafeLink.Properties.Settings.Default.AutoDiscovery)
            {
                System.Diagnostics.Debug.WriteLine("⚠️ Auto Discovery is disabled in settings");
                return;
            }

            _isRunning = true;

            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 Starting SafeLink Network Discovery...");
                System.Diagnostics.Debug.WriteLine($"📊 Settings - AutoDiscovery: {SafeLink.Properties.Settings.Default.AutoDiscovery}");
                System.Diagnostics.Debug.WriteLine($"📊 Settings - BroadcastPresence: {SafeLink.Properties.Settings.Default.BroadcastPresence}");
                System.Diagnostics.Debug.WriteLine($"📊 Settings - NetworkPort: {SafeLink.Properties.Settings.Default.NetworkPort}");

                // إضافة قواعد جدار الحماية (Inbound + Outbound)
                System.Diagnostics.Debug.WriteLine("🔥 Adding firewall rules (Inbound + Outbound)...");
                var firewallSuccess = await FirewallManager.AddFirewallRulesAsync();
                if (firewallSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("✅ Firewall rules added successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ Some firewall rules failed - continuing anyway");
                }

                // First, scan for existing networks
                var existingNetwork = await ScanForExistingNetworks();

                if (existingNetwork.HasValue)
                {
                    // Join existing network
                    _currentPort = existingNetwork.Value;
                    _meshNetworkPort = existingNetwork.Value;
                    _isMeshLeader = false;
                    System.Diagnostics.Debug.WriteLine($"🔗 Joining existing mesh network on port {_currentPort}");
                }
                else
                {
                    // Create new network - become mesh leader
                    _currentPort = await FindAvailablePort();
                    _meshNetworkPort = _currentPort;
                    _isMeshLeader = true;
                    System.Diagnostics.Debug.WriteLine($"👑 Creating new mesh network as leader on port {_currentPort}");
                }

                // Start UDP discovery with SO_REUSEADDR
                _udpClient = new UdpClient();
                _udpClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                _udpClient.Client.Bind(new IPEndPoint(IPAddress.Any, _currentPort));
                _udpClient.EnableBroadcast = true;
                System.Diagnostics.Debug.WriteLine($"✅ UDP Client started on port {_currentPort} (Mesh Leader: {_isMeshLeader})");

                // Start TCP listener for direct connections
                var tcpPort = _currentPort + 1; // TCP port is UDP port + 1
                _tcpListener = new TcpListener(IPAddress.Any, tcpPort);
                _tcpListener.Start();
                System.Diagnostics.Debug.WriteLine($"✅ TCP Listener started on port {tcpPort}");

                // Update connection status
                ConnectionStatusChanged?.Invoke(true);

                // Start background tasks
                _ = Task.Run(DiscoveryLoop, _cancellationTokenSource.Token);
                _ = Task.Run(ListenForConnections, _cancellationTokenSource.Token);
                _ = Task.Run(BroadcastPresence, _cancellationTokenSource.Token);
                _ = Task.Run(MonitorUserStatus, _cancellationTokenSource.Token);

                // Add immediate broadcast to announce presence
                _ = Task.Run(async () =>
                {
                    await Task.Delay(1000); // Wait 1 second then broadcast
                    await BroadcastImmediately();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Failed to start network discovery: {ex.Message}");
                ConnectionStatusChanged?.Invoke(false);
                throw;
            }
        }

        public void StopDiscovery()
        {
            if (!_isRunning) return;

            _isRunning = false;
            _cancellationTokenSource?.Cancel();

            try
            {
                _udpClient?.Close();
                _tcpListener?.Stop();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error stopping network discovery: {ex.Message}");
            }

            ConnectionStatusChanged?.Invoke(false);
        }

        private async Task<int?> ScanForExistingNetworks()
        {
            System.Diagnostics.Debug.WriteLine("🔍 Scanning for existing SafeLink networks...");

            var foundNetworks = new Dictionary<int, int>(); // Port -> UserCount

            // Scan port range for active networks
            for (int port = _basePort; port < _basePort + _maxPortRange; port++)
            {
                try
                {
                    using var scanClient = new UdpClient();
                    scanClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                    scanClient.Client.ReceiveTimeout = 1000; // 1 second timeout

                    // Send discovery probe
                    var probeMessage = "SAFELINK_NETWORK_PROBE";
                    var probeData = Encoding.UTF8.GetBytes(probeMessage);
                    var broadcastEndpoint = new IPEndPoint(IPAddress.Broadcast, port);

                    await scanClient.SendAsync(probeData, probeData.Length, broadcastEndpoint);

                    // Listen for responses for a short time
                    var startTime = DateTime.Now;
                    var userCount = 0;

                    while ((DateTime.Now - startTime).TotalMilliseconds < 500) // 500ms scan per port
                    {
                        try
                        {
                            var result = await scanClient.ReceiveAsync();
                            var response = Encoding.UTF8.GetString(result.Buffer);

                            if (response.StartsWith("SAFELINK_DISCOVERY:") || response.StartsWith("SAFELINK_NETWORK_RESPONSE:"))
                            {
                                userCount++;
                            }
                        }
                        catch (SocketException)
                        {
                            break; // Timeout or no more data
                        }
                    }

                    if (userCount > 0)
                    {
                        foundNetworks[port] = userCount;
                        System.Diagnostics.Debug.WriteLine($"📡 Found network on port {port} with {userCount} users");
                    }
                }
                catch (Exception)
                {
                    // Silent scan - don't log every port failure
                    continue;
                }
            }

            // Return the port with the most users (most active network)
            if (foundNetworks.Any())
            {
                var bestNetwork = foundNetworks.OrderByDescending(x => x.Value).First();
                System.Diagnostics.Debug.WriteLine($"🎯 Best network found: Port {bestNetwork.Key} with {bestNetwork.Value} users");
                return bestNetwork.Key;
            }

            System.Diagnostics.Debug.WriteLine("🔍 No existing networks found - will create new one");
            return null;
        }

        private async Task<int> FindAvailablePort()
        {
            System.Diagnostics.Debug.WriteLine("🔍 Finding available port for new network...");

            for (int port = _basePort; port < _basePort + _maxPortRange; port++)
            {
                try
                {
                    // Test if port is available
                    using var testClient = new UdpClient();
                    testClient.Client.SetSocketOption(SocketOptionLevel.Socket, SocketOptionName.ReuseAddress, true);
                    testClient.Client.Bind(new IPEndPoint(IPAddress.Any, port));

                    System.Diagnostics.Debug.WriteLine($"✅ Port {port} is available for new network");
                    return port;
                }
                catch (SocketException)
                {
                    // Port is busy, try next one
                    continue;
                }
            }

            // Fallback to base port if all are busy
            System.Diagnostics.Debug.WriteLine($"⚠️ All ports busy, using base port {_basePort}");
            return _basePort;
        }

        private async Task MigrateToNetwork(int targetPort)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 Migrating from port {_currentPort} to {targetPort}");

                // Stop current network operations
                StopDiscovery();

                // Update network settings
                _currentPort = targetPort;
                _meshNetworkPort = targetPort;
                _isMeshLeader = false; // We're joining, not leading

                // Restart with new port
                await StartDiscovery();

                System.Diagnostics.Debug.WriteLine($"✅ Successfully migrated to network on port {_currentPort}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Failed to migrate to network: {ex.Message}");

                // Fallback to original behavior
                _currentPort = _basePort;
                _meshNetworkPort = _basePort;
                _isMeshLeader = true;

                try
                {
                    await StartDiscovery();
                }
                catch
                {
                    // Last resort - just log the error
                    System.Diagnostics.Debug.WriteLine("❌ Failed to restart network after migration failure");
                }
            }
        }

        private async Task DiscoveryLoop()
        {
            while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    if (_udpClient == null) break;

                    var result = await _udpClient.ReceiveAsync();
                    var message = Encoding.UTF8.GetString(result.Buffer);

                    if (message.StartsWith("SAFELINK_DISCOVERY:"))
                    {
                        System.Diagnostics.Debug.WriteLine($"📨 Received discovery message from {result.RemoteEndPoint}");
                        var userData = message.Substring("SAFELINK_DISCOVERY:".Length);

                        try
                        {
                            var user = JsonConvert.DeserializeObject<User>(userData);

                            if (user != null && !string.IsNullOrEmpty(user.Username))
                            {
                                user.LastSeen = DateTime.Now;
                                user.IsOnline = true;

                                // Check if this user is on a different port - join their network if they're the leader
                                if (user.IsMeshLeader && user.Port != _currentPort && user.Port > 0)
                                {
                                    System.Diagnostics.Debug.WriteLine($"👑 Found mesh leader {user.Username} on port {user.Port} - considering migration");

                                    // If we're not a leader or their network is more established, consider joining
                                    if (!_isMeshLeader || user.Port < _currentPort)
                                    {
                                        _ = Task.Run(async () => await MigrateToNetwork(user.Port));
                                        return; // Restart discovery loop after migration
                                    }
                                }

                                var endPoint = result.RemoteEndPoint;
                                _discoveredPeers[user.Username] = endPoint;
                                _connectedUsers[user.Username] = user;
                                _userLastSeen[user.Username] = DateTime.Now;

                                System.Diagnostics.Debug.WriteLine($"👤 Discovered user: {user.Username} at {endPoint} (Port: {user.Port}, Leader: {user.IsMeshLeader})");
                                System.Diagnostics.Debug.WriteLine($"🔍 Total discovered users: {_connectedUsers.Count}");
                                UserJoined?.Invoke(user);
                            }
                        }
                        catch (JsonException ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ JSON parsing error: {ex.Message}");
                        }
                    }
                    else if (message.StartsWith("SAFELINK_MESSAGE:"))
                    {
                        var messageData = message.Substring("SAFELINK_MESSAGE:".Length);

                        try
                        {
                            var receivedMessage = JsonConvert.DeserializeObject<Message>(messageData);

                            if (receivedMessage != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"📨 Received message from {receivedMessage.SenderName}: {receivedMessage.Content}");
                                MessageReceived?.Invoke(receivedMessage);

                                // Send delivery confirmation
                                _ = Task.Run(async () => await SendDeliveryConfirmation(receivedMessage.MessageId, receivedMessage.SenderName));
                            }
                        }
                        catch (JsonException ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ Message JSON parsing error: {ex.Message}");
                        }
                    }
                    else if (message.StartsWith("SAFELINK_DELIVERY:"))
                    {
                        var confirmationData = message.Substring("SAFELINK_DELIVERY:".Length);
                        try
                        {
                            var parts = confirmationData.Split('|');
                            if (parts.Length >= 2)
                            {
                                var messageId = parts[0];
                                var status = (MessageStatus)int.Parse(parts[1]);
                                MessageStatusChanged?.Invoke(messageId, status);
                            }
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ Delivery confirmation parsing error: {ex.Message}");
                        }
                    }
                }
                catch (ObjectDisposedException)
                {
                    System.Diagnostics.Debug.WriteLine("🔴 UDP client disposed, stopping discovery loop");
                    break;
                }
                catch (SocketException ex)
                {
                    System.Diagnostics.Debug.WriteLine($"🔴 Socket error: {ex.Message}");
                    await Task.Delay(2000, _cancellationTokenSource.Token);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"🔴 Discovery loop error: {ex.Message}");
                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
            }
        }

        private async Task BroadcastPresence()
        {
            while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    // Check if broadcasting is enabled in settings
                    if (SafeLink.Properties.Settings.Default.BroadcastPresence)
                    {
                        await BroadcastImmediately();
                        System.Diagnostics.Debug.WriteLine("📡 Presence broadcast sent");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ Broadcast Presence is disabled in settings");
                    }

                    await Task.Delay(3000, _cancellationTokenSource.Token); // Broadcast every 3 seconds
                }
                catch (ObjectDisposedException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Broadcast error: {ex.Message}");
                    await Task.Delay(5000, _cancellationTokenSource.Token);
                }
            }
        }

        private async Task BroadcastImmediately()
        {
            try
            {
                var currentUser = GetCurrentUser();
                if (currentUser == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ Cannot broadcast - no current user set");
                    System.Diagnostics.Debug.WriteLine("🔍 Debug: Checking if user is logged in...");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"🔍 Broadcasting user: {currentUser.Username} (DeviceId: {currentUser.DeviceId})");

                // Add mesh network information to user data
                currentUser.Port = _currentPort;
                currentUser.IsMeshLeader = _isMeshLeader;
                currentUser.MeshNetworkPort = _meshNetworkPort;
                currentUser.LastSeen = DateTime.Now;
                currentUser.IsOnline = true;

                var userData = JsonConvert.SerializeObject(currentUser);
                var message = $"SAFELINK_DISCOVERY:{userData}";
                var data = Encoding.UTF8.GetBytes(message);

                System.Diagnostics.Debug.WriteLine($"📡 Broadcasting user: {currentUser.Username} on port {_currentPort} (Leader: {_isMeshLeader})");
                System.Diagnostics.Debug.WriteLine($"🔍 Message size: {data.Length} bytes");
                System.Diagnostics.Debug.WriteLine($"🔍 UDP Client status: {(_udpClient != null ? "Active" : "Null")}");

                // Get all network interfaces and broadcast on each
                var broadcastAddresses = new List<IPEndPoint>();

                // Standard broadcast addresses
                broadcastAddresses.Add(new IPEndPoint(IPAddress.Broadcast, _currentPort));
                broadcastAddresses.Add(new IPEndPoint(IPAddress.Parse("***************"), _currentPort));

                // Get local network interfaces
                try
                {
                    var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces()
                        .Where(ni => ni.OperationalStatus == OperationalStatus.Up &&
                                   ni.NetworkInterfaceType != NetworkInterfaceType.Loopback);

                    foreach (var ni in networkInterfaces)
                    {
                        var ipProps = ni.GetIPProperties();
                        foreach (var addr in ipProps.UnicastAddresses)
                        {
                            if (addr.Address.AddressFamily == AddressFamily.InterNetwork)
                            {
                                var ip = addr.Address;
                                var mask = addr.IPv4Mask;

                                if (ip != null && mask != null)
                                {
                                    var broadcast = GetBroadcastAddress(ip, mask);
                                    if (broadcast != null)
                                    {
                                        broadcastAddresses.Add(new IPEndPoint(broadcast, _currentPort));
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ Error getting network interfaces: {ex.Message}");
                }

                // Also try common local network ranges
                var commonRanges = new[]
                {
                    "*************", "*************", "*************",
                    "**********", "**************"
                };

                foreach (var range in commonRanges)
                {
                    try
                    {
                        broadcastAddresses.Add(new IPEndPoint(IPAddress.Parse(range), _currentPort));
                    }
                    catch { /* Ignore invalid addresses */ }
                }

                // Remove duplicates
                broadcastAddresses = broadcastAddresses.Distinct().ToList();

                foreach (var endpoint in broadcastAddresses)
                {
                    try
                    {
                        await _udpClient.SendAsync(data, data.Length, endpoint);
                        System.Diagnostics.Debug.WriteLine($"📡 Broadcasting to {endpoint}: {currentUser.Username}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ Failed to broadcast to {endpoint}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ BroadcastImmediately error: {ex.Message}");
            }
        }

        private IPAddress GetBroadcastAddress(IPAddress address, IPAddress mask)
        {
            try
            {
                var addressBytes = address.GetAddressBytes();
                var maskBytes = mask.GetAddressBytes();
                var broadcastBytes = new byte[4];

                for (int i = 0; i < 4; i++)
                {
                    broadcastBytes[i] = (byte)(addressBytes[i] | (~maskBytes[i] & 0xFF));
                }

                return new IPAddress(broadcastBytes);
            }
            catch
            {
                return null;
            }
        }

        private async Task ListenForConnections()
        {
            while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var tcpClient = await _tcpListener.AcceptTcpClientAsync();
                    _ = Task.Run(() => HandleTcpClient(tcpClient), _cancellationTokenSource.Token);
                }
                catch (ObjectDisposedException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"TCP listener error: {ex.Message}");
                }
            }
        }

        private async Task HandleTcpClient(TcpClient client)
        {
            try
            {
                using (client)
                using (var stream = client.GetStream())
                {
                    var buffer = new byte[4096];
                    var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);

                    if (bytesRead > 0)
                    {
                        var message = Encoding.UTF8.GetString(buffer, 0, bytesRead);

                        if (message.StartsWith("SAFELINK_MESSAGE:"))
                        {
                            var messageData = message.Substring("SAFELINK_MESSAGE:".Length);
                            var receivedMessage = JsonConvert.DeserializeObject<Message>(messageData);

                            if (receivedMessage != null)
                            {
                                MessageReceived?.Invoke(receivedMessage);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"TCP client handling error: {ex.Message}");
            }
        }

        public async Task SendMessageAsync(Message message)
        {
            try
            {
                if (_udpClient == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ UDP client not initialized - attempting to restart network");
                    await StartDiscovery();
                    if (_udpClient == null)
                    {
                        message.Status = MessageStatus.Failed;
                        MessageStatusChanged?.Invoke(message.MessageId, MessageStatus.Failed);
                        return;
                    }
                }

                // Validate message content
                if (string.IsNullOrEmpty(message.Content) && !message.HasAttachments)
                {
                    System.Diagnostics.Debug.WriteLine("❌ Message has no content or attachment");
                    message.Status = MessageStatus.Failed;
                    MessageStatusChanged?.Invoke(message.MessageId, MessageStatus.Failed);
                    return;
                }

                // Mark message as sending initially
                message.Status = MessageStatus.Sending;
                MessageStatusChanged?.Invoke(message.MessageId, MessageStatus.Sending);

                var messageData = JsonConvert.SerializeObject(message);
                var fullMessage = $"SAFELINK_MESSAGE:{messageData}";
                var data = Encoding.UTF8.GetBytes(fullMessage);

                bool messageSent = false;

                // Try UDP broadcast first
                try
                {
                    var broadcastEndPoint = new IPEndPoint(IPAddress.Broadcast, _currentPort);
                    await _udpClient.SendAsync(data, data.Length, broadcastEndPoint);
                    messageSent = true;
                    System.Diagnostics.Debug.WriteLine($"📤 UDP broadcast sent: {message.MessageId} to port {_currentPort}");

                    // Also try local network ranges
                    var localIPs = new[] { "*************", "*************", "**********" };
                    foreach (var ip in localIPs)
                    {
                        try
                        {
                            var localEndPoint = new IPEndPoint(IPAddress.Parse(ip), _currentPort);
                            await _udpClient.SendAsync(data, data.Length, localEndPoint);
                            System.Diagnostics.Debug.WriteLine($"📤 Local broadcast sent to {ip}");
                        }
                        catch (Exception localEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ Local broadcast to {ip} failed: {localEx.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ UDP broadcast failed: {ex.Message}");
                }

                // Also try direct TCP connections to known peers
                foreach (var peer in _discoveredPeers.Values)
                {
                    try
                    {
                        using var tcpClient = new TcpClient();
                        tcpClient.ReceiveTimeout = 3000;
                        tcpClient.SendTimeout = 3000;
                        await tcpClient.ConnectAsync(peer.Address, _currentPort + 1);
                        using var stream = tcpClient.GetStream();
                        await stream.WriteAsync(data, 0, data.Length);
                        messageSent = true;
                        System.Diagnostics.Debug.WriteLine($"📤 TCP sent to {peer.Address}: {message.MessageId}");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ TCP to {peer.Address} failed: {ex.Message}");
                    }
                }

                // Update message status based on success
                if (messageSent)
                {
                    message.Status = MessageStatus.Sent;
                    MessageStatusChanged?.Invoke(message.MessageId, MessageStatus.Sent);
                    System.Diagnostics.Debug.WriteLine($"✅ Message sent successfully: {message.MessageId}");
                }
                else
                {
                    message.Status = MessageStatus.Failed;
                    MessageStatusChanged?.Invoke(message.MessageId, MessageStatus.Failed);
                    System.Diagnostics.Debug.WriteLine($"❌ Message failed to send: {message.MessageId}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ SendMessageAsync error: {ex.Message}");
                message.Status = MessageStatus.Failed;
                MessageStatusChanged?.Invoke(message.MessageId, MessageStatus.Failed);
            }
        }

        public async Task SendProblemReportAsync(int userId)
        {
            try
            {
                // Take screenshot
                var screenshot = await ScreenshotManager.TakeScreenshotAsync();

                // Create problem report
                var report = new ProblemReport
                {
                    UserId = userId,
                    Timestamp = DateTime.Now,
                    UserAgent = Environment.OSVersion.ToString(),
                    Description = "User-reported problem with screenshot"
                };

                // Save screenshot
                var screenshotPath = await SaveScreenshotAsync(screenshot, userId);
                report.ScreenshotPath = screenshotPath;

                // Send to developer (broadcast for now)
                var reportData = JsonConvert.SerializeObject(report);
                var message = $"SAFELINK_REPORT:{reportData}";
                var data = Encoding.UTF8.GetBytes(message);

                var broadcastEndPoint = new IPEndPoint(IPAddress.Broadcast, _currentPort);
                await _udpClient.SendAsync(data, data.Length, broadcastEndPoint);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to send problem report: {ex.Message}");
                throw;
            }
        }

        public async Task SendSystemStatusReportAsync(int userId)
        {
            try
            {
                // Create system status report
                var report = new ProblemReport
                {
                    UserId = userId,
                    Timestamp = DateTime.Now,
                    UserAgent = Environment.OSVersion.ToString(),
                    Description = $"System Status Report:\n" +
                                $"OS: {Environment.OSVersion}\n" +
                                $"Machine: {Environment.MachineName}\n" +
                                $"User: {Environment.UserName}\n" +
                                $"Runtime: {Environment.Version}\n" +
                                $"Memory: {GC.GetTotalMemory(false) / 1024 / 1024} MB\n" +
                                $"Processors: {Environment.ProcessorCount}\n" +
                                $"Uptime: {Environment.TickCount / 1000 / 60} minutes"
                };

                // Send to developer (broadcast for now)
                var reportData = JsonConvert.SerializeObject(report);
                var message = $"SAFELINK_SYSTEM_REPORT:{reportData}";
                var data = Encoding.UTF8.GetBytes(message);

                var broadcastEndPoint = new IPEndPoint(IPAddress.Broadcast, _currentPort);
                await _udpClient.SendAsync(data, data.Length, broadcastEndPoint);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to send system status report: {ex.Message}");
                throw;
            }
        }

        public async Task SendCustomMessageReportAsync(int userId, string customMessage)
        {
            try
            {
                // Create custom message report
                var report = new ProblemReport
                {
                    UserId = userId,
                    Timestamp = DateTime.Now,
                    UserAgent = Environment.OSVersion.ToString(),
                    Description = $"Custom Message Report:\n{customMessage}"
                };

                // Send to developer (broadcast for now)
                var reportData = JsonConvert.SerializeObject(report);
                var message = $"SAFELINK_CUSTOM_REPORT:{reportData}";
                var data = Encoding.UTF8.GetBytes(message);

                var broadcastEndPoint = new IPEndPoint(IPAddress.Broadcast, _currentPort);
                await _udpClient.SendAsync(data, data.Length, broadcastEndPoint);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to send custom message report: {ex.Message}");
                throw;
            }
        }

        private async Task<string> SaveScreenshotAsync(byte[] screenshot, int userId)
        {
            var screenshotsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Screenshots");
            if (!Directory.Exists(screenshotsPath))
            {
                Directory.CreateDirectory(screenshotsPath);
            }

            var filename = $"report_{userId}_{DateTime.Now:yyyyMMdd_HHmmss}.png";
            var filePath = Path.Combine(screenshotsPath, filename);

            await File.WriteAllBytesAsync(filePath, screenshot);
            return filePath;
        }

        private User _currentUser;

        public void SetCurrentUser(User user)
        {
            _currentUser = user;
        }

        private User GetCurrentUser()
        {
            return _currentUser;
        }

        private IPAddress GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        return ip;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ Failed to get local IP: {ex.Message}");
            }
            return null;
        }

        private IPAddress GetSubnetBroadcast(IPAddress localIP)
        {
            try
            {
                // Simple subnet calculation for common networks
                var bytes = localIP.GetAddressBytes();

                // Assume /24 subnet (*************)
                bytes[3] = 255;

                return new IPAddress(bytes);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ Failed to calculate subnet broadcast: {ex.Message}");
                return null;
            }
        }

        private async Task SendDeliveryConfirmation(string messageId, string senderName)
        {
            try
            {
                var confirmationMessage = $"SAFELINK_DELIVERY:{messageId}|{(int)MessageStatus.Delivered}";
                var data = Encoding.UTF8.GetBytes(confirmationMessage);

                var broadcastEndPoint = new IPEndPoint(IPAddress.Broadcast, _currentPort);
                await _udpClient.SendAsync(data, data.Length, broadcastEndPoint);

                System.Diagnostics.Debug.WriteLine($"✅ Sent delivery confirmation for message {messageId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Failed to send delivery confirmation: {ex.Message}");
            }
        }

        private async Task MonitorUserStatus()
        {
            while (_isRunning && !_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    var now = DateTime.Now;
                    var offlineUsers = new List<string>();

                    foreach (var kvp in _userLastSeen.ToList())
                    {
                        if (now - kvp.Value > TimeSpan.FromSeconds(10)) // 10 seconds timeout
                        {
                            offlineUsers.Add(kvp.Key);
                        }
                    }

                    foreach (var username in offlineUsers)
                    {
                        _userLastSeen.Remove(username);
                        _discoveredPeers.Remove(username);
                        UserWentOffline?.Invoke(username);
                        System.Diagnostics.Debug.WriteLine($"🔴 User went offline: {username}");
                    }

                    await Task.Delay(5000, _cancellationTokenSource.Token); // Check every 5 seconds
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Error in user status monitoring: {ex.Message}");
                    await Task.Delay(5000, _cancellationTokenSource.Token);
                }
            }
        }

        public List<User> GetConnectedUsers()
        {
            return _connectedUsers?.Values?.ToList() ?? new List<User>();
        }

        // Remote Command Execution
        public async Task<bool> SendRemoteCommandAsync(string targetDeviceId, string commandType, string command)
        {
            try
            {
                var targetUser = _connectedUsers.Values.FirstOrDefault(u => u.DeviceId == targetDeviceId);
                if (targetUser == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Target device not found: {targetDeviceId}");
                    return false;
                }

                var commandMessage = new
                {
                    Type = "RemoteCommand",
                    CommandType = commandType,
                    Command = command,
                    Timestamp = DateTime.Now,
                    SenderId = _currentUser?.DeviceId
                };

                var jsonMessage = System.Text.Json.JsonSerializer.Serialize(commandMessage);
                var messageBytes = Encoding.UTF8.GetBytes(jsonMessage);

                // Get IP address from discovered peers
                if (!_discoveredPeers.TryGetValue(targetUser.Username, out var targetEndPoint))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ IP address not found for user: {targetUser.Username}");
                    return false;
                }

                // Send via TCP for reliable delivery
                using (var tcpClient = new TcpClient())
                {
                    await tcpClient.ConnectAsync(targetEndPoint.Address, _currentPort + 1);
                    using (var stream = tcpClient.GetStream())
                    {
                        await stream.WriteAsync(messageBytes, 0, messageBytes.Length);
                        await stream.FlushAsync();
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ Remote command sent to {targetUser.Username}: {commandType}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error sending remote command: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DisconnectUserAsync(string deviceId)
        {
            try
            {
                var user = _connectedUsers.Values.FirstOrDefault(u => u.DeviceId == deviceId);
                if (user != null)
                {
                    _discoveredPeers.Remove(user.Username);
                    _connectedUsers.Remove(user.Username);

                    // Notify other users about disconnection
                    await BroadcastPresence();

                    System.Diagnostics.Debug.WriteLine($"✅ User disconnected: {user.Username}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error disconnecting user: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            StopDiscovery();
            _cancellationTokenSource?.Dispose();
        }
    }
}
