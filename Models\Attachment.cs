using System;
using System.ComponentModel.DataAnnotations;

namespace SafeLink.Models
{
    public class Attachment
    {
        [Key]
        public int Id { get; set; }
        
        [Required]
        public int MessageId { get; set; }
        
        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(255)]
        public string OriginalFileName { get; set; } = string.Empty;
        
        [Required]
        [MaxLength(100)]
        public string FileExtension { get; set; } = string.Empty;
        
        [Required]
        public long FileSize { get; set; }
        
        [MaxLength(255)]
        public string FilePath { get; set; } = string.Empty;
        
        [MaxLength(255)]
        public string FileHash { get; set; } = string.Empty;
        
        [MaxLength(100)]
        public string MimeType { get; set; } = string.Empty;
        
        public DateTime UploadedAt { get; set; } = DateTime.Now;
        
        public int UploadedBy { get; set; }
        
        public bool IsImage { get; set; }
        public bool IsVideo { get; set; }
        public bool IsAudio { get; set; }
        public bool IsDocument { get; set; }
        
        // Navigation properties
        public virtual Message Message { get; set; }
        public virtual User UploadedByUser { get; set; }
        
        // Helper properties
        public string DisplayName => string.IsNullOrEmpty(OriginalFileName) ? FileName : OriginalFileName;
        public string FileIcon => Core.FileManager.GetFileIcon(FileName);
        public string FormattedSize => Core.FileManager.FormatFileSize(FileSize);
        
        public AttachmentType GetAttachmentType()
        {
            if (IsImage) return AttachmentType.Image;
            if (IsVideo) return AttachmentType.Video;
            if (IsAudio) return AttachmentType.Audio;
            if (IsDocument) return AttachmentType.Document;
            return AttachmentType.Other;
        }
    }
    
    public enum AttachmentType
    {
        Image,
        Video,
        Audio,
        Document,
        Archive,
        Other
    }
}
