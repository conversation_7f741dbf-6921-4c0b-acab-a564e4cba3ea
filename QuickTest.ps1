# SafeLink Network Quick Test
Write-Host "🔍 SafeLink Network Quick Test" -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Cyan

# Check active UDP ports
Write-Host "`n📡 Checking UDP ports 8888-8895..." -ForegroundColor Yellow
$udpPorts = Get-NetUDPEndpoint | Where-Object {$_.LocalPort -ge 8888 -and $_.LocalPort -le 8895}
if ($udpPorts) {
    foreach ($port in $udpPorts) {
        Write-Host "✅ UDP Port $($port.LocalPort) is ACTIVE" -ForegroundColor Green
    }
} else {
    Write-Host "❌ No SafeLink UDP ports found" -ForegroundColor Red
}

# Check TCP ports
Write-Host "`n🔗 Checking TCP ports 8898-8905..." -ForegroundColor Yellow
$tcpPorts = Get-NetTCPConnection | Where-Object {$_.LocalPort -ge 8898 -and $_.LocalPort -le 8905}
if ($tcpPorts) {
    foreach ($port in $tcpPorts) {
        Write-Host "✅ TCP Port $($port.LocalPort) is ACTIVE (State: $($port.State))" -ForegroundColor Green
    }
} else {
    Write-Host "⚪ No SafeLink TCP ports found" -ForegroundColor Gray
}

# Check SafeLink processes
Write-Host "`n🚀 Checking SafeLink processes..." -ForegroundColor Yellow
$processes = Get-Process | Where-Object {$_.ProcessName -like "*SafeLink*" -or $_.ProcessName -like "*dotnet*"}
if ($processes) {
    foreach ($proc in $processes) {
        Write-Host "✅ Process: $($proc.ProcessName) (PID: $($proc.Id))" -ForegroundColor Green
    }
} else {
    Write-Host "⚪ No SafeLink processes found" -ForegroundColor Gray
}

Write-Host "`nTest completed!" -ForegroundColor Cyan
