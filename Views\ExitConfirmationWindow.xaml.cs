using System.Windows;
using SafeLink.Core;

namespace SafeLink.Views
{
    public partial class ExitConfirmationWindow : Window
    {
        public bool Result { get; private set; } = false;

        public ExitConfirmationWindow()
        {
            InitializeComponent();
            LoadLanguageStrings();
        }

        private void LoadLanguageStrings()
        {
            var language = LanguageManager.CurrentLanguage;

            // Use LanguageManager for consistent translations
            TitleText.Text = LanguageManager.GetString("ExitConfirmation");
            MessageText.Text = LanguageManager.GetString("ExitMessage");
            YesButtonText.Text = LanguageManager.GetString("Yes");
            NoButtonText.Text = LanguageManager.GetString("No");

            // Set flow direction based on language
            if (language == "ar")
            {
                FlowDirection = FlowDirection.RightToLeft;
            }
            else
            {
                FlowDirection = FlowDirection.LeftToRight;
            }
        }

        private void YesButton_Click(object sender, RoutedEventArgs e)
        {
            Result = true;
            DialogResult = true;
            Close();
        }

        private void NoButton_Click(object sender, RoutedEventArgs e)
        {
            Result = false;
            DialogResult = false;
            Close();
        }

        protected override void OnSourceInitialized(System.EventArgs e)
        {
            base.OnSourceInitialized(e);
            
            // Add window blur effect if available
            try
            {
                var helper = new System.Windows.Interop.WindowInteropHelper(this);
                var hwnd = helper.Handle;
                
                // Enable blur behind
                var accent = new AccentPolicy
                {
                    AccentState = AccentState.ACCENT_ENABLE_BLURBEHIND,
                    GradientColor = 0x01000000
                };

                var accentStructSize = System.Runtime.InteropServices.Marshal.SizeOf(accent);
                var accentPtr = System.Runtime.InteropServices.Marshal.AllocHGlobal(accentStructSize);
                System.Runtime.InteropServices.Marshal.StructureToPtr(accent, accentPtr, false);

                var data = new WindowCompositionAttributeData
                {
                    Attribute = WindowCompositionAttribute.WCA_ACCENT_POLICY,
                    SizeOfData = accentStructSize,
                    Data = accentPtr
                };

                SetWindowCompositionAttribute(hwnd, ref data);
                System.Runtime.InteropServices.Marshal.FreeHGlobal(accentPtr);
            }
            catch
            {
                // Ignore if blur effect is not available
            }
        }

        #region Windows API for Blur Effect
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        internal static extern int SetWindowCompositionAttribute(System.IntPtr hwnd, ref WindowCompositionAttributeData data);

        [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
        internal struct WindowCompositionAttributeData
        {
            public WindowCompositionAttribute Attribute;
            public System.IntPtr Data;
            public int SizeOfData;
        }

        internal enum WindowCompositionAttribute
        {
            WCA_ACCENT_POLICY = 19
        }

        [System.Runtime.InteropServices.StructLayout(System.Runtime.InteropServices.LayoutKind.Sequential)]
        internal struct AccentPolicy
        {
            public AccentState AccentState;
            public int AccentFlags;
            public int GradientColor;
            public int AnimationId;
        }

        internal enum AccentState
        {
            ACCENT_DISABLED = 0,
            ACCENT_ENABLE_GRADIENT = 1,
            ACCENT_ENABLE_TRANSPARENTGRADIENT = 2,
            ACCENT_ENABLE_BLURBEHIND = 3,
            ACCENT_INVALID_STATE = 4
        }
        #endregion
    }
}
