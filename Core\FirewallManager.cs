using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace SafeLink.Core
{
    public static class FirewallManager
    {
        private static readonly string AppName = "SafeLink";
        private static readonly string AppDescription = "SafeLink - Secure Communication Platform";
        
        /// <summary>
        /// إضافة قواعد جدار الحماية الكاملة (Inbound + Outbound)
        /// </summary>
        public static async Task<bool> AddFirewallRulesAsync()
        {
            try
            {
                var appPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (string.IsNullOrEmpty(appPath))
                {
                    System.Diagnostics.Debug.WriteLine("❌ Could not get application path");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"🔥 Adding firewall rules for: {appPath}");

                // إضافة قواعد UDP (Inbound + Outbound)
                var udpInboundSuccess = await AddFirewallRule(
                    $"{AppName}_UDP_Inbound", 
                    appPath, 
                    "UDP", 
                    "in", 
                    "8888-8938",
                    "Allow UDP inbound connections for SafeLink network discovery"
                );

                var udpOutboundSuccess = await AddFirewallRule(
                    $"{AppName}_UDP_Outbound", 
                    appPath, 
                    "UDP", 
                    "out", 
                    "8888-8938",
                    "Allow UDP outbound connections for SafeLink network discovery"
                );

                // إضافة قواعد TCP (Inbound + Outbound)
                var tcpInboundSuccess = await AddFirewallRule(
                    $"{AppName}_TCP_Inbound", 
                    appPath, 
                    "TCP", 
                    "in", 
                    "8889-8939",
                    "Allow TCP inbound connections for SafeLink direct communication"
                );

                var tcpOutboundSuccess = await AddFirewallRule(
                    $"{AppName}_TCP_Outbound", 
                    appPath, 
                    "TCP", 
                    "out", 
                    "8889-8939",
                    "Allow TCP outbound connections for SafeLink direct communication"
                );

                var allSuccess = udpInboundSuccess && udpOutboundSuccess && tcpInboundSuccess && tcpOutboundSuccess;
                
                if (allSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("✅ All firewall rules added successfully");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ Some firewall rules failed to add");
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding firewall rules: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إضافة قاعدة جدار حماية واحدة
        /// </summary>
        private static async Task<bool> AddFirewallRule(string ruleName, string appPath, string protocol, string direction, string ports, string description)
        {
            try
            {
                // التحقق من وجود القاعدة أولاً
                if (await CheckFirewallRuleExists(ruleName))
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Firewall rule '{ruleName}' already exists");
                    return true;
                }

                // إنشاء أمر netsh لإضافة القاعدة
                var arguments = $"advfirewall firewall add rule " +
                              $"name=\"{ruleName}\" " +
                              $"dir={direction} " +
                              $"action=allow " +
                              $"protocol={protocol} " +
                              $"localport={ports} " +
                              $"program=\"{appPath}\" " +
                              $"description=\"{description}\" " +
                              $"enable=yes";

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = arguments,
                    UseShellExecute = true,
                    Verb = "runas", // تشغيل كمدير
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                System.Diagnostics.Debug.WriteLine($"🔥 Adding {direction} rule: {ruleName} ({protocol} {ports})");

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;
                        
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ {ruleName} added successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to add {ruleName} (Exit code: {process.ExitCode})");
                        }
                        
                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding firewall rule '{ruleName}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة جدار الحماية
        /// </summary>
        private static async Task<bool> CheckFirewallRuleExists(string ruleName)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall show rule name=\"{ruleName}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await Task.Run(() => process.WaitForExit());
                        
                        return !output.Contains("No rules match the specified criteria");
                    }
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// إزالة جميع قواعد SafeLink من جدار الحماية
        /// </summary>
        public static async Task<bool> RemoveFirewallRulesAsync()
        {
            try
            {
                var rules = new[]
                {
                    $"{AppName}_UDP_Inbound",
                    $"{AppName}_UDP_Outbound", 
                    $"{AppName}_TCP_Inbound",
                    $"{AppName}_TCP_Outbound"
                };

                bool allSuccess = true;

                foreach (var rule in rules)
                {
                    var success = await RemoveFirewallRule(rule);
                    if (!success) allSuccess = false;
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error removing firewall rules: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إزالة قاعدة جدار حماية واحدة
        /// </summary>
        private static async Task<bool> RemoveFirewallRule(string ruleName)
        {
            try
            {
                if (!await CheckFirewallRuleExists(ruleName))
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Firewall rule '{ruleName}' does not exist");
                    return true;
                }

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall delete rule name=\"{ruleName}\"",
                    UseShellExecute = true,
                    Verb = "runas",
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;
                        
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ {ruleName} removed successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to remove {ruleName}");
                        }
                        
                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error removing firewall rule '{ruleName}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من حالة جدار الحماية
        /// </summary>
        public static async Task<string> GetFirewallStatusAsync()
        {
            try
            {
                var rules = new[]
                {
                    $"{AppName}_UDP_Inbound",
                    $"{AppName}_UDP_Outbound", 
                    $"{AppName}_TCP_Inbound",
                    $"{AppName}_TCP_Outbound"
                };

                var status = "🔥 Firewall Rules Status:\n";
                
                foreach (var rule in rules)
                {
                    var exists = await CheckFirewallRuleExists(rule);
                    status += $"   {rule}: {(exists ? "✅ Active" : "❌ Missing")}\n";
                }

                return status;
            }
            catch (Exception ex)
            {
                return $"❌ Error checking firewall status: {ex.Message}";
            }
        }
    }
}
