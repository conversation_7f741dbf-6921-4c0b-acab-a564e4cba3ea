using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace SafeLink.Core
{
    public static class FirewallManager
    {
        private static readonly string AppName = "SafeLink";

        /// <summary>
        /// إضافة قاعدة جدار حماية للتطبيق (الطريقة الصحيحة لـ Windows Defender)
        /// هذه الطريقة تضيف قاعدة واحدة للتطبيق تشمل Inbound و Outbound تلقائياً
        /// </summary>
        public static async Task<bool> AddApplicationFirewallRuleAsync()
        {
            try
            {
                var appPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (string.IsNullOrEmpty(appPath))
                {
                    System.Diagnostics.Debug.WriteLine("❌ Could not get application path");
                    return false;
                }

                System.Diagnostics.Debug.WriteLine($"🔥 Adding application firewall rule for: {Path.GetFileName(appPath)}");

                // التحقق من وجود القاعدة أولاً
                var ruleName = $"{AppName} - Secure Communication";
                if (await CheckFirewallRuleExists(ruleName))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Firewall rule '{ruleName}' already exists");
                    return true;
                }

                // إضافة قاعدة واحدة للتطبيق (Windows Defender سيسأل مرة واحدة فقط)
                var arguments = $"advfirewall firewall add rule " +
                              $"name=\"{ruleName}\" " +
                              $"dir=in " +
                              $"action=allow " +
                              $"program=\"{appPath}\" " +
                              $"description=\"Allow {AppName} secure communication platform\" " +
                              $"enable=yes";

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = arguments,
                    UseShellExecute = true,
                    Verb = "runas", // سيطلب صلاحيات المدير
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                System.Diagnostics.Debug.WriteLine($"🔥 Adding firewall rule: {ruleName}");

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;
                        
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ {ruleName} added successfully");
                            System.Diagnostics.Debug.WriteLine("ℹ️ Windows Defender will now allow both Inbound and Outbound connections");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to add {ruleName} (Exit code: {process.ExitCode})");
                        }
                        
                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding application firewall rule: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة جدار الحماية
        /// </summary>
        private static async Task<bool> CheckFirewallRuleExists(string ruleName)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall show rule name=\"{ruleName}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await Task.Run(() => process.WaitForExit());
                        
                        return !output.Contains("No rules match the specified criteria");
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error checking firewall rule existence: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إزالة قاعدة جدار الحماية
        /// </summary>
        public static async Task<bool> RemoveApplicationFirewallRuleAsync()
        {
            try
            {
                var ruleName = $"{AppName} - Secure Communication";
                
                if (!await CheckFirewallRuleExists(ruleName))
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Firewall rule '{ruleName}' does not exist");
                    return true;
                }

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall delete rule name=\"{ruleName}\"",
                    UseShellExecute = true,
                    Verb = "runas",
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;
                        
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ {ruleName} removed successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to remove {ruleName}");
                        }
                        
                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error removing firewall rule: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من حالة جدار الحماية
        /// </summary>
        public static async Task<string> GetFirewallStatusAsync()
        {
            try
            {
                var ruleName = $"{AppName} - Secure Communication";
                var exists = await CheckFirewallRuleExists(ruleName);
                
                if (exists)
                {
                    return $"✅ Firewall Rule Status: ACTIVE\n" +
                           $"📋 Rule Name: {ruleName}\n" +
                           $"🔥 Protection: Inbound + Outbound\n" +
                           $"🌐 Network: All profiles (Domain, Private, Public)\n" +
                           $"✅ SafeLink is protected by Windows Firewall";
                }
                else
                {
                    return $"❌ Firewall Rule Status: NOT FOUND\n" +
                           $"⚠️ SafeLink is not protected by Windows Firewall\n" +
                           $"🔧 Recommendation: Add firewall rule for network access";
                }
            }
            catch (Exception ex)
            {
                return $"❌ Error checking firewall status: {ex.Message}";
            }
        }
    }
}
