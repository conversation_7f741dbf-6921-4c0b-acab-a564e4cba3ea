using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace SafeLink.Core
{
    public static class FirewallManager
    {
        private static readonly string AppName = "SafeLink";
        private static readonly string AppDescription = "SafeLink - Secure Communication Platform";
        
        /// <summary>
        /// إضافة قواعد جدار الحماية الكاملة (Inbound + Outbound)
        /// </summary>
        public static async Task<bool> AddFirewallRulesAsync(bool silentMode = false)
        {
            try
            {
                var appPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (string.IsNullOrEmpty(appPath))
                {
                    System.Diagnostics.Debug.WriteLine("❌ Could not get application path");
                    return false;
                }

                if (!silentMode)
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Adding firewall rules for: {appPath}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"🔇 Adding silent firewall rules...");
                }

                // إضافة قواعد UDP (Inbound + Outbound)
                var udpInboundSuccess = await AddFirewallRule(
                    $"{AppName}_UDP_Inbound",
                    appPath,
                    "UDP",
                    "in",
                    "8888-8938",
                    "Allow UDP inbound connections for SafeLink network discovery",
                    silentMode
                );

                var udpOutboundSuccess = await AddFirewallRule(
                    $"{AppName}_UDP_Outbound",
                    appPath,
                    "UDP",
                    "out",
                    "8888-8938",
                    "Allow UDP outbound connections for SafeLink network discovery",
                    silentMode
                );

                // إضافة قواعد TCP (Inbound + Outbound)
                var tcpInboundSuccess = await AddFirewallRule(
                    $"{AppName}_TCP_Inbound",
                    appPath,
                    "TCP",
                    "in",
                    "8889-8939",
                    "Allow TCP inbound connections for SafeLink direct communication",
                    silentMode
                );

                var tcpOutboundSuccess = await AddFirewallRule(
                    $"{AppName}_TCP_Outbound",
                    appPath,
                    "TCP",
                    "out",
                    "8889-8939",
                    "Allow TCP outbound connections for SafeLink direct communication",
                    silentMode
                );

                var allSuccess = udpInboundSuccess && udpOutboundSuccess && tcpInboundSuccess && tcpOutboundSuccess;
                
                if (allSuccess)
                {
                    if (!silentMode)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ All firewall rules added successfully");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("🔇 Silent firewall rules added");
                    }
                }
                else
                {
                    if (!silentMode)
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ Some firewall rules failed to add");
                    }
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding firewall rules: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إضافة قاعدة جدار حماية واحدة
        /// </summary>
        private static async Task<bool> AddFirewallRule(string ruleName, string appPath, string protocol, string direction, string ports, string description, bool silentMode = false)
        {
            try
            {
                // التحقق من وجود القاعدة أولاً
                if (await CheckFirewallRuleExists(ruleName))
                {
                    if (!silentMode)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔥 Firewall rule '{ruleName}' already exists");
                    }
                    return true;
                }

                // إنشاء أمر netsh لإضافة القاعدة
                var arguments = $"advfirewall firewall add rule " +
                              $"name=\"{ruleName}\" " +
                              $"dir={direction} " +
                              $"action=allow " +
                              $"protocol={protocol} " +
                              $"localport={ports} " +
                              $"program=\"{appPath}\" " +
                              $"description=\"{description}\" " +
                              $"enable=yes";

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = arguments,
                    UseShellExecute = silentMode ? false : true, // Silent mode uses different approach
                    Verb = silentMode ? null : "runas", // No elevation prompt in silent mode
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true,
                    RedirectStandardOutput = silentMode,
                    RedirectStandardError = silentMode
                };

                if (!silentMode)
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Adding {direction} rule: {ruleName} ({protocol} {ports})");
                }

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        if (silentMode && processInfo.RedirectStandardOutput)
                        {
                            // في الوضع الصامت، اقرأ الإخراج بدون عرضه
                            var output = await process.StandardOutput.ReadToEndAsync();
                            var error = await process.StandardError.ReadToEndAsync();
                        }

                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;

                        if (!silentMode)
                        {
                            if (success)
                            {
                                System.Diagnostics.Debug.WriteLine($"✅ {ruleName} added successfully");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ Failed to add {ruleName} (Exit code: {process.ExitCode})");
                            }
                        }

                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding firewall rule '{ruleName}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة جدار الحماية
        /// </summary>
        private static async Task<bool> CheckFirewallRuleExists(string ruleName)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall show rule name=\"{ruleName}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await Task.Run(() => process.WaitForExit());
                        
                        return !output.Contains("No rules match the specified criteria");
                    }
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// إزالة جميع قواعد SafeLink من جدار الحماية
        /// </summary>
        public static async Task<bool> RemoveFirewallRulesAsync()
        {
            try
            {
                var rules = new[]
                {
                    $"{AppName}_UDP_Inbound",
                    $"{AppName}_UDP_Outbound", 
                    $"{AppName}_TCP_Inbound",
                    $"{AppName}_TCP_Outbound"
                };

                bool allSuccess = true;

                foreach (var rule in rules)
                {
                    var success = await RemoveFirewallRule(rule);
                    if (!success) allSuccess = false;
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error removing firewall rules: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إزالة قاعدة جدار حماية واحدة
        /// </summary>
        private static async Task<bool> RemoveFirewallRule(string ruleName)
        {
            try
            {
                if (!await CheckFirewallRuleExists(ruleName))
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Firewall rule '{ruleName}' does not exist");
                    return true;
                }

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall delete rule name=\"{ruleName}\"",
                    UseShellExecute = true,
                    Verb = "runas",
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;
                        
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ {ruleName} removed successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to remove {ruleName}");
                        }
                        
                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error removing firewall rule '{ruleName}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعداد جدار الحماية بشكل صامت (بدون نوافذ أو إشعارات)
        /// </summary>
        public static async Task<bool> SetupSilentFirewallAsync()
        {
            try
            {
                // محاولة إضافة القواعد بشكل صامت أولاً
                var silentSuccess = await AddFirewallRulesAsync(true);

                if (silentSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("🔇 Silent firewall setup completed successfully");
                    return true;
                }

                // إذا فشل الوضع الصامت، جرب الوضع العادي
                System.Diagnostics.Debug.WriteLine("🔇 Silent mode failed, trying normal mode...");
                return await AddFirewallRulesAsync(false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Silent firewall setup failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إضافة قواعد جدار الحماية باستخدام PowerShell (أكثر صمت<|im_start|>)
        /// </summary>
        public static async Task<bool> AddFirewallRulesViaPowerShellAsync()
        {
            try
            {
                var appPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (string.IsNullOrEmpty(appPath))
                {
                    return false;
                }

                // استخدام PowerShell لإضافة القواعد بشكل صامت
                var script = $@"
                    try {{
                        # إضافة قواعد UDP
                        New-NetFirewallRule -DisplayName 'SafeLink_UDP_Inbound' -Direction Inbound -Protocol UDP -LocalPort 8888-8938 -Program '{appPath}' -Action Allow -ErrorAction SilentlyContinue
                        New-NetFirewallRule -DisplayName 'SafeLink_UDP_Outbound' -Direction Outbound -Protocol UDP -LocalPort 8888-8938 -Program '{appPath}' -Action Allow -ErrorAction SilentlyContinue

                        # إضافة قواعد TCP
                        New-NetFirewallRule -DisplayName 'SafeLink_TCP_Inbound' -Direction Inbound -Protocol TCP -LocalPort 8889-8939 -Program '{appPath}' -Action Allow -ErrorAction SilentlyContinue
                        New-NetFirewallRule -DisplayName 'SafeLink_TCP_Outbound' -Direction Outbound -Protocol TCP -LocalPort 8889-8939 -Program '{appPath}' -Action Allow -ErrorAction SilentlyContinue

                        Write-Output 'SUCCESS'
                    }} catch {{
                        Write-Output 'FAILED'
                    }}
                ";

                var processInfo = new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = $"-ExecutionPolicy Bypass -WindowStyle Hidden -Command \"{script}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await Task.Run(() => process.WaitForExit());

                        var success = output.Contains("SUCCESS");
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine("🔇 PowerShell firewall rules added silently");
                        }

                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ PowerShell firewall setup failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من حالة جدار الحماية
        /// </summary>
        public static async Task<string> GetFirewallStatusAsync()
        {
            try
            {
                var rules = new[]
                {
                    $"{AppName}_UDP_Inbound",
                    $"{AppName}_UDP_Outbound", 
                    $"{AppName}_TCP_Inbound",
                    $"{AppName}_TCP_Outbound"
                };

                var status = "🔥 Firewall Rules Status:\n";
                
                foreach (var rule in rules)
                {
                    var exists = await CheckFirewallRuleExists(rule);
                    status += $"   {rule}: {(exists ? "✅ Active" : "❌ Missing")}\n";
                }

                return status;
            }
            catch (Exception ex)
            {
                return $"❌ Error checking firewall status: {ex.Message}";
            }
        }
    }
}
