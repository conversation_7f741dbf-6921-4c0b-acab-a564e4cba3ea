using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace SafeLink.Core
{
    public static class FirewallManager
    {
        private static readonly string AppName = "SafeLink";
        private static readonly string AppDescription = "SafeLink - Secure Communication Platform";
        
        /// <summary>
        /// إضافة قواعد جدار الحماية الكاملة (Inbound + Outbound)
        /// </summary>
        public static async Task<bool> AddFirewallRulesAsync(bool silentMode = false)
        {
            try
            {
                var appPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (string.IsNullOrEmpty(appPath))
                {
                    System.Diagnostics.Debug.WriteLine("❌ Could not get application path");
                    return false;
                }

                if (!silentMode)
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Adding firewall rules for: {appPath}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"🔇 Adding silent firewall rules...");
                }

                // إضافة قواعد UDP (Inbound + Outbound)
                var udpInboundSuccess = await AddFirewallRule(
                    $"{AppName}_UDP_Inbound",
                    appPath,
                    "UDP",
                    "in",
                    "8888-8938",
                    "Allow UDP inbound connections for SafeLink network discovery",
                    silentMode
                );

                var udpOutboundSuccess = await AddFirewallRule(
                    $"{AppName}_UDP_Outbound",
                    appPath,
                    "UDP",
                    "out",
                    "8888-8938",
                    "Allow UDP outbound connections for SafeLink network discovery",
                    silentMode
                );

                // إضافة قواعد TCP (Inbound + Outbound)
                var tcpInboundSuccess = await AddFirewallRule(
                    $"{AppName}_TCP_Inbound",
                    appPath,
                    "TCP",
                    "in",
                    "8889-8939",
                    "Allow TCP inbound connections for SafeLink direct communication",
                    silentMode
                );

                var tcpOutboundSuccess = await AddFirewallRule(
                    $"{AppName}_TCP_Outbound",
                    appPath,
                    "TCP",
                    "out",
                    "8889-8939",
                    "Allow TCP outbound connections for SafeLink direct communication",
                    silentMode
                );

                var allSuccess = udpInboundSuccess && udpOutboundSuccess && tcpInboundSuccess && tcpOutboundSuccess;
                
                if (allSuccess)
                {
                    if (!silentMode)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ All firewall rules added successfully");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("🔇 Silent firewall rules added");
                    }
                }
                else
                {
                    if (!silentMode)
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ Some firewall rules failed to add");
                    }
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding firewall rules: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إضافة قاعدة جدار حماية واحدة
        /// </summary>
        private static async Task<bool> AddFirewallRule(string ruleName, string appPath, string protocol, string direction, string ports, string description, bool silentMode = false)
        {
            try
            {
                // التحقق من وجود القاعدة أولاً
                if (await CheckFirewallRuleExists(ruleName))
                {
                    if (!silentMode)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔥 Firewall rule '{ruleName}' already exists");
                    }
                    return true;
                }

                // إنشاء أمر netsh لإضافة القاعدة
                var arguments = $"advfirewall firewall add rule " +
                              $"name=\"{ruleName}\" " +
                              $"dir={direction} " +
                              $"action=allow " +
                              $"protocol={protocol} " +
                              $"localport={ports} " +
                              $"program=\"{appPath}\" " +
                              $"description=\"{description}\" " +
                              $"enable=yes";

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = arguments,
                    UseShellExecute = silentMode ? false : true, // Silent mode uses different approach
                    Verb = silentMode ? null : "runas", // No elevation prompt in silent mode
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true,
                    RedirectStandardOutput = silentMode,
                    RedirectStandardError = silentMode
                };

                if (!silentMode)
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Adding {direction} rule: {ruleName} ({protocol} {ports})");
                }

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        if (silentMode && processInfo.RedirectStandardOutput)
                        {
                            // في الوضع الصامت، اقرأ الإخراج بدون عرضه
                            var output = await process.StandardOutput.ReadToEndAsync();
                            var error = await process.StandardError.ReadToEndAsync();
                        }

                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;

                        if (!silentMode)
                        {
                            if (success)
                            {
                                System.Diagnostics.Debug.WriteLine($"✅ {ruleName} added successfully");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ Failed to add {ruleName} (Exit code: {process.ExitCode})");
                            }
                        }

                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding firewall rule '{ruleName}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود قاعدة جدار الحماية
        /// </summary>
        private static async Task<bool> CheckFirewallRuleExists(string ruleName)
        {
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall show rule name=\"{ruleName}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await Task.Run(() => process.WaitForExit());
                        
                        return !output.Contains("No rules match the specified criteria");
                    }
                }

                return false;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// إزالة جميع قواعد SafeLink من جدار الحماية
        /// </summary>
        public static async Task<bool> RemoveFirewallRulesAsync()
        {
            try
            {
                var rules = new[]
                {
                    $"{AppName}_UDP_Inbound",
                    $"{AppName}_UDP_Outbound", 
                    $"{AppName}_TCP_Inbound",
                    $"{AppName}_TCP_Outbound"
                };

                bool allSuccess = true;

                foreach (var rule in rules)
                {
                    var success = await RemoveFirewallRule(rule);
                    if (!success) allSuccess = false;
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error removing firewall rules: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إزالة قاعدة جدار حماية واحدة
        /// </summary>
        private static async Task<bool> RemoveFirewallRule(string ruleName)
        {
            try
            {
                if (!await CheckFirewallRuleExists(ruleName))
                {
                    System.Diagnostics.Debug.WriteLine($"🔥 Firewall rule '{ruleName}' does not exist");
                    return true;
                }

                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall delete rule name=\"{ruleName}\"",
                    UseShellExecute = true,
                    Verb = "runas",
                    WindowStyle = ProcessWindowStyle.Hidden,
                    CreateNoWindow = true
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        await Task.Run(() => process.WaitForExit());
                        var success = process.ExitCode == 0;
                        
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ {ruleName} removed successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to remove {ruleName}");
                        }
                        
                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error removing firewall rule '{ruleName}': {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إعداد جدار الحماية بشكل صامت تمام<|im_start|> (بدون أي نوافذ أو إشعارات)
        /// </summary>
        public static async Task<bool> SetupSilentFirewallAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔇 Starting completely silent firewall setup...");

                // الطريقة الأولى: WMI (أكثر صمت<|im_start|>)
                System.Diagnostics.Debug.WriteLine("🔇 Trying WMI method...");
                var wmiSuccess = await AddFirewallRulesViaWMIAsync();
                if (wmiSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("🔇 WMI silent firewall setup completed successfully");
                    return true;
                }

                // الطريقة الثانية: netsh صامت
                System.Diagnostics.Debug.WriteLine("🔇 Trying silent netsh method...");
                var netshSuccess = await AddFirewallRulesViaSilentNetshAsync();
                if (netshSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("🔇 Silent netsh firewall setup completed successfully");
                    return true;
                }

                // الطريقة الثالثة: netsh عادي مع silent flag
                System.Diagnostics.Debug.WriteLine("🔇 Trying standard silent method...");
                var standardSilentSuccess = await AddFirewallRulesAsync(true);
                if (standardSilentSuccess)
                {
                    System.Diagnostics.Debug.WriteLine("🔇 Standard silent firewall setup completed successfully");
                    return true;
                }

                // إذا فشلت جميع الطرق الصامتة، لا نجرب الوضع العادي
                System.Diagnostics.Debug.WriteLine("⚠️ All silent methods failed - firewall rules may need manual setup");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Silent firewall setup failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إضافة قواعد جدار الحماية باستخدام netsh صامت (بدون UAC)
        /// </summary>
        public static async Task<bool> AddFirewallRulesViaSilentNetshAsync()
        {
            try
            {
                var appPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (string.IsNullOrEmpty(appPath))
                {
                    return false;
                }

                // استخدام netsh بدلاً من PowerShell لتجنب UAC prompts
                var commands = new[]
                {
                    $"advfirewall firewall add rule name=\"SafeLink_UDP_Inbound\" dir=in action=allow protocol=UDP localport=8888-8938 program=\"{appPath}\"",
                    $"advfirewall firewall add rule name=\"SafeLink_UDP_Outbound\" dir=out action=allow protocol=UDP localport=8888-8938 program=\"{appPath}\"",
                    $"advfirewall firewall add rule name=\"SafeLink_TCP_Inbound\" dir=in action=allow protocol=TCP localport=8889-8939 program=\"{appPath}\"",
                    $"advfirewall firewall add rule name=\"SafeLink_TCP_Outbound\" dir=out action=allow protocol=TCP localport=8889-8939 program=\"{appPath}\""
                };

                var successCount = 0;

                foreach (var command in commands)
                {
                    try
                    {
                        var processInfo = new ProcessStartInfo
                        {
                            FileName = "netsh",
                            Arguments = command,
                            UseShellExecute = false,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            CreateNoWindow = true,
                            WindowStyle = ProcessWindowStyle.Hidden
                        };

                        using (var process = Process.Start(processInfo))
                        {
                            if (process != null)
                            {
                                var output = await process.StandardOutput.ReadToEndAsync();
                                var error = await process.StandardError.ReadToEndAsync();
                                await Task.Run(() => process.WaitForExit());

                                if (process.ExitCode == 0 || output.Contains("Ok."))
                                {
                                    successCount++;
                                }
                            }
                        }
                    }
                    catch
                    {
                        // تجاهل الأخطاء في الوضع الصامت
                        continue;
                    }
                }

                var success = successCount >= 2; // نحتاج على الأقل UDP rules
                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"🔇 Silent netsh firewall rules added ({successCount}/4)");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Silent netsh firewall setup failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إضافة قواعد جدار الحماية باستخدام WMI (صامت تمام<|im_start|>)
        /// </summary>
        public static async Task<bool> AddFirewallRulesViaWMIAsync()
        {
            try
            {
                var appPath = Process.GetCurrentProcess().MainModule?.FileName;
                if (string.IsNullOrEmpty(appPath))
                {
                    return false;
                }

                // استخدام WMI لإضافة القواعد بدون أي prompts
                var script = $@"
                    try {{
                        $fw = New-Object -ComObject HNetCfg.FwPolicy2
                        $rules = $fw.Rules

                        # إضافة قواعد UDP
                        $rule1 = New-Object -ComObject HNetCfg.FWRule
                        $rule1.Name = 'SafeLink_UDP_Inbound'
                        $rule1.Direction = 1
                        $rule1.Action = 1
                        $rule1.Protocol = 17
                        $rule1.LocalPorts = '8888-8938'
                        $rule1.ApplicationName = '{appPath}'
                        $rule1.Enabled = $true
                        $rules.Add($rule1)

                        $rule2 = New-Object -ComObject HNetCfg.FWRule
                        $rule2.Name = 'SafeLink_UDP_Outbound'
                        $rule2.Direction = 2
                        $rule2.Action = 1
                        $rule2.Protocol = 17
                        $rule2.LocalPorts = '8888-8938'
                        $rule2.ApplicationName = '{appPath}'
                        $rule2.Enabled = $true
                        $rules.Add($rule2)

                        # إضافة قواعد TCP
                        $rule3 = New-Object -ComObject HNetCfg.FWRule
                        $rule3.Name = 'SafeLink_TCP_Inbound'
                        $rule3.Direction = 1
                        $rule3.Action = 1
                        $rule3.Protocol = 6
                        $rule3.LocalPorts = '8889-8939'
                        $rule3.ApplicationName = '{appPath}'
                        $rule3.Enabled = $true
                        $rules.Add($rule3)

                        $rule4 = New-Object -ComObject HNetCfg.FWRule
                        $rule4.Name = 'SafeLink_TCP_Outbound'
                        $rule4.Direction = 2
                        $rule4.Action = 1
                        $rule4.Protocol = 6
                        $rule4.LocalPorts = '8889-8939'
                        $rule4.ApplicationName = '{appPath}'
                        $rule4.Enabled = $true
                        $rules.Add($rule4)

                        Write-Output 'WMI_SUCCESS'
                    }} catch {{
                        Write-Output 'WMI_FAILED'
                    }}
                ";

                var processInfo = new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = $"-ExecutionPolicy Bypass -WindowStyle Hidden -NoProfile -NonInteractive -Command \"{script}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                };

                using (var process = Process.Start(processInfo))
                {
                    if (process != null)
                    {
                        var output = await process.StandardOutput.ReadToEndAsync();
                        await Task.Run(() => process.WaitForExit());

                        var success = output.Contains("WMI_SUCCESS");
                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine("🔇 WMI firewall rules added silently");
                        }

                        return success;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ WMI firewall setup failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من حالة جدار الحماية
        /// </summary>
        public static async Task<string> GetFirewallStatusAsync()
        {
            try
            {
                var rules = new[]
                {
                    $"{AppName}_UDP_Inbound",
                    $"{AppName}_UDP_Outbound", 
                    $"{AppName}_TCP_Inbound",
                    $"{AppName}_TCP_Outbound"
                };

                var status = "🔥 Firewall Rules Status:\n";
                
                foreach (var rule in rules)
                {
                    var exists = await CheckFirewallRuleExists(rule);
                    status += $"   {rule}: {(exists ? "✅ Active" : "❌ Missing")}\n";
                }

                return status;
            }
            catch (Exception ex)
            {
                return $"❌ Error checking firewall status: {ex.Message}";
            }
        }
    }
}
