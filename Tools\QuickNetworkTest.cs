using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace SafeLink.Tools
{
    public static class QuickNetworkTest
    {
        public static async Task TestNetworkDiscovery()
        {
            Console.WriteLine("🔍 SafeLink Network Discovery Test");
            Console.WriteLine("==================================");
            
            // Test UDP ports 8888-8938
            for (int port = 8888; port <= 8938; port++)
            {
                await TestPort(port);
            }
            
            Console.WriteLine("\n✅ Network test completed!");
        }
        
        private static async Task TestPort(int port)
        {
            try
            {
                using var client = new UdpClient();
                client.Client.ReceiveTimeout = 1000;
                
                // Send discovery probe
                var probe = "SAFELINK_NETWORK_PROBE";
                var data = Encoding.UTF8.GetBytes(probe);
                var endpoint = new IPEndPoint(IPAddress.Broadcast, port);
                
                await client.SendAsync(data, data.Length, endpoint);
                
                // Listen for responses
                try
                {
                    var result = await client.ReceiveAsync();
                    var response = Encoding.UTF8.GetString(result.Buffer);
                    
                    if (response.StartsWith("SAFELINK_"))
                    {
                        Console.WriteLine($"📡 Port {port}: ACTIVE - {response.Substring(0, Math.Min(50, response.Length))}...");
                    }
                }
                catch (SocketException)
                {
                    // No response - port likely inactive
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Port {port}: ERROR - {ex.Message}");
            }
        }
        
        public static async Task SendTestDiscovery(int port = 8891)
        {
            try
            {
                Console.WriteLine($"📡 Sending test discovery to port {port}...");
                
                using var client = new UdpClient();
                
                var testUser = new
                {
                    Username = "TestUser",
                    ArabicUsername = "مستخدم تجريبي",
                    DeviceId = "TEST_DEVICE",
                    Role = 0,
                    Port = port,
                    IsMeshLeader = false,
                    MeshNetworkPort = port,
                    LastSeen = DateTime.Now,
                    IsOnline = true
                };
                
                var userData = JsonConvert.SerializeObject(testUser);
                var message = $"SAFELINK_DISCOVERY:{userData}";
                var data = Encoding.UTF8.GetBytes(message);
                
                var endpoint = new IPEndPoint(IPAddress.Broadcast, port);
                await client.SendAsync(data, data.Length, endpoint);
                
                Console.WriteLine("✅ Test discovery sent!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to send test discovery: {ex.Message}");
            }
        }
    }
}
