# SafeLink Firewall Test Script
Write-Host "🔥 SafeLink Firewall Test" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""

# تحقق من القاعدة الجديدة
$ruleName = "SafeLink - Secure Communication"
Write-Host "📋 Checking firewall rule: $ruleName" -ForegroundColor Yellow

$result = netsh advfirewall firewall show rule name="$ruleName" 2>$null

if ($result -match "No rules match the specified criteria") {
    Write-Host "❌ $ruleName - NOT FOUND" -ForegroundColor Red
} else {
    Write-Host "✅ $ruleName - ACTIVE" -ForegroundColor Green
    
    # استخراج تفاصيل القاعدة
    if ($result -match "Direction:\s+(\w+)") {
        $direction = $matches[1]
        Write-Host "   Direction: $direction" -ForegroundColor Gray
    }
    
    if ($result -match "Action:\s+(\w+)") {
        $action = $matches[1]
        Write-Host "   Action: $action" -ForegroundColor Gray
    }
    
    if ($result -match "Profiles:\s+(.+)") {
        $profiles = $matches[1]
        Write-Host "   Profiles: $profiles" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "📡 Checking SafeLink ports..." -ForegroundColor Yellow

# تحقق من البورتات النشطة
$udpPorts = netstat -an | findstr "UDP" | findstr ":889"
if ($udpPorts) {
    Write-Host "✅ SafeLink UDP ports found:" -ForegroundColor Green
    $udpPorts | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ No SafeLink UDP ports found" -ForegroundColor Red
}

Write-Host ""
Write-Host "🚀 SafeLink Process:" -ForegroundColor Yellow
$processes = Get-Process | Where-Object {$_.ProcessName -like "*SafeLink*"}
if ($processes) {
    Write-Host "✅ SafeLink is running" -ForegroundColor Green
    $processes | Format-Table ProcessName, Id, WorkingSet -AutoSize
} else {
    Write-Host "❌ SafeLink is not running" -ForegroundColor Red
}

Write-Host ""
Write-Host "✅ Test completed!" -ForegroundColor Green
