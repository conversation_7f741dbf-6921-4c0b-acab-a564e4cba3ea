using System;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Windows;
using Newtonsoft.Json;
using System.IO.Compression;
using System.Net.Sockets;
using System.Text;

namespace SafeLink.Core
{
    public class UpdateManager
    {
        private static readonly string UPDATE_FOLDER = "./Update";
        private static readonly string VERSION_FILE = Path.Combine(UPDATE_FOLDER, "version.json");
        private static readonly string UPDATE_PACKAGE = Path.Combine(UPDATE_FOLDER, "SafeLink_Update.zip");
        private static readonly string CURRENT_VERSION = "1.0.0";
        private static readonly int UPDATE_PORT = 8891;

        public static async Task<bool> CheckForUpdatesAsync()
        {
            try
            {
                // إنشاء مجلد Update إذا لم يكن موجوداً
                if (!Directory.Exists(UPDATE_FOLDER))
                    Directory.CreateDirectory(UPDATE_FOLDER);

                // فحص ملف الإصدار المحلي
                if (File.Exists(VERSION_FILE))
                {
                    var versionContent = await File.ReadAllTextAsync(VERSION_FILE);
                    var versionInfo = JsonConvert.DeserializeObject<VersionInfo>(versionContent);

                    if (IsNewerVersion(versionInfo.Version, CURRENT_VERSION))
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        public static async Task<bool> DownloadAndInstallUpdateAsync()
        {
            try
            {
                // التحقق من وجود ملف التحديث
                if (!File.Exists(UPDATE_PACKAGE))
                {
                    MessageBox.Show("ملف التحديث غير موجود في مجلد Update", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // نسخ ملف التحديث إلى مجلد مؤقت
                var tempPath = Path.Combine(Path.GetTempPath(), "SafeLink_Update.zip");
                File.Copy(UPDATE_PACKAGE, tempPath, true);

                // إنشاء ملف batch للتحديث
                var batchPath = Path.Combine(Path.GetTempPath(), "SafeLink_Update.bat");
                var currentDir = AppDomain.CurrentDomain.BaseDirectory;

                var batchContent = $@"
@echo off
echo SafeLink Auto-Update...
timeout /t 3 /nobreak > nul

echo Stopping SafeLink...
taskkill /f /im SafeLink.exe > nul 2>&1

echo Extracting update...
powershell -command ""Expand-Archive -Path '{tempPath}' -DestinationPath '{currentDir}' -Force""

echo Cleaning up...
del ""{tempPath}""

echo Starting SafeLink...
cd /d ""{currentDir}""
start """" ""SafeLink.exe""

echo Update completed!
del ""%~f0""
";

                await File.WriteAllTextAsync(batchPath, batchContent);

                // تشغيل التحديث
                Process.Start(new ProcessStartInfo
                {
                    FileName = batchPath,
                    UseShellExecute = true,
                    WindowStyle = ProcessWindowStyle.Hidden
                });

                // إغلاق التطبيق الحالي
                Application.Current.Shutdown();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في تحديث البرنامج: {ex.Message}", "خطأ في التحديث",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private static bool IsNewerVersion(string serverVersion, string currentVersion)
        {
            var server = new Version(serverVersion);
            var current = new Version(currentVersion);
            return server > current;
        }

        public static async Task SendUpdateCommandAsync(string targetDeviceId)
        {
            try
            {
                // إرسال أمر التحديث عبر الشبكة المحلية
                var command = new UpdateCommand
                {
                    DeviceId = targetDeviceId,
                    Command = "UPDATE_NOW",
                    Timestamp = DateTime.Now
                };

                var json = JsonConvert.SerializeObject(command);
                var data = Encoding.UTF8.GetBytes(json);

                // إرسال عبر UDP broadcast
                using var udpClient = new UdpClient();
                udpClient.EnableBroadcast = true;
                await udpClient.SendAsync(data, data.Length, "255.255.255.255", UPDATE_PORT);

                MessageBox.Show("تم إرسال أمر التحديث لجميع الأجهزة المتصلة", "نجح الإرسال",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في إرسال أمر التحديث: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public static async Task StartUpdateListenerAsync()
        {
            try
            {
                using var udpClient = new UdpClient(UPDATE_PORT);

                while (true)
                {
                    var result = await udpClient.ReceiveAsync();
                    var json = Encoding.UTF8.GetString(result.Buffer);
                    var command = JsonConvert.DeserializeObject<UpdateCommand>(json);

                    if (command?.Command == "UPDATE_NOW")
                    {
                        // تنفيذ التحديث
                        await Application.Current.Dispatcher.InvokeAsync(async () =>
                        {
                            var hasUpdate = await CheckForUpdatesAsync();
                            if (hasUpdate)
                            {
                                var result = MessageBox.Show(
                                    "🔄 المطور طلب تحديث البرنامج!\n\nسيتم التحديث الآن.",
                                    "أمر تحديث من المطور",
                                    MessageBoxButton.OK,
                                    MessageBoxImage.Information);

                                await DownloadAndInstallUpdateAsync();
                            }
                        });
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء الاستماع
            }
        }

        public static async Task CreateUpdatePackageAsync()
        {
            try
            {
                // إنشاء مجلد Update إذا لم يكن موجوداً
                if (!Directory.Exists(UPDATE_FOLDER))
                    Directory.CreateDirectory(UPDATE_FOLDER);

                // إنشاء ملف الإصدار الجديد
                var newVersion = new VersionInfo
                {
                    Version = "1.0.1",
                    ReleaseDate = DateTime.Now.ToString("yyyy-MM-dd"),
                    Changes = new[]
                    {
                        "تحسينات في الأمان والأداء",
                        "إصلاح أخطاء في الواجهة",
                        "ميزات جديدة للمطور",
                        "تحسين نظام التحديث التلقائي"
                    }
                };

                var versionJson = JsonConvert.SerializeObject(newVersion, Formatting.Indented);
                await File.WriteAllTextAsync(VERSION_FILE, versionJson);

                MessageBox.Show($"تم إنشاء ملف الإصدار الجديد في:\n{VERSION_FILE}", "تم الإنشاء",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في إنشاء حزمة التحديث: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    public class VersionInfo
    {
        public string Version { get; set; }
        public string ReleaseDate { get; set; }
        public string[] Changes { get; set; }
    }

    public class UpdateCommand
    {
        public string DeviceId { get; set; }
        public string Command { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
