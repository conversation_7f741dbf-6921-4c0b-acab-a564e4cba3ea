using System;
using System.Windows;
using SafeLink.Models;

namespace SafeLink.Views
{
    public partial class CreateUserDialog : Window
    {
        public string Username { get; private set; }
        public string ArabicUsername { get; private set; }
        public string Password { get; private set; }
        public UserRole SelectedRole { get; private set; }

        public CreateUserDialog()
        {
            InitializeComponent();
        }

        private void CreateButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
                {
                    MessageBox.Show("Please enter a username", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    UsernameTextBox.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(PasswordBox.Password))
                {
                    MessageBox.Show("Please enter a password", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    PasswordBox.Focus();
                    return;
                }

                if (PasswordBox.Password != ConfirmPasswordBox.Password)
                {
                    MessageBox.Show("Passwords do not match", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    ConfirmPasswordBox.Focus();
                    return;
                }

                if (PasswordBox.Password.Length < 6)
                {
                    MessageBox.Show("Password must be at least 6 characters long", "Validation Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    PasswordBox.Focus();
                    return;
                }

                // Get selected role
                if (RoleComboBox.SelectedItem is System.Windows.Controls.ComboBoxItem selectedItem)
                {
                    var roleTag = selectedItem.Tag?.ToString();
                    if (Enum.TryParse<UserRole>(roleTag, out var role))
                    {
                        SelectedRole = role;
                    }
                    else
                    {
                        SelectedRole = UserRole.User; // Default
                    }
                }

                // Set properties
                Username = UsernameTextBox.Text.Trim();
                ArabicUsername = ArabicUsernameTextBox.Text.Trim();
                Password = PasswordBox.Password;

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating user: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
