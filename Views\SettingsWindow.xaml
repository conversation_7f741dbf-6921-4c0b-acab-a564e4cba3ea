<Window x:Class="SafeLink.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink Settings"
        Height="600" Width="750"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Icon="pack://application:,,,/Icons/SafeLink.ico">

    <Window.Resources>
        <!-- Professional Security Gradient -->
        <LinearGradientBrush x:Key="SecurityGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#0D1421" Offset="0"/>
            <GradientStop Color="#1A2332" Offset="0.3"/>
            <GradientStop Color="#243447" Offset="0.7"/>
            <GradientStop Color="#2C4A5C" Offset="1"/>
        </LinearGradientBrush>

        <!-- Accent Colors -->
        <SolidColorBrush x:Key="PrimaryAccent" Color="#00E5FF"/>
        <SolidColorBrush x:Key="SecondaryAccent" Color="#1DE9B6"/>
        <SolidColorBrush x:Key="WarningAccent" Color="#FFB74D"/>

        <!-- Professional GroupBox Style -->
        <Style x:Key="ProfessionalGroupBoxStyle" TargetType="GroupBox">
            <Setter Property="Foreground" Value="{StaticResource PrimaryAccent}"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="{StaticResource PrimaryAccent}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="GroupBox">
                        <Border Background="#15FFFFFF" BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="8">
                            <Border.Effect>
                                <DropShadowEffect Color="#00E5FF" BlurRadius="10" ShadowDepth="0" Opacity="0.3"/>
                            </Border.Effect>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Border Grid.Row="0" Background="{StaticResource PrimaryAccent}"
                                       CornerRadius="8,8,0,0" Padding="15,8">
                                    <ContentPresenter ContentSource="Header"
                                                    TextBlock.Foreground="#0D1421"
                                                    TextBlock.FontWeight="SemiBold"/>
                                </Border>
                                <ContentPresenter Grid.Row="1" Margin="15"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional ComboBox Style -->
        <Style x:Key="ProfessionalComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="#20FFFFFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource SecondaryAccent}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <ToggleButton x:Name="ToggleButton"
                                        Background="{TemplateBinding Background}"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        IsChecked="{Binding IsDropDownOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                        ClickMode="Press">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Border Background="{TemplateBinding Background}"
                                               BorderBrush="{TemplateBinding BorderBrush}"
                                               BorderThickness="{TemplateBinding BorderThickness}"
                                               CornerRadius="6">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <controls:FeatherIcon Grid.Column="1" IconName="chevron-down"
                                                                    IconColor="#1DE9B6" Width="16" Height="16"
                                                                    Margin="0,0,12,0"/>
                                            </Grid>
                                        </Border>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>
                            <ContentPresenter x:Name="ContentSite"
                                            Content="{TemplateBinding SelectionBoxItem}"
                                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                            Margin="12,0,30,0"
                                            VerticalAlignment="Center"
                                            HorizontalAlignment="Left"/>
                            <Popup x:Name="Popup"
                                   Placement="Bottom"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Slide">
                                <Grid MinWidth="{TemplateBinding ActualWidth}"
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    <Border Background="#1A2332" BorderBrush="{StaticResource SecondaryAccent}"
                                           BorderThickness="1" CornerRadius="6">
                                        <ScrollViewer>
                                            <StackPanel IsItemsHost="True"/>
                                        </ScrollViewer>
                                    </Border>
                                </Grid>
                            </Popup>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryAccent}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="ItemContainerStyle">
                <Setter.Value>
                    <Style TargetType="ComboBoxItem">
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="Padding" Value="12,8"/>
                        <Setter Property="FontSize" Value="12"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ComboBoxItem">
                                    <Border Background="{TemplateBinding Background}"
                                           Padding="{TemplateBinding Padding}">
                                        <ContentPresenter/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsHighlighted" Value="True">
                                            <Setter Property="Background" Value="{StaticResource PrimaryAccent}"/>
                                            <Setter Property="Foreground" Value="#0D1421"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="{StaticResource SecondaryAccent}"/>
                                            <Setter Property="Foreground" Value="#0D1421"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Label Style -->
        <Style x:Key="ProfessionalLabelStyle" TargetType="Label">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,5,0,2"/>
        </Style>

        <!-- Professional TextBox Style -->
        <Style x:Key="ProfessionalTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#20FFFFFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource SecondaryAccent}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="6">
                            <ScrollViewer x:Name="PART_ContentHost" Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Button Style -->
        <Style x:Key="ProfessionalButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryAccent}"/>
            <Setter Property="Foreground" Value="#0D1421"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6">
                            <Border.Effect>
                                <DropShadowEffect Color="#00E5FF" BlurRadius="8" ShadowDepth="0" Opacity="0.5"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryAccent}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional ListBox Style -->
        <Style x:Key="ProfessionalListBoxStyle" TargetType="ListBox">
            <Setter Property="Background" Value="#20FFFFFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="{StaticResource SecondaryAccent}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListBox">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="6">
                            <ScrollViewer>
                                <ItemsPresenter/>
                            </ScrollViewer>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Toggle Switch Style -->
        <Style x:Key="ProfessionalCheckBoxStyle" TargetType="CheckBox">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,8,0,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <StackPanel Orientation="Horizontal">
                            <!-- Modern Toggle Switch -->
                            <Border x:Name="ToggleTrack"
                                   Width="50" Height="26"
                                   Background="#4A5568"
                                   CornerRadius="13"
                                   Margin="0,0,12,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="#000000" BlurRadius="3" ShadowDepth="1" Opacity="0.3"/>
                                </Border.Effect>

                                <Grid>
                                    <!-- ON/OFF Text -->
                                    <TextBlock x:Name="OnText" Text="ON" FontSize="8" FontWeight="Bold"
                                              Foreground="White" HorizontalAlignment="Left"
                                              VerticalAlignment="Center" Margin="6,0,0,0" Opacity="0"/>
                                    <TextBlock x:Name="OffText" Text="OFF" FontSize="8" FontWeight="Bold"
                                              Foreground="White" HorizontalAlignment="Right"
                                              VerticalAlignment="Center" Margin="0,0,4,0" Opacity="1"/>

                                    <!-- Toggle Circle -->
                                    <Border x:Name="ToggleThumb"
                                           Width="20" Height="20"
                                           Background="White"
                                           CornerRadius="10"
                                           HorizontalAlignment="Left"
                                           VerticalAlignment="Center"
                                           Margin="3,0,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect Color="#000000" BlurRadius="2" ShadowDepth="1" Opacity="0.2"/>
                                        </Border.Effect>
                                        <Border.RenderTransform>
                                            <TranslateTransform x:Name="ThumbTransform" X="0"/>
                                        </Border.RenderTransform>
                                    </Border>
                                </Grid>
                            </Border>
                            <ContentPresenter VerticalAlignment="Center"/>
                        </StackPanel>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="ToggleTrack" Property="Background" Value="#1DE9B6"/>
                                <Setter TargetName="OnText" Property="Opacity" Value="1"/>
                                <Setter TargetName="OffText" Property="Opacity" Value="0"/>
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                           Storyboard.TargetProperty="X"
                                                           To="24" Duration="0:0:0.2">
                                                <DoubleAnimation.EasingFunction>
                                                    <QuadraticEase EasingMode="EaseOut"/>
                                                </DoubleAnimation.EasingFunction>
                                            </DoubleAnimation>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                           Storyboard.TargetProperty="X"
                                                           To="0" Duration="0:0:0.2">
                                                <DoubleAnimation.EasingFunction>
                                                    <QuadraticEase EasingMode="EaseOut"/>
                                                </DoubleAnimation.EasingFunction>
                                            </DoubleAnimation>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ToggleTrack" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#00E5FF" BlurRadius="8" ShadowDepth="0" Opacity="0.5"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="{StaticResource SecurityGradient}" CornerRadius="15"
           BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect Color="#00E5FF" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
        </Border.Effect>

        <Grid Margin="25">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Professional Header -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryAccent}"
                   Padding="20,15" CornerRadius="10" Margin="0,0,0,25">
                <Border.Effect>
                    <DropShadowEffect Color="#00E5FF" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                </Border.Effect>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <controls:FeatherIcon IconName="settings" IconColor="#0D1421"
                                        Width="28" Height="28" Margin="0,0,12,0"/>
                    <TextBlock Text="SafeLink Settings" FontSize="20" FontWeight="SemiBold"
                              Foreground="#0D1421" VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Settings Content -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- Language Settings -->
                    <GroupBox x:Name="LanguageGroupBox" Header="Language / اللغة" Style="{StaticResource ProfessionalGroupBoxStyle}">
                        <StackPanel>
                            <Label x:Name="InterfaceLanguageLabel" Content="Interface Language:" Style="{StaticResource ProfessionalLabelStyle}"/>
                            <ComboBox x:Name="LanguageComboBox" Style="{StaticResource ProfessionalComboBoxStyle}"
                                     SelectionChanged="LanguageComboBox_SelectionChanged">
                                <ComboBoxItem Content="English" Tag="en"/>
                                <ComboBoxItem Content="العربية" Tag="ar"/>
                            </ComboBox>

                            <TextBlock x:Name="LanguageRestartNote" Text="Language changes will take effect after restart"
                                      FontSize="10" Foreground="{StaticResource WarningAccent}"
                                      Margin="0,8,0,0"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Network Settings -->
                    <GroupBox x:Name="NetworkGroupBox" Header="Network Settings" Style="{StaticResource ProfessionalGroupBoxStyle}">
                        <StackPanel>
                            <Label x:Name="DiscoveryPortLabel" Content="Discovery Port:" Style="{StaticResource ProfessionalLabelStyle}"/>
                            <TextBox x:Name="PortTextBox" Text="8888" Style="{StaticResource ProfessionalTextBoxStyle}"/>

                            <CheckBox x:Name="AutoDiscoveryCheckBox" Content="Enable automatic user discovery"
                                     IsChecked="True" Style="{StaticResource ProfessionalCheckBoxStyle}"/>

                            <CheckBox x:Name="BroadcastPresenceCheckBox" Content="Broadcast my presence to network"
                                     IsChecked="True" Style="{StaticResource ProfessionalCheckBoxStyle}"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Printer Sharing -->
                    <GroupBox x:Name="PrinterGroupBox" Header="Printer Sharing" Style="{StaticResource ProfessionalGroupBoxStyle}">
                        <StackPanel>
                            <CheckBox x:Name="EnablePrinterSharingCheckBox" Content="Enable printer sharing"
                                     Style="{StaticResource ProfessionalCheckBoxStyle}"
                                     Checked="EnablePrinterSharingCheckBox_Checked"
                                     Unchecked="EnablePrinterSharingCheckBox_Unchecked"/>

                            <Label x:Name="AvailablePrintersLabel" Content="Available Printers:" Style="{StaticResource ProfessionalLabelStyle}" Margin="0,15,0,0"/>
                            <ListBox x:Name="PrintersListBox" Style="{StaticResource ProfessionalListBoxStyle}"
                                    Height="100" IsEnabled="False"/>

                            <Label x:Name="AuthorizedUsersLabel" Content="Authorized Users:" Style="{StaticResource ProfessionalLabelStyle}" Margin="0,15,0,0"/>
                            <ListBox x:Name="AuthorizedUsersListBox" Style="{StaticResource ProfessionalListBoxStyle}"
                                    Height="80" IsEnabled="False"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Security Settings -->
                    <GroupBox x:Name="SecurityGroupBox" Header="Security Settings" Style="{StaticResource ProfessionalGroupBoxStyle}">
                        <StackPanel>
                            <CheckBox x:Name="EncryptMessagesCheckBox" Content="Encrypt all messages (AES-256)"
                                     IsChecked="True" IsEnabled="False"
                                     Style="{StaticResource ProfessionalCheckBoxStyle}"/>

                            <CheckBox x:Name="LogActivityCheckBox" Content="Log user activity"
                                     IsChecked="True"
                                     Style="{StaticResource ProfessionalCheckBoxStyle}"/>

                            <Label x:Name="ReportShortcutLabel" Content="Report Problem Shortcut:" Style="{StaticResource ProfessionalLabelStyle}" Margin="0,15,0,0"/>
                            <TextBox x:Name="ReportShortcutTextBox" Text="F8" Style="{StaticResource ProfessionalTextBoxStyle}"
                                    IsReadOnly="True"/>
                        </StackPanel>
                    </GroupBox>

                    <!-- Data Management -->
                    <GroupBox x:Name="DataManagementGroupBox" Header="Data Management" Style="{StaticResource ProfessionalGroupBoxStyle}">
                        <StackPanel>
                            <!-- Clear Temporary Data -->
                            <Border Background="#15FFFFFF" BorderBrush="#FFB74D" BorderThickness="1"
                                   CornerRadius="8" Padding="15,12" Margin="0,0,0,15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="#FFB74D" CornerRadius="20" Width="40" Height="40" Margin="0,0,15,0">
                                        <controls:FeatherIcon IconName="shield" IconColor="White" Width="20" Height="20"
                                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>

                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock x:Name="ClearTempDataTitle" Text="Clear Temporary Data"
                                                  FontSize="14" FontWeight="SemiBold" Foreground="White" Margin="0,0,0,4"/>
                                        <TextBlock x:Name="ClearTempDataDesc"
                                                  Text="Remove login credentials, cached messages, and temporary files"
                                                  FontSize="11" Foreground="#B0BEC5" TextWrapping="Wrap"/>
                                    </StackPanel>

                                    <Border Grid.Column="2" Background="#FFB74D" CornerRadius="6" Cursor="Hand">
                                        <Button x:Name="ClearTempDataButton" Background="Transparent" Foreground="White"
                                               FontWeight="SemiBold" BorderThickness="0" Padding="15,8"
                                               Click="ClearTempDataButton_Click" Cursor="Hand">
                                            <StackPanel Orientation="Horizontal">
                                                <controls:FeatherIcon IconName="trash-2" IconColor="White" Width="14" Height="14" Margin="0,0,6,0"/>
                                                <TextBlock x:Name="ClearTempDataButtonText" Text="Clear" FontSize="11"/>
                                            </StackPanel>
                                        </Button>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Factory Reset -->
                            <Border Background="#15FFFFFF" BorderBrush="#FF5252" BorderThickness="1"
                                   CornerRadius="8" Padding="15,12">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" Background="#FF5252" CornerRadius="20" Width="40" Height="40" Margin="0,0,15,0">
                                        <controls:FeatherIcon IconName="shield" IconColor="White" Width="20" Height="20"
                                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>

                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                        <TextBlock x:Name="FactoryResetTitle" Text="Factory Reset"
                                                  FontSize="14" FontWeight="SemiBold" Foreground="White" Margin="0,0,0,4"/>
                                        <TextBlock x:Name="FactoryResetDesc"
                                                  Text="Reset all settings to default and remove all data (like fresh installation)"
                                                  FontSize="11" Foreground="#B0BEC5" TextWrapping="Wrap"/>
                                    </StackPanel>

                                    <Border Grid.Column="2" Background="#FF5252" CornerRadius="6" Cursor="Hand">
                                        <Button x:Name="FactoryResetButton" Background="Transparent" Foreground="White"
                                               FontWeight="SemiBold" BorderThickness="0" Padding="15,8"
                                               Click="FactoryResetButton_Click" Cursor="Hand">
                                            <StackPanel Orientation="Horizontal">
                                                <controls:FeatherIcon IconName="refresh-cw" IconColor="White" Width="14" Height="14" Margin="0,0,6,0"/>
                                                <TextBlock x:Name="FactoryResetButtonText" Text="Reset" FontSize="11"/>
                                            </StackPanel>
                                        </Button>
                                    </Border>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </GroupBox>

                    <!-- Device Information -->
                    <GroupBox x:Name="DeviceGroupBox" Header="Device Information" Style="{StaticResource ProfessionalGroupBoxStyle}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <Label Grid.Row="0" Grid.Column="0" x:Name="DeviceIdLabel" Content="Device ID:" Style="{StaticResource ProfessionalLabelStyle}"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="DeviceIdTextBlock"
                                      Foreground="White" VerticalAlignment="Center" Margin="15,0,0,0" FontSize="12"/>

                            <Label Grid.Row="1" Grid.Column="0" x:Name="MachineNameLabel" Content="Machine Name:" Style="{StaticResource ProfessionalLabelStyle}"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="MachineNameTextBlock"
                                      Foreground="White" VerticalAlignment="Center" Margin="15,0,0,0" FontSize="12"/>

                            <Label Grid.Row="2" Grid.Column="0" x:Name="OSVersionLabel" Content="OS Version:" Style="{StaticResource ProfessionalLabelStyle}"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="OSVersionTextBlock"
                                      Foreground="White" VerticalAlignment="Center" Margin="15,0,0,0" FontSize="12"/>

                            <Label Grid.Row="3" Grid.Column="0" x:Name="IPAddressLabel" Content="IP Address:" Style="{StaticResource ProfessionalLabelStyle}"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" x:Name="IPAddressTextBlock"
                                      Foreground="White" VerticalAlignment="Center" Margin="15,0,0,0" FontSize="12"/>
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </ScrollViewer>

            <!-- Professional Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,25,0,0">
                <Button x:Name="SaveButton" Style="{StaticResource ProfessionalButtonStyle}"
                       Click="SaveButton_Click" Width="120" Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="save" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock x:Name="SaveButtonText" Text="Save" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                <Button x:Name="CancelButton" Style="{StaticResource ProfessionalButtonStyle}"
                       Click="CancelButton_Click" Width="120">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="x" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock x:Name="CancelButtonText" Text="Cancel" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
