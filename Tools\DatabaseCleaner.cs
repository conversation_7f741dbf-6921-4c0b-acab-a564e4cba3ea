using System;
using System.IO;
using System.Diagnostics;

namespace SafeLink.Tools
{
    public static class DatabaseCleaner
    {
        public static void CleanAllDatabases()
        {
            try
            {
                Debug.WriteLine("🧹 Starting database cleanup...");

                // Clean AppData database
                CleanAppDataDatabase();

                // Clean program directory databases
                CleanProgramDirectoryDatabases();

                Debug.WriteLine("✅ Database cleanup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Database cleanup failed: {ex.Message}");
            }
        }

        private static void CleanAppDataDatabase()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var safeLinkDataPath = Path.Combine(appDataPath, "SafeLink");
                var dbPath = Path.Combine(safeLinkDataPath, "SafeLink.db");

                if (File.Exists(dbPath))
                {
                    File.Delete(dbPath);
                    Debug.WriteLine($"🗑️ Deleted AppData database: {dbPath}");
                }
                else
                {
                    Debug.WriteLine($"📂 AppData database not found: {dbPath}");
                }

                // Also clean the entire SafeLink folder if empty
                if (Directory.Exists(safeLinkDataPath) && Directory.GetFiles(safeLinkDataPath).Length == 0)
                {
                    Directory.Delete(safeLinkDataPath);
                    Debug.WriteLine($"🗑️ Deleted empty SafeLink AppData folder: {safeLinkDataPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to clean AppData database: {ex.Message}");
            }
        }

        private static void CleanProgramDirectoryDatabases()
        {
            try
            {
                // Clean current program directory
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory ?? Environment.CurrentDirectory;
                var dataPath = Path.Combine(baseDirectory, "Data");
                var dbPath = Path.Combine(dataPath, "SafeLink.db");

                if (File.Exists(dbPath))
                {
                    File.Delete(dbPath);
                    Debug.WriteLine($"🗑️ Deleted program directory database: {dbPath}");
                }

                // Clean Update directories
                var updatePath = Path.Combine(baseDirectory, "Update");
                if (Directory.Exists(updatePath))
                {
                    CleanUpdateDirectories(updatePath);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to clean program directory databases: {ex.Message}");
            }
        }

        private static void CleanUpdateDirectories(string updatePath)
        {
            try
            {
                var updateDirs = new[]
                {
                    Path.Combine(updatePath, "SafeLink Windows 10-11", "Data", "SafeLink.db"),
                    Path.Combine(updatePath, "SafeLink Windows 7", "Data", "SafeLink.db")
                };

                foreach (var dbPath in updateDirs)
                {
                    if (File.Exists(dbPath))
                    {
                        File.Delete(dbPath);
                        Debug.WriteLine($"🗑️ Deleted update directory database: {dbPath}");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to clean update directories: {ex.Message}");
            }
        }

        public static string GetDatabaseLocations()
        {
            var locations = new System.Text.StringBuilder();
            locations.AppendLine("📍 SafeLink Database Locations:");
            locations.AppendLine("================================");

            try
            {
                // AppData location
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var safeLinkDataPath = Path.Combine(appDataPath, "SafeLink");
                var appDataDbPath = Path.Combine(safeLinkDataPath, "SafeLink.db");
                
                locations.AppendLine($"🏠 AppData Database:");
                locations.AppendLine($"   Path: {appDataDbPath}");
                locations.AppendLine($"   Exists: {(File.Exists(appDataDbPath) ? "✅ Yes" : "❌ No")}");
                if (File.Exists(appDataDbPath))
                {
                    var fileInfo = new FileInfo(appDataDbPath);
                    locations.AppendLine($"   Size: {fileInfo.Length / 1024} KB");
                    locations.AppendLine($"   Modified: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
                }
                locations.AppendLine();

                // Program directory
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory ?? Environment.CurrentDirectory;
                var programDbPath = Path.Combine(baseDirectory, "Data", "SafeLink.db");
                
                locations.AppendLine($"💻 Program Directory Database:");
                locations.AppendLine($"   Path: {programDbPath}");
                locations.AppendLine($"   Exists: {(File.Exists(programDbPath) ? "✅ Yes" : "❌ No")}");
                if (File.Exists(programDbPath))
                {
                    var fileInfo = new FileInfo(programDbPath);
                    locations.AppendLine($"   Size: {fileInfo.Length / 1024} KB");
                    locations.AppendLine($"   Modified: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
                }
                locations.AppendLine();

                // Update directories
                var updatePath = Path.Combine(baseDirectory, "Update");
                if (Directory.Exists(updatePath))
                {
                    locations.AppendLine($"📦 Update Directories:");
                    
                    var updateDirs = new[]
                    {
                        ("Windows 10-11", Path.Combine(updatePath, "SafeLink Windows 10-11", "Data", "SafeLink.db")),
                        ("Windows 7", Path.Combine(updatePath, "SafeLink Windows 7", "Data", "SafeLink.db"))
                    };

                    foreach (var (name, dbPath) in updateDirs)
                    {
                        locations.AppendLine($"   {name}: {(File.Exists(dbPath) ? "✅ Yes" : "❌ No")}");
                        if (File.Exists(dbPath))
                        {
                            var fileInfo = new FileInfo(dbPath);
                            locations.AppendLine($"      Size: {fileInfo.Length / 1024} KB, Modified: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");
                        }
                    }
                }

                locations.AppendLine();
                locations.AppendLine("💡 Note: Messages persist because databases are copied with the program.");
                locations.AppendLine("💡 Solution: Use AppData location to avoid copying databases.");
            }
            catch (Exception ex)
            {
                locations.AppendLine($"❌ Error getting database locations: {ex.Message}");
            }

            return locations.ToString();
        }

        public static void ShowDatabaseCleanupDialog()
        {
            try
            {
                var locations = GetDatabaseLocations();
                Debug.WriteLine(locations);

                // You can add a MessageBox here if needed
                Debug.WriteLine("🧹 To clean all databases, call DatabaseCleaner.CleanAllDatabases()");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Failed to show database cleanup dialog: {ex.Message}");
            }
        }
    }
}
