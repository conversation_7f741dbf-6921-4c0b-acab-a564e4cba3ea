<UserControl x:Class="SafeLink.Controls.ConnectionNotification"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Width="320" Height="90">
    
    <UserControl.Resources>
        <!-- Drop Shadow Effect -->
        <DropShadowEffect x:Key="DropShadowEffect" Color="Black" BlurRadius="10" 
                         ShadowDepth="3" Opacity="0.3"/>
        
        <!-- Animation Storyboards -->
        <Storyboard x:Key="SlideInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="350" To="0" Duration="0:0:0.5">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="0" To="1" Duration="0:0:0.3"/>
        </Storyboard>
        
        <Storyboard x:Key="SlideOutAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                           From="0" To="350" Duration="0:0:0.3">
                <DoubleAnimation.EasingFunction>
                    <BackEase EasingMode="EaseIn"/>
                </DoubleAnimation.EasingFunction>
            </DoubleAnimation>
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                           From="1" To="0" Duration="0:0:0.3"/>
        </Storyboard>
        
        <Storyboard x:Key="GlowAnimation" RepeatBehavior="Forever" AutoReverse="True">
            <DoubleAnimation Storyboard.TargetName="GlowBorder" 
                           Storyboard.TargetProperty="Opacity"
                           From="0.3" To="0.8" Duration="0:0:1"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RenderTransform>
            <TranslateTransform/>
        </Grid.RenderTransform>
        
        <!-- Glow Effect -->
        <Border x:Name="GlowBorder" Background="#4CAF50" CornerRadius="12" 
                Opacity="0.3" Margin="-2"/>
        
        <!-- Main Container -->
        <Border Background="#2C2C2C" CornerRadius="10" BorderBrush="#404040" BorderThickness="1"
                Effect="{StaticResource DropShadowEffect}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Rank Icon Container -->
                <Border Grid.Column="0" Width="60" Height="60" Margin="10"
                        x:Name="RankContainer" CornerRadius="30">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#4CAF50" Offset="0"/>
                            <GradientStop Color="#45A049" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    
                    <!-- Rank Icon -->
                    <TextBlock x:Name="RankIcon" Text="👑" FontSize="24" 
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                
                <!-- Content -->
                <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="5,0,10,0">
                    <TextBlock x:Name="UsernameText" Text="Username" FontSize="15" 
                              FontWeight="Bold" Foreground="White" TextTrimming="CharacterEllipsis"/>
                    <TextBlock x:Name="StatusText" Text="متصل الآن" FontSize="12" 
                              Foreground="#B0B0B0" Margin="0,2,0,0"/>
                    <TextBlock x:Name="RankText" Text="مطور" FontSize="11" 
                              Foreground="#4CAF50" FontWeight="SemiBold"/>
                </StackPanel>
                
                <!-- Connection Status Indicator -->
                <Border Grid.Column="2" Width="12" Height="12" Margin="0,0,15,0"
                        x:Name="StatusIndicator" CornerRadius="6" Background="#4CAF50">
                    <Border.Effect>
                        <DropShadowEffect Color="#4CAF50" BlurRadius="8" ShadowDepth="0" Opacity="0.8"/>
                    </Border.Effect>
                </Border>
            </Grid>
        </Border>
    </Grid>
</UserControl>
