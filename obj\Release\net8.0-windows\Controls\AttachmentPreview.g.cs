﻿#pragma checksum "..\..\..\..\Controls\AttachmentPreview.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "BE4DA1EBF4FAC89857353EB75420FBC29723C959"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Controls {
    
    
    /// <summary>
    /// AttachmentPreview
    /// </summary>
    public partial class AttachmentPreview : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MainBorder;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image ImagePreview;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileIcon;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeText;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadButton;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenButton;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Controls\AttachmentPreview.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/controls/attachmentpreview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\AttachmentPreview.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainBorder = ((System.Windows.Controls.Border)(target));
            
            #line 37 "..\..\..\..\Controls\AttachmentPreview.xaml"
            this.MainBorder.MouseEnter += new System.Windows.Input.MouseEventHandler(this.MainBorder_MouseEnter);
            
            #line default
            #line hidden
            
            #line 38 "..\..\..\..\Controls\AttachmentPreview.xaml"
            this.MainBorder.MouseLeave += new System.Windows.Input.MouseEventHandler(this.MainBorder_MouseLeave);
            
            #line default
            #line hidden
            
            #line 39 "..\..\..\..\Controls\AttachmentPreview.xaml"
            this.MainBorder.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.MainBorder_MouseLeftButtonUp);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ImagePreview = ((System.Windows.Controls.Image)(target));
            return;
            case 3:
            this.FileIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.FileSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.DownloadButton = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\..\Controls\AttachmentPreview.xaml"
            this.DownloadButton.Click += new System.Windows.RoutedEventHandler(this.DownloadButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.OpenButton = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\Controls\AttachmentPreview.xaml"
            this.OpenButton.Click += new System.Windows.RoutedEventHandler(this.OpenButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.RemoveButton = ((System.Windows.Controls.Button)(target));
            
            #line 174 "..\..\..\..\Controls\AttachmentPreview.xaml"
            this.RemoveButton.Click += new System.Windows.RoutedEventHandler(this.RemoveButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

