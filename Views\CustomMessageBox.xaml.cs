using System;
using System.Windows;
using System.Windows.Media;
using SafeLink.Core;

namespace SafeLink.Views
{
    public partial class CustomMessageBox : Window
    {
        public enum MessageBoxType
        {
            Information,
            Warning,
            Error,
            Question
        }

        public enum MessageBoxButtons
        {
            OK,
            OKCancel,
            YesNo
        }

        public bool? Result { get; private set; }

        private CustomMessageBox()
        {
            InitializeComponent();

            // Subscribe to language changes
            LanguageManager.LanguageChanged += UpdateLanguage;
            UpdateLanguage();

            // Handle window closing to unsubscribe from events
            Closing += (s, e) => LanguageManager.LanguageChanged -= UpdateLanguage;
        }

        public static bool? Show(string message, string title = "SafeLink",
            MessageBoxType type = MessageBoxType.Information,
            MessageBoxButtons buttons = MessageBoxButtons.OK,
            Window owner = null)
        {
            try
            {
                var messageBox = new CustomMessageBox();

                if (owner != null)
                {
                    messageBox.Owner = owner;
                }
                else if (Application.Current.MainWindow != null)
                {
                    messageBox.Owner = Application.Current.MainWindow;
                }

                messageBox.SetupMessageBox(message, title, type, buttons);
                messageBox.ShowDialog();

                return messageBox.Result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing custom message box: {ex.Message}");
                // Fallback to standard MessageBox
                System.Windows.MessageBox.Show(message, title);
                return true;
            }
        }

        private void SetupMessageBox(string message, string title, MessageBoxType type, MessageBoxButtons buttons)
        {
            TitleText.Text = title;
            MessageText.Text = message;

            // Set shield color based on type - Professional colors
            switch (type)
            {
                case MessageBoxType.Information:
                    MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0x1D, 0xE9, 0xB6)); // Success Green
                    break;
                case MessageBoxType.Warning:
                    MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0xFF, 0xB7, 0x4D)); // Warning Orange
                    break;
                case MessageBoxType.Error:
                    MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0xFF, 0x52, 0x52)); // Error Red
                    break;
                case MessageBoxType.Question:
                    MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0x00, 0xE5, 0xFF)); // Question Blue
                    break;
            }

            // Setup buttons
            switch (buttons)
            {
                case MessageBoxButtons.OK:
                    CancelButton.Visibility = Visibility.Collapsed;
                    OkButtonText.Text = LanguageManager.GetString("OK") ?? "OK";
                    break;
                case MessageBoxButtons.OKCancel:
                    CancelButton.Visibility = Visibility.Visible;
                    OkButtonText.Text = LanguageManager.GetString("OK") ?? "OK";
                    CancelButtonText.Text = LanguageManager.GetString("Cancel") ?? "Cancel";
                    break;
                case MessageBoxButtons.YesNo:
                    CancelButton.Visibility = Visibility.Visible;
                    OkButtonText.Text = LanguageManager.GetString("Yes") ?? "Yes";
                    CancelButtonText.Text = LanguageManager.GetString("No") ?? "No";
                    break;
            }
        }

        private void UpdateLanguage()
        {
            try
            {
                // Update button texts if they haven't been set by SetupMessageBox yet
                if (OkButtonText.Text == "OK")
                    OkButtonText.Text = LanguageManager.GetString("OK") ?? "OK";
                if (CancelButtonText.Text == "Cancel")
                    CancelButtonText.Text = LanguageManager.GetString("Cancel") ?? "Cancel";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update language: {ex.Message}");
            }
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            Result = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            Result = false;
            Close();
        }

        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Escape)
            {
                Result = false;
                Close();
            }
            else if (e.Key == System.Windows.Input.Key.Enter)
            {
                Result = true;
                Close();
            }

            base.OnKeyDown(e);
        }
    }
}
