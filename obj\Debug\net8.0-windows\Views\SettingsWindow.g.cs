﻿#pragma checksum "..\..\..\..\Views\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F50A0B10A27D84DFC046DA74340A3F166BC578EE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Views {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 374 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox LanguageGroupBox;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label InterfaceLanguageLabel;
        
        #line default
        #line hidden
        
        
        #line 377 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LanguageRestartNote;
        
        #line default
        #line hidden
        
        
        #line 390 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox NetworkGroupBox;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label DiscoveryPortLabel;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PortTextBox;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoDiscoveryCheckBox;
        
        #line default
        #line hidden
        
        
        #line 398 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BroadcastPresenceCheckBox;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox PrinterGroupBox;
        
        #line default
        #line hidden
        
        
        #line 406 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnablePrinterSharingCheckBox;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label AvailablePrintersLabel;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox PrintersListBox;
        
        #line default
        #line hidden
        
        
        #line 415 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label AuthorizedUsersLabel;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox AuthorizedUsersListBox;
        
        #line default
        #line hidden
        
        
        #line 422 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox SecurityGroupBox;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EncryptMessagesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LogActivityCheckBox;
        
        #line default
        #line hidden
        
        
        #line 434 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label ReportShortcutLabel;
        
        #line default
        #line hidden
        
        
        #line 435 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReportShortcutTextBox;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox DataManagementGroupBox;
        
        #line default
        #line hidden
        
        
        #line 459 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClearTempDataTitle;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClearTempDataDesc;
        
        #line default
        #line hidden
        
        
        #line 467 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearTempDataButton;
        
        #line default
        #line hidden
        
        
        #line 472 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ClearTempDataButtonText;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FactoryResetTitle;
        
        #line default
        #line hidden
        
        
        #line 497 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FactoryResetDesc;
        
        #line default
        #line hidden
        
        
        #line 503 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FactoryResetButton;
        
        #line default
        #line hidden
        
        
        #line 508 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FactoryResetButtonText;
        
        #line default
        #line hidden
        
        
        #line 518 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox DeviceGroupBox;
        
        #line default
        #line hidden
        
        
        #line 531 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label DeviceIdLabel;
        
        #line default
        #line hidden
        
        
        #line 532 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceIdTextBlock;
        
        #line default
        #line hidden
        
        
        #line 535 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label MachineNameLabel;
        
        #line default
        #line hidden
        
        
        #line 536 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MachineNameTextBlock;
        
        #line default
        #line hidden
        
        
        #line 539 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label OSVersionLabel;
        
        #line default
        #line hidden
        
        
        #line 540 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OSVersionTextBlock;
        
        #line default
        #line hidden
        
        
        #line 543 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Label IPAddressLabel;
        
        #line default
        #line hidden
        
        
        #line 544 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IPAddressTextBlock;
        
        #line default
        #line hidden
        
        
        #line 553 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 557 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SaveButtonText;
        
        #line default
        #line hidden
        
        
        #line 560 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        
        #line 564 "..\..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CancelButtonText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/views/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LanguageGroupBox = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 2:
            this.InterfaceLanguageLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 3:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 378 "..\..\..\..\Views\SettingsWindow.xaml"
            this.LanguageComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LanguageComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.LanguageRestartNote = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.NetworkGroupBox = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 6:
            this.DiscoveryPortLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 7:
            this.PortTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.AutoDiscoveryCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.BroadcastPresenceCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.PrinterGroupBox = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 11:
            this.EnablePrinterSharingCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 408 "..\..\..\..\Views\SettingsWindow.xaml"
            this.EnablePrinterSharingCheckBox.Checked += new System.Windows.RoutedEventHandler(this.EnablePrinterSharingCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 409 "..\..\..\..\Views\SettingsWindow.xaml"
            this.EnablePrinterSharingCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.EnablePrinterSharingCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 12:
            this.AvailablePrintersLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 13:
            this.PrintersListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 14:
            this.AuthorizedUsersLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 15:
            this.AuthorizedUsersListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 16:
            this.SecurityGroupBox = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 17:
            this.EncryptMessagesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 426 "..\..\..\..\Views\SettingsWindow.xaml"
            this.EncryptMessagesCheckBox.Checked += new System.Windows.RoutedEventHandler(this.EncryptMessagesCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 427 "..\..\..\..\Views\SettingsWindow.xaml"
            this.EncryptMessagesCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.EncryptMessagesCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 18:
            this.LogActivityCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.ReportShortcutLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 20:
            this.ReportShortcutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.DataManagementGroupBox = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 22:
            this.ClearTempDataTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.ClearTempDataDesc = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.ClearTempDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 469 "..\..\..\..\Views\SettingsWindow.xaml"
            this.ClearTempDataButton.Click += new System.Windows.RoutedEventHandler(this.ClearTempDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.ClearTempDataButtonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.FactoryResetTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.FactoryResetDesc = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.FactoryResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 505 "..\..\..\..\Views\SettingsWindow.xaml"
            this.FactoryResetButton.Click += new System.Windows.RoutedEventHandler(this.FactoryResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.FactoryResetButtonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.DeviceGroupBox = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 31:
            this.DeviceIdLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 32:
            this.DeviceIdTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.MachineNameLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 34:
            this.MachineNameTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.OSVersionLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 36:
            this.OSVersionTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.IPAddressLabel = ((System.Windows.Controls.Label)(target));
            return;
            case 38:
            this.IPAddressTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 554 "..\..\..\..\Views\SettingsWindow.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.SaveButtonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 561 "..\..\..\..\Views\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.CancelButtonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

