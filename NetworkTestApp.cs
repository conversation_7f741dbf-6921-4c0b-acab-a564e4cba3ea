using System;
using System.Threading.Tasks;
using SafeLink.Tools;

class NetworkTestApp
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("🚀 SafeLink Network Test Utility");
        Console.WriteLine("=================================\n");
        
        if (args.Length > 0 && args[0] == "send")
        {
            int port = args.Length > 1 && int.TryParse(args[1], out int p) ? p : 8891;
            await QuickNetworkTest.SendTestDiscovery(port);
        }
        else
        {
            await QuickNetworkTest.TestNetworkDiscovery();
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
