using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.IO;
using System.Threading.Tasks;
using BCrypt.Net;
using SafeLink.Models;

namespace SafeLink.Core
{
    public class DatabaseManager : IDisposable
    {
        private SQLiteConnection _connection;
        private readonly string _connectionString;

        public DatabaseManager()
        {
            try
            {
                // Use AppData for database to avoid copying with program
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                var safeLinkDataPath = Path.Combine(appDataPath, "SafeLink");
                var dbPath = Path.Combine(safeLinkDataPath, "SafeLink.db");

                // Create directory if it doesn't exist
                if (!Directory.Exists(safeLinkDataPath))
                {
                    Directory.CreateDirectory(safeLinkDataPath);
                    System.Diagnostics.Debug.WriteLine($"Created SafeLink data directory: {safeLinkDataPath}");
                }

                _connectionString = $"Data Source={dbPath};Version=3;";
                System.Diagnostics.Debug.WriteLine($"Database path (AppData): {dbPath}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating DatabaseManager: {ex.Message}");
                // Fallback to program directory
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory ?? Environment.CurrentDirectory;
                var dataPath = Path.Combine(baseDirectory, "Data");
                var dbPath = Path.Combine(dataPath, "SafeLink.db");
                _connectionString = $"Data Source={dbPath};Version=3;";
                System.Diagnostics.Debug.WriteLine($"Database path (fallback): {dbPath}");
            }
        }

        public void Initialize()
        {
            try
            {
                // Ensure directory exists
                var baseDirectory = AppDomain.CurrentDomain.BaseDirectory ?? Environment.CurrentDirectory;
                var dataPath = Path.Combine(baseDirectory, "Data");
                if (!Directory.Exists(dataPath))
                {
                    Directory.CreateDirectory(dataPath);
                    System.Diagnostics.Debug.WriteLine($"Created data directory: {dataPath}");
                }

                System.Diagnostics.Debug.WriteLine($"Connecting to database with: {_connectionString}");
                _connection = new SQLiteConnection(_connectionString);
                _connection.Open();
                CreateTables();
                CreateDeveloperAccount();

                System.Diagnostics.Debug.WriteLine("✅ Database initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"🔴 Database initialization error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"🔴 Stack trace: {ex.StackTrace}");
                throw new Exception($"Failed to initialize database: {ex.Message}", ex);
            }
        }

        private void CreateTables()
        {
            var commands = new[]
            {
                @"CREATE TABLE IF NOT EXISTS Users (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Username TEXT UNIQUE NOT NULL,
                    PasswordHash TEXT NOT NULL,
                    ArabicUsername TEXT,
                    DeviceId TEXT NOT NULL,
                    Role INTEGER DEFAULT 0,
                    Status INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastLogin DATETIME,
                    IsActive BOOLEAN DEFAULT 1
                )",

                @"CREATE TABLE IF NOT EXISTS Messages (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    FromUserId INTEGER NOT NULL,
                    ToUserId INTEGER NOT NULL,
                    Content TEXT NOT NULL,
                    Type INTEGER DEFAULT 0,
                    FilePath TEXT,
                    Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    IsRead BOOLEAN DEFAULT 0,
                    IsEncrypted BOOLEAN DEFAULT 1,
                    Status INTEGER DEFAULT 0,
                    DeliveredAt DATETIME,
                    ReadAt DATETIME,
                    MessageId TEXT UNIQUE,
                    FOREIGN KEY (FromUserId) REFERENCES Users (Id),
                    FOREIGN KEY (ToUserId) REFERENCES Users (Id)
                )",

                @"CREATE TABLE IF NOT EXISTS ChatSessions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    User1Id INTEGER NOT NULL,
                    User2Id INTEGER NOT NULL,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    LastActivity DATETIME DEFAULT CURRENT_TIMESTAMP,
                    IsActive BOOLEAN DEFAULT 1,
                    FOREIGN KEY (User1Id) REFERENCES Users (Id),
                    FOREIGN KEY (User2Id) REFERENCES Users (Id)
                )",

                @"CREATE TABLE IF NOT EXISTS ProblemReports (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER NOT NULL,
                    ScreenshotPath TEXT,
                    Description TEXT,
                    UserAgent TEXT,
                    Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    Status INTEGER DEFAULT 0,
                    FOREIGN KEY (UserId) REFERENCES Users (Id)
                )",

                @"CREATE TABLE IF NOT EXISTS SystemLogs (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    UserId INTEGER,
                    Action TEXT NOT NULL,
                    Details TEXT,
                    IpAddress TEXT,
                    Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    Level INTEGER DEFAULT 0,
                    FOREIGN KEY (UserId) REFERENCES Users (Id)
                )",

                @"CREATE TABLE IF NOT EXISTS Attachments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    MessageId INTEGER NOT NULL,
                    FileName TEXT NOT NULL,
                    OriginalFileName TEXT NOT NULL,
                    FileExtension TEXT NOT NULL,
                    FileSize INTEGER NOT NULL,
                    FilePath TEXT NOT NULL,
                    FileHash TEXT,
                    MimeType TEXT,
                    UploadedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UploadedBy INTEGER NOT NULL,
                    IsImage BOOLEAN DEFAULT 0,
                    IsVideo BOOLEAN DEFAULT 0,
                    IsAudio BOOLEAN DEFAULT 0,
                    IsDocument BOOLEAN DEFAULT 0,
                    FOREIGN KEY (MessageId) REFERENCES Messages (Id) ON DELETE CASCADE,
                    FOREIGN KEY (UploadedBy) REFERENCES Users (Id)
                )"
            };

            foreach (var command in commands)
            {
                try
                {
                    using var cmd = new SQLiteCommand(command, _connection);
                    cmd.ExecuteNonQuery();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"🔴 Error creating table: {ex.Message}");
                    throw;
                }
            }

            System.Diagnostics.Debug.WriteLine("✅ Database tables created successfully");
        }

        private void CreateDeveloperAccount()
        {
            try
            {
                // Temporarily use fixed device ID for development
                var tempDeviceId = "TEMP_DEVICE_ID";

                // Create default accounts with temporary device ID
                var defaultAccounts = new[]
                {
                    new { Username = "Drikon", Password = "Drikon", Role = UserRole.Developer, DeviceId = tempDeviceId },
                    new { Username = "admin", Password = "admin", Role = UserRole.Admin, DeviceId = tempDeviceId },
                    new { Username = "user1", Password = "user1", Role = UserRole.User, DeviceId = tempDeviceId },
                    new { Username = "user2", Password = "user2", Role = UserRole.User, DeviceId = tempDeviceId }
                };

                foreach (var account in defaultAccounts)
                {
                    using var checkCmd = new SQLiteCommand("SELECT COUNT(*) FROM Users WHERE Username = @username", _connection);
                    checkCmd.Parameters.AddWithValue("@username", account.Username);
                    var count = Convert.ToInt32(checkCmd.ExecuteScalar());

                    if (count == 0)
                    {
                        // Create new account
                        var passwordHash = BCrypt.Net.BCrypt.HashPassword(account.Password);

                        using var insertCmd = new SQLiteCommand(
                            @"INSERT INTO Users (Username, PasswordHash, DeviceId, Role)
                              VALUES (@username, @passwordHash, @deviceId, @role)", _connection);

                        insertCmd.Parameters.AddWithValue("@username", account.Username);
                        insertCmd.Parameters.AddWithValue("@passwordHash", passwordHash);
                        insertCmd.Parameters.AddWithValue("@deviceId", account.DeviceId);
                        insertCmd.Parameters.AddWithValue("@role", (int)account.Role);

                        insertCmd.ExecuteNonQuery();

                        LogAction(null, "system", $"Account created: {account.Username} ({account.Role}) on device {account.DeviceId}");
                        System.Diagnostics.Debug.WriteLine($"✅ Account created: {account.Username} ({account.Role})");
                    }
                    else if (account.Username == "Drikon")
                    {
                        // Update developer account password and device ID
                        var passwordHash = BCrypt.Net.BCrypt.HashPassword(account.Password);

                        using var updateCmd = new SQLiteCommand(
                            @"UPDATE Users SET PasswordHash = @passwordHash, DeviceId = @deviceId, Role = @role WHERE Username = @username", _connection);

                        updateCmd.Parameters.AddWithValue("@passwordHash", passwordHash);
                        updateCmd.Parameters.AddWithValue("@deviceId", account.DeviceId);
                        updateCmd.Parameters.AddWithValue("@role", (int)UserRole.Developer);
                        updateCmd.Parameters.AddWithValue("@username", account.Username);

                        updateCmd.ExecuteNonQuery();

                        LogAction(null, "system", $"Developer account updated: {account.Username} on device {account.DeviceId} with Developer role");
                        System.Diagnostics.Debug.WriteLine($"✅ Developer account updated: {account.Username} with Developer role");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"🔴 Error creating/updating accounts: {ex.Message}");
                throw;
            }
        }

        public async Task<User> AuthenticateUserAsync(string username, string password, string deviceId)
        {
            return await Task.Run(() =>
            {
                try
                {
                    using var cmd = new SQLiteCommand(
                        "SELECT * FROM Users WHERE Username = @username AND IsActive = 1", _connection);
                    cmd.Parameters.AddWithValue("@username", username);

                    using var reader = cmd.ExecuteReader();
                    if (reader.Read())
                    {
                        var user = MapUserFromReader(reader);
                        var storedHash = reader["PasswordHash"].ToString();

                        // TEMPORARILY DISABLED: Device ID verification for development
                        System.Diagnostics.Debug.WriteLine($"🔧 TEMP: Device ID verification bypassed for user: {username}");
                        System.Diagnostics.Debug.WriteLine($"🔧 TEMP: Expected device: {user.DeviceId}, Got: {deviceId} (ignored)");

                        // TODO: Re-enable device verification after development phase
                        /*
                        if (user.Role != UserRole.Developer && user.DeviceId != deviceId)
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ Unauthorized device access attempt: {deviceId} for user {username}");
                            System.Diagnostics.Debug.WriteLine($"⚠️ Expected device: {user.DeviceId}, Got: {deviceId}");
                            throw new UnauthorizedAccessException("Unauthorized device. Please contact administrator.");
                        }
                        */

                        // Verify password
                        if (BCrypt.Net.BCrypt.Verify(password, storedHash))
                        {
                            // Update last login
                            reader.Close();
                            UpdateLastLogin(user.Id);
                            LogAction(user.Id, "login", $"User logged in from device {deviceId}");
                            System.Diagnostics.Debug.WriteLine($"✅ User authenticated: {username}");
                            return user;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ Invalid password for user: {username}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ User not found: {username}");
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"🔴 Authentication error: {ex.Message}");
                    throw;
                }
            });
        }

        public async Task<bool> RegisterUserAsync(string username, string password, string deviceId)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var passwordHash = BCrypt.Net.BCrypt.HashPassword(password);

                    using var cmd = new SQLiteCommand(
                        @"INSERT INTO Users (Username, PasswordHash, DeviceId)
                          VALUES (@username, @passwordHash, @deviceId)", _connection);

                    cmd.Parameters.AddWithValue("@username", username);
                    cmd.Parameters.AddWithValue("@passwordHash", passwordHash);
                    cmd.Parameters.AddWithValue("@deviceId", deviceId);

                    cmd.ExecuteNonQuery();
                    LogAction(null, "register", $"New user registered: {username}");
                    return true;
                }
                catch (SQLiteException ex) when (ex.Message.Contains("UNIQUE constraint failed"))
                {
                    return false;
                }
            });
        }

        public async Task<List<Message>> GetChatHistoryAsync(int user1Id, int user2Id, int limit = 50)
        {
            return await Task.Run(() =>
            {
                var messages = new List<Message>();

                using var cmd = new SQLiteCommand(
                    @"SELECT m.Id, m.FromUserId, m.ToUserId, m.Content, m.Type, m.FilePath,
                             m.Timestamp, m.IsRead, m.IsEncrypted, m.Status, m.DeliveredAt,
                             m.ReadAt, m.MessageId, u.Username as SenderName
                      FROM Messages m
                      JOIN Users u ON m.FromUserId = u.Id
                      WHERE (m.FromUserId = @user1 AND m.ToUserId = @user2)
                         OR (m.FromUserId = @user2 AND m.ToUserId = @user1)
                      ORDER BY m.Timestamp DESC
                      LIMIT @limit", _connection);

                cmd.Parameters.AddWithValue("@user1", user1Id);
                cmd.Parameters.AddWithValue("@user2", user2Id);
                cmd.Parameters.AddWithValue("@limit", limit);

                using var reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    messages.Add(MapMessageFromReader(reader));
                }

                messages.Reverse(); // Show oldest first
                return messages;
            });
        }

        public async Task SaveMessageAsync(Message message)
        {
            await Task.Run(() =>
            {
                using var cmd = new SQLiteCommand(
                    @"INSERT INTO Messages (FromUserId, ToUserId, Content, Type, FilePath, IsEncrypted, Status, MessageId)
                      VALUES (@fromUserId, @toUserId, @content, @type, @filePath, @isEncrypted, @status, @messageId)", _connection);

                cmd.Parameters.AddWithValue("@fromUserId", message.FromUserId);
                cmd.Parameters.AddWithValue("@toUserId", message.ToUserId);
                cmd.Parameters.AddWithValue("@content", message.Content);
                cmd.Parameters.AddWithValue("@type", (int)message.Type);
                cmd.Parameters.AddWithValue("@filePath", message.FilePath ?? "");
                cmd.Parameters.AddWithValue("@isEncrypted", message.IsEncrypted);
                cmd.Parameters.AddWithValue("@status", (int)message.Status);
                cmd.Parameters.AddWithValue("@messageId", message.MessageId);

                cmd.ExecuteNonQuery();
            });
        }

        public async Task SaveAttachmentAsync(Attachment attachment)
        {
            await Task.Run(() =>
            {
                using var cmd = new SQLiteCommand(
                    @"INSERT INTO Attachments (MessageId, FileName, OriginalFileName, FileExtension, FileSize, FilePath, FileHash, MimeType, UploadedBy, IsImage, IsVideo, IsAudio, IsDocument)
                      VALUES (@messageId, @fileName, @originalFileName, @fileExtension, @fileSize, @filePath, @fileHash, @mimeType, @uploadedBy, @isImage, @isVideo, @isAudio, @isDocument)", _connection);

                cmd.Parameters.AddWithValue("@messageId", attachment.MessageId);
                cmd.Parameters.AddWithValue("@fileName", attachment.FileName);
                cmd.Parameters.AddWithValue("@originalFileName", attachment.OriginalFileName);
                cmd.Parameters.AddWithValue("@fileExtension", attachment.FileExtension);
                cmd.Parameters.AddWithValue("@fileSize", attachment.FileSize);
                cmd.Parameters.AddWithValue("@filePath", attachment.FilePath);
                cmd.Parameters.AddWithValue("@fileHash", attachment.FileHash ?? string.Empty);
                cmd.Parameters.AddWithValue("@mimeType", attachment.MimeType ?? string.Empty);
                cmd.Parameters.AddWithValue("@uploadedBy", attachment.UploadedBy);
                cmd.Parameters.AddWithValue("@isImage", attachment.IsImage);
                cmd.Parameters.AddWithValue("@isVideo", attachment.IsVideo);
                cmd.Parameters.AddWithValue("@isAudio", attachment.IsAudio);
                cmd.Parameters.AddWithValue("@isDocument", attachment.IsDocument);

                cmd.ExecuteNonQuery();
            });
        }

        public async Task<List<Attachment>> GetMessageAttachmentsAsync(int messageId)
        {
            return await Task.Run(() =>
            {
                var attachments = new List<Attachment>();
                using var cmd = new SQLiteCommand(
                    "SELECT * FROM Attachments WHERE MessageId = @messageId ORDER BY UploadedAt", _connection);
                cmd.Parameters.AddWithValue("@messageId", messageId);

                using var reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    attachments.Add(new Attachment
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        MessageId = Convert.ToInt32(reader["MessageId"]),
                        FileName = reader["FileName"].ToString(),
                        OriginalFileName = reader["OriginalFileName"].ToString(),
                        FileExtension = reader["FileExtension"].ToString(),
                        FileSize = Convert.ToInt64(reader["FileSize"]),
                        FilePath = reader["FilePath"].ToString(),
                        FileHash = reader["FileHash"] != DBNull.Value ? reader["FileHash"].ToString() : string.Empty,
                        MimeType = reader["MimeType"] != DBNull.Value ? reader["MimeType"].ToString() : string.Empty,
                        UploadedAt = Convert.ToDateTime(reader["UploadedAt"]),
                        UploadedBy = Convert.ToInt32(reader["UploadedBy"]),
                        IsImage = Convert.ToBoolean(reader["IsImage"]),
                        IsVideo = Convert.ToBoolean(reader["IsVideo"]),
                        IsAudio = Convert.ToBoolean(reader["IsAudio"]),
                        IsDocument = Convert.ToBoolean(reader["IsDocument"])
                    });
                }
                return attachments;
            });
        }

        public async Task UpdateMessageStatusAsync(string messageId, MessageStatus status)
        {
            await Task.Run(() =>
            {
                using var cmd = new SQLiteCommand(
                    @"UPDATE Messages SET Status = @status,
                      DeliveredAt = CASE WHEN @status >= 2 AND DeliveredAt IS NULL THEN CURRENT_TIMESTAMP ELSE DeliveredAt END,
                      ReadAt = CASE WHEN @status = 3 AND ReadAt IS NULL THEN CURRENT_TIMESTAMP ELSE ReadAt END
                      WHERE MessageId = @messageId", _connection);

                cmd.Parameters.AddWithValue("@status", (int)status);
                cmd.Parameters.AddWithValue("@messageId", messageId);

                cmd.ExecuteNonQuery();
            });
        }

        public void LogAction(int? userId, string action, string details, string ipAddress = null)
        {
            try
            {
                using var cmd = new SQLiteCommand(
                    @"INSERT INTO SystemLogs (UserId, Action, Details, IpAddress)
                      VALUES (@userId, @action, @details, @ipAddress)", _connection);

                cmd.Parameters.AddWithValue("@userId", userId.HasValue ? (object)userId.Value : DBNull.Value);
                cmd.Parameters.AddWithValue("@action", action);
                cmd.Parameters.AddWithValue("@details", details);
                cmd.Parameters.AddWithValue("@ipAddress", ipAddress ?? "");

                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to log action: {ex.Message}");
            }
        }

        private void UpdateLastLogin(int userId)
        {
            using var cmd = new SQLiteCommand(
                "UPDATE Users SET LastLogin = CURRENT_TIMESTAMP WHERE Id = @userId", _connection);
            cmd.Parameters.AddWithValue("@userId", userId);
            cmd.ExecuteNonQuery();
        }

        private User MapUserFromReader(SQLiteDataReader reader)
        {
            return new User
            {
                Id = Convert.ToInt32(reader["Id"]),
                Username = reader["Username"].ToString(),
                ArabicUsername = reader["ArabicUsername"].ToString(),
                DeviceId = reader["DeviceId"].ToString(),
                Role = (UserRole)Convert.ToInt32(reader["Role"]),
                Status = (UserStatus)Convert.ToInt32(reader["Status"]),
                LastLogin = reader["LastLogin"] != DBNull.Value ? Convert.ToDateTime(reader["LastLogin"]) : null,
                IsActive = Convert.ToBoolean(reader["IsActive"])
            };
        }

        private Message MapMessageFromReader(SQLiteDataReader reader)
        {
            return new Message
            {
                Id = Convert.ToInt32(reader["Id"]),
                FromUserId = Convert.ToInt32(reader["FromUserId"]),
                ToUserId = Convert.ToInt32(reader["ToUserId"]),
                Content = reader["Content"].ToString(),
                Type = (MessageType)Convert.ToInt32(reader["Type"]),
                FilePath = reader["FilePath"].ToString(),
                Timestamp = Convert.ToDateTime(reader["Timestamp"]),
                IsRead = Convert.ToBoolean(reader["IsRead"]),
                SenderName = reader["SenderName"].ToString(),
                IsEncrypted = Convert.ToBoolean(reader["IsEncrypted"]),
                Status = reader["Status"] != DBNull.Value ? (MessageStatus)Convert.ToInt32(reader["Status"]) : MessageStatus.Sent,
                DeliveredAt = reader["DeliveredAt"] != DBNull.Value ? Convert.ToDateTime(reader["DeliveredAt"]) : null,
                ReadAt = reader["ReadAt"] != DBNull.Value ? Convert.ToDateTime(reader["ReadAt"]) : null,
                MessageId = reader["MessageId"]?.ToString() ?? Guid.NewGuid().ToString()
            };
        }

        public void Dispose()
        {
            _connection?.Close();
            _connection?.Dispose();
        }
    }
}
