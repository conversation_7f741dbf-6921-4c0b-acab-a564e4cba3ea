<Window x:Class="SafeLink.Views.ExitConfirmationWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="Exit Confirmation"
        Width="400" Height="250"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">

    <Window.Resources>
        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#1DE9B6"/>
            <Setter Property="Foreground" Value="#0D1421"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="8"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#00E5FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#00BCD4"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CancelButton" TargetType="Button">
            <Setter Property="Background" Value="#2A3441"/>
            <Setter Property="Foreground" Value="#FFFFFF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#3A4551"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="8"
                               Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3A4551"/>
                                <Setter Property="BorderBrush" Value="#4A5568"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1A2027"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="#0D1421"
           BorderBrush="#1DE9B6"
           BorderThickness="2"
           CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect Color="#000000" BlurRadius="20" ShadowDepth="5" Opacity="0.5"/>
        </Border.Effect>

        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,20">
                <controls:FeatherIcon IconName="alert-circle"
                                    IconColor="#FF6B6B"
                                    Width="32" Height="32"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,15"/>
                
                <TextBlock x:Name="TitleText"
                          Text="تأكيد الإغلاق"
                          FontSize="18"
                          FontWeight="SemiBold"
                          Foreground="#FFFFFF"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,5"/>
            </StackPanel>

            <!-- Message -->
            <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock x:Name="MessageText"
                          Text="هل تريد إغلاق البرنامج؟"
                          FontSize="14"
                          Foreground="#B0BEC5"
                          HorizontalAlignment="Center"
                          TextAlignment="Center"
                          TextWrapping="Wrap"
                          LineHeight="22"/>
            </StackPanel>

            <!-- Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button x:Name="YesButton"
                       Style="{StaticResource ModernButton}"
                       Click="YesButton_Click"
                       Margin="0,0,15,0"
                       MinWidth="80">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="check"
                                            IconColor="#0D1421"
                                            Width="16" Height="16"
                                            Margin="0,0,8,0"/>
                        <TextBlock x:Name="YesButtonText" Text="نعم" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button x:Name="NoButton"
                       Style="{StaticResource CancelButton}"
                       Click="NoButton_Click"
                       MinWidth="80">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="x"
                                            IconColor="#FFFFFF"
                                            Width="16" Height="16"
                                            Margin="0,0,8,0"/>
                        <TextBlock x:Name="NoButtonText" Text="لا" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
