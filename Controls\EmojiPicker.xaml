<UserControl x:Class="SafeLink.Controls.EmojiPicker"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="350">
    
    <UserControl.Resources>
        <Style x:Key="EmojiButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="22"/>
            <Setter Property="Margin" Value="3"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="EmojiBorder"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                RenderTransformOrigin="0.5,0.5">
                            <Border.RenderTransform>
                                <ScaleTransform x:Name="EmojiScale" ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1A00D4AA"/>
                                <Setter Property="BorderBrush" Value="#00D4AA"/>
                                <Setter Property="BorderThickness" Value="1"/>
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="EmojiScale"
                                                           Storyboard.TargetProperty="ScaleX"
                                                           To="1.2" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="EmojiScale"
                                                           Storyboard.TargetProperty="ScaleY"
                                                           To="1.2" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="EmojiScale"
                                                           Storyboard.TargetProperty="ScaleX"
                                                           To="1.0" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="EmojiScale"
                                                           Storyboard.TargetProperty="ScaleY"
                                                           To="1.0" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3300D4AA"/>
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="EmojiScale"
                                                           Storyboard.TargetProperty="ScaleX"
                                                           To="0.9" Duration="0:0:0.05"/>
                                            <DoubleAnimation Storyboard.TargetName="EmojiScale"
                                                           Storyboard.TargetProperty="ScaleY"
                                                           To="0.9" Duration="0:0:0.05"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    
    <Border Background="#1A1A2E"
            BorderBrush="#00D4AA"
            BorderThickness="2"
            CornerRadius="12"
            Effect="{StaticResource WindowShadow}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0"
                    Background="#2C3E50"
                    CornerRadius="10,10,0,0"
                    Padding="15,12">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="😊"
                               FontSize="18"
                               Margin="0,0,8,0"/>
                    <TextBlock Text="اختر إيموجي"
                               FontSize="16"
                               FontWeight="SemiBold"
                               Foreground="#FFFFFF"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Border>
            
            <!-- Emoji Grid -->
            <ScrollViewer Grid.Row="1"
                          VerticalScrollBarVisibility="Auto"
                          HorizontalScrollBarVisibility="Disabled"
                          Padding="10"
                          MaxHeight="300"
                          CanContentScroll="True">
                <StackPanel>
                    <!-- Smileys & Emotion -->
                    <TextBlock Text="😊 الوجوه والمشاعر"
                               FontSize="13"
                               FontWeight="SemiBold"
                               Foreground="#BDC3C7"
                               Margin="0,8,0,8"/>
                    <WrapPanel x:Name="SmileysPanel"
                               Orientation="Horizontal"
                               MaxWidth="280"/>

                    <!-- People & Body -->
                    <TextBlock Text="👥 الأشخاص والجسم"
                               FontSize="13"
                               FontWeight="SemiBold"
                               Foreground="#BDC3C7"
                               Margin="0,20,0,8"/>
                    <WrapPanel x:Name="PeoplePanel"
                               Orientation="Horizontal"
                               MaxWidth="280"/>

                    <!-- Objects -->
                    <TextBlock Text="🎮 الأشياء"
                               FontSize="13"
                               FontWeight="SemiBold"
                               Foreground="#BDC3C7"
                               Margin="0,20,0,8"/>
                    <WrapPanel x:Name="ObjectsPanel"
                               Orientation="Horizontal"
                               MaxWidth="280"/>

                    <!-- Symbols -->
                    <TextBlock Text="⭐ الرموز"
                               FontSize="13"
                               FontWeight="SemiBold"
                               Foreground="#BDC3C7"
                               Margin="0,20,0,8"/>
                    <WrapPanel x:Name="SymbolsPanel"
                               Orientation="Horizontal"
                               MaxWidth="280"/>

                    <!-- Tech & Security -->
                    <TextBlock Text="🔒 التقنية والأمان"
                               FontSize="13"
                               FontWeight="SemiBold"
                               Foreground="#00D4AA"
                               Margin="0,20,0,8"/>
                    <WrapPanel x:Name="TechPanel"
                               Orientation="Horizontal"
                               MaxWidth="280"/>

                    <!-- Food & Drink -->
                    <TextBlock Text="🍕 الطعام والشراب"
                               FontSize="13"
                               FontWeight="SemiBold"
                               Foreground="#BDC3C7"
                               Margin="0,20,0,8"/>
                    <WrapPanel x:Name="FoodPanel" Orientation="Horizontal"/>

                    <!-- Animals & Nature -->
                    <TextBlock Text="🐾 الحيوانات والطبيعة"
                               FontSize="13"
                               FontWeight="SemiBold"
                               Foreground="#BDC3C7"
                               Margin="0,20,0,8"/>
                    <WrapPanel x:Name="AnimalsPanel" Orientation="Horizontal"/>
                </StackPanel>
            </ScrollViewer>
        </Grid>
    </Border>
</UserControl>
