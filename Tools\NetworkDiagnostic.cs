using System;
using System.Diagnostics;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace SafeLink.Tools
{
    public static class NetworkDiagnostic
    {
        public static async Task<string> RunFullDiagnosticAsync()
        {
            var report = new StringBuilder();
            report.AppendLine("🛡️ SafeLink Network Diagnostic Report");
            report.AppendLine("=====================================");
            report.AppendLine($"📅 Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // 1. Check local IP configuration
            report.AppendLine("1️⃣ LOCAL IP CONFIGURATION:");
            report.AppendLine("---------------------------");
            try
            {
                var localIP = GetLocalIPAddress();
                if (localIP != null)
                {
                    report.AppendLine($"✅ Local IP: {localIP}");
                    report.AppendLine($"✅ Subnet: {GetSubnetMask(localIP)}");
                    report.AppendLine($"✅ Broadcast: {GetBroadcastAddress(localIP)}");
                }
                else
                {
                    report.AppendLine("❌ No local IP found");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ IP Configuration Error: {ex.Message}");
            }
            report.AppendLine();

            // 2. Check port availability
            report.AppendLine("2️⃣ PORT AVAILABILITY:");
            report.AppendLine("---------------------");
            var ports = new[] { 8888, 8889, 8890, 8891 };
            foreach (var port in ports)
            {
                var isAvailable = await IsPortAvailableAsync(port);
                var status = isAvailable ? "✅ Available" : "❌ In Use";
                report.AppendLine($"Port {port}: {status}");
            }
            report.AppendLine();

            // 3. Check Windows Firewall
            report.AppendLine("3️⃣ WINDOWS FIREWALL:");
            report.AppendLine("--------------------");
            try
            {
                var firewallStatus = CheckWindowsFirewall();
                report.AppendLine(firewallStatus);
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Firewall Check Error: {ex.Message}");
            }
            report.AppendLine();

            // 4. Test UDP broadcast
            report.AppendLine("4️⃣ UDP BROADCAST TEST:");
            report.AppendLine("----------------------");
            try
            {
                var broadcastResult = await TestUDPBroadcastAsync();
                report.AppendLine(broadcastResult);
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Broadcast Test Error: {ex.Message}");
            }
            report.AppendLine();

            // 5. Network interfaces
            report.AppendLine("5️⃣ NETWORK INTERFACES:");
            report.AppendLine("----------------------");
            try
            {
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (var ni in interfaces)
                {
                    if (ni.OperationalStatus == OperationalStatus.Up &&
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    {
                        report.AppendLine($"✅ {ni.Name}: {ni.NetworkInterfaceType}");
                    }
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"❌ Interface Check Error: {ex.Message}");
            }
            report.AppendLine();

            // 6. Recommendations
            report.AppendLine("6️⃣ RECOMMENDATIONS:");
            report.AppendLine("-------------------");
            report.AppendLine("🔧 If devices can't see each other:");
            report.AppendLine("   • Ensure both devices are on the same network");
            report.AppendLine("   • Add SafeLink.exe to Windows Firewall exceptions");
            report.AppendLine("   • Check router settings for UDP broadcast");
            report.AppendLine("   • Temporarily disable antivirus network protection");
            report.AppendLine();
            report.AppendLine("🔧 If ports are in use:");
            report.AppendLine("   • Close other SafeLink instances");
            report.AppendLine("   • Restart the application");
            report.AppendLine("   • Check for other applications using these ports");

            return report.ToString();
        }

        private static IPAddress GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        return ip;
                    }
                }
            }
            catch { }
            return null;
        }

        private static string GetSubnetMask(IPAddress localIP)
        {
            try
            {
                var interfaces = NetworkInterface.GetAllNetworkInterfaces();
                foreach (var ni in interfaces)
                {
                    var properties = ni.GetIPProperties();
                    foreach (var addr in properties.UnicastAddresses)
                    {
                        if (addr.Address.Equals(localIP))
                        {
                            return addr.IPv4Mask?.ToString() ?? "*************";
                        }
                    }
                }
            }
            catch { }
            return "*************";
        }

        private static string GetBroadcastAddress(IPAddress localIP)
        {
            try
            {
                var bytes = localIP.GetAddressBytes();
                bytes[3] = 255; // Assume /24 subnet
                return new IPAddress(bytes).ToString();
            }
            catch { }
            return "***************";
        }

        private static async Task<bool> IsPortAvailableAsync(int port)
        {
            try
            {
                await Task.Run(() =>
                {
                    using var udpClient = new UdpClient();
                    udpClient.Client.Bind(new IPEndPoint(IPAddress.Any, port));
                });
                return true;
            }
            catch
            {
                return false;
            }
        }

        private static string CheckWindowsFirewall()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "netsh",
                        Arguments = "advfirewall show allprofiles state",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                if (output.Contains("ON"))
                {
                    return "⚠️ Windows Firewall is ON - May block SafeLink";
                }
                else
                {
                    return "✅ Windows Firewall is OFF";
                }
            }
            catch
            {
                return "❓ Could not check Windows Firewall status";
            }
        }

        private static async Task<string> TestUDPBroadcastAsync()
        {
            try
            {
                using var udpClient = new UdpClient();
                udpClient.EnableBroadcast = true;

                var testMessage = "SAFELINK_TEST";
                var data = Encoding.UTF8.GetBytes(testMessage);

                await udpClient.SendAsync(data, data.Length, "***************", 8888);

                return "✅ UDP Broadcast test successful";
            }
            catch (Exception ex)
            {
                return $"❌ UDP Broadcast failed: {ex.Message}";
            }
        }

        public static async Task ShowDiagnosticWindowAsync()
        {
            try
            {
                var report = await RunFullDiagnosticAsync();

                var window = new Window
                {
                    Title = "SafeLink Network Diagnostic",
                    Width = 950,
                    Height = 700,
                    WindowStartupLocation = WindowStartupLocation.CenterScreen,
                    WindowStyle = WindowStyle.None,
                    AllowsTransparency = true,
                    Background = System.Windows.Media.Brushes.Transparent
                };

                // Create professional container
                var mainBorder = new System.Windows.Controls.Border
                {
                    CornerRadius = new CornerRadius(15),
                    BorderThickness = new Thickness(2, 2, 2, 2),
                    BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x00, 0xE5, 0xFF)),
                    Margin = new Thickness(10, 10, 10, 10)
                };

                // Professional gradient background
                var backgroundBrush = new System.Windows.Media.LinearGradientBrush();
                backgroundBrush.StartPoint = new System.Windows.Point(0, 0);
                backgroundBrush.EndPoint = new System.Windows.Point(1, 1);
                backgroundBrush.GradientStops.Add(new System.Windows.Media.GradientStop(System.Windows.Media.Color.FromRgb(0x0D, 0x14, 0x21), 0));
                backgroundBrush.GradientStops.Add(new System.Windows.Media.GradientStop(System.Windows.Media.Color.FromRgb(0x1A, 0x23, 0x32), 0.5));
                backgroundBrush.GradientStops.Add(new System.Windows.Media.GradientStop(System.Windows.Media.Color.FromRgb(0x24, 0x34, 0x47), 1));
                mainBorder.Background = backgroundBrush;

                // Add glow effect
                var dropShadow = new System.Windows.Media.Effects.DropShadowEffect
                {
                    Color = System.Windows.Media.Color.FromRgb(0x00, 0xE5, 0xFF),
                    BlurRadius = 20,
                    ShadowDepth = 0,
                    Opacity = 0.5
                };
                mainBorder.Effect = dropShadow;

                // Main grid
                var mainGrid = new System.Windows.Controls.Grid();
                mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(100) }); // Increased header height
                mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(1, System.Windows.GridUnitType.Star) });
                mainGrid.RowDefinitions.Add(new System.Windows.Controls.RowDefinition { Height = new System.Windows.GridLength(70) }); // Increased footer height

                // Professional header with more space
                var headerBorder = new System.Windows.Controls.Border
                {
                    CornerRadius = new CornerRadius(15, 15, 0, 0),
                    Padding = new Thickness(30, 25, 30, 25) // Increased padding
                };
                var headerBrush = new System.Windows.Media.LinearGradientBrush();
                headerBrush.StartPoint = new System.Windows.Point(0, 0);
                headerBrush.EndPoint = new System.Windows.Point(1, 0);
                headerBrush.GradientStops.Add(new System.Windows.Media.GradientStop(System.Windows.Media.Color.FromRgb(0x24, 0x34, 0x47), 0));
                headerBrush.GradientStops.Add(new System.Windows.Media.GradientStop(System.Windows.Media.Color.FromRgb(0x2C, 0x4A, 0x5C), 1));
                headerBorder.Background = headerBrush;
                System.Windows.Controls.Grid.SetRow(headerBorder, 0);

                var headerStack = new System.Windows.Controls.StackPanel();
                var titleText = new System.Windows.Controls.TextBlock
                {
                    Text = "NETWORK DIAGNOSTIC REPORT",
                    FontSize = 20, // Increased font size
                    FontWeight = System.Windows.FontWeights.Bold,
                    Foreground = System.Windows.Media.Brushes.White,
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 8) // Increased margin
                };
                var subtitleText = new System.Windows.Controls.TextBlock
                {
                    Text = "SafeLink Professional Security Platform",
                    FontSize = 14, // Increased font size
                    Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xB0, 0xBE, 0xC5)),
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center
                };
                headerStack.Children.Add(titleText);
                headerStack.Children.Add(subtitleText);
                headerBorder.Child = headerStack;

                // Content area with professional styling
                var contentBorder = new System.Windows.Controls.Border
                {
                    Padding = new Thickness(30, 30, 30, 30), // Increased padding
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromArgb(0x10, 0xFF, 0xFF, 0xFF))
                };
                System.Windows.Controls.Grid.SetRow(contentBorder, 1);

                var scrollViewer = new System.Windows.Controls.ScrollViewer
                {
                    VerticalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Auto,
                    HorizontalScrollBarVisibility = System.Windows.Controls.ScrollBarVisibility.Disabled
                };

                var textBlock = new System.Windows.Controls.TextBlock
                {
                    Text = report,
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 13,
                    TextWrapping = TextWrapping.Wrap,
                    Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x1D, 0xE9, 0xB6)),
                    LineHeight = 20
                };

                scrollViewer.Content = textBlock;
                contentBorder.Child = scrollViewer;

                // Professional footer with close button
                var footerBorder = new System.Windows.Controls.Border
                {
                    CornerRadius = new CornerRadius(0, 0, 15, 15),
                    Padding = new Thickness(30, 20, 30, 20), // Increased padding
                    Background = headerBrush
                };
                System.Windows.Controls.Grid.SetRow(footerBorder, 2);

                var closeButton = new System.Windows.Controls.Button
                {
                    Content = "CLOSE",
                    Width = 120, // Increased width
                    Height = 40, // Increased height
                    HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x00, 0xE5, 0xFF)),
                    Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x0D, 0x14, 0x21)),
                    BorderThickness = new Thickness(0, 0, 0, 0),
                    FontWeight = System.Windows.FontWeights.Bold,
                    FontSize = 13 // Increased font size
                };
                closeButton.Click += (s, e) => window.Close();
                footerBorder.Child = closeButton;

                mainGrid.Children.Add(headerBorder);
                mainGrid.Children.Add(contentBorder);
                mainGrid.Children.Add(footerBorder);
                mainBorder.Child = mainGrid;
                window.Content = mainBorder;

                // Make window draggable
                window.MouseLeftButtonDown += (s, e) => window.DragMove();

                window.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Diagnostic Error: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
