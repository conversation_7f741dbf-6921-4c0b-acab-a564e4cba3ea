using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SafeLink.Controls
{
    public partial class EmojiPicker : UserControl
    {
        public event Action<string> EmojiSelected;

        private Dictionary<string, List<EmojiInfo>> _emojiCategories;
        private string _emojiBasePath;

        public class EmojiInfo
        {
            public string File { get; set; }
            public string Name { get; set; }
            public string DescriptionAr { get; set; }
            public string DescriptionEn { get; set; }
        }

        public class EmojiCategory
        {
            public string NameAr { get; set; }
            public string NameEn { get; set; }
            public List<EmojiInfo> Emojis { get; set; }
        }

        public EmojiPicker()
        {
            InitializeComponent();
            _emojiBasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Emojis");
            LoadEmojisFromJson();
        }

        private void LoadEmojisFromJson()
        {
            try
            {
                var jsonPath = Path.Combine(_emojiBasePath, "emoji_index.json");
                if (!File.Exists(jsonPath))
                {
                    LoadFallbackEmojis();
                    return;
                }

                var jsonContent = File.ReadAllText(jsonPath);
                var emojiData = JsonConvert.DeserializeObject<JObject>(jsonContent);

                _emojiCategories = new Dictionary<string, List<EmojiInfo>>();

                var categories = emojiData["categories"] as JObject;
                foreach (var category in categories)
                {
                    var categoryKey = category.Key;
                    var categoryData = category.Value as JObject;

                    var emojis = new List<EmojiInfo>();
                    foreach (var emoji in categoryData["emojis"])
                    {
                        emojis.Add(new EmojiInfo
                        {
                            File = emoji["file"]?.ToString(),
                            Name = emoji["name"]?.ToString(),
                            DescriptionAr = emoji["description_ar"]?.ToString(),
                            DescriptionEn = emoji["description_en"]?.ToString()
                        });
                    }

                    _emojiCategories[categoryKey] = emojis;
                }

                LoadEmojis();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading emojis from JSON: {ex.Message}");
                LoadFallbackEmojis();
            }
        }

        private void LoadFallbackEmojis()
        {
            // إيموجي احتياطية في حالة عدم وجود ملف JSON
            _emojiCategories = new Dictionary<string, List<EmojiInfo>>
            {
                ["faces"] = new List<EmojiInfo>
                {
                    new EmojiInfo { Name = "😀", DescriptionAr = "وجه مبتسم", DescriptionEn = "Grinning Face" },
                    new EmojiInfo { Name = "😊", DescriptionAr = "وجه مبتسم بعينين مبتسمتين", DescriptionEn = "Smiling Face" },
                    new EmojiInfo { Name = "😂", DescriptionAr = "وجه يضحك بدموع", DescriptionEn = "Face with Tears of Joy" },
                    new EmojiInfo { Name = "😍", DescriptionAr = "وجه مبتسم بعيون قلوب", DescriptionEn = "Heart Eyes" },
                    new EmojiInfo { Name = "😢", DescriptionAr = "وجه يبكي", DescriptionEn = "Crying Face" }
                },
                ["gestures"] = new List<EmojiInfo>
                {
                    new EmojiInfo { Name = "👍", DescriptionAr = "إبهام لأعلى", DescriptionEn = "Thumbs Up" },
                    new EmojiInfo { Name = "👎", DescriptionAr = "إبهام لأسفل", DescriptionEn = "Thumbs Down" },
                    new EmojiInfo { Name = "👏", DescriptionAr = "تصفيق", DescriptionEn = "Clapping Hands" },
                    new EmojiInfo { Name = "🙏", DescriptionAr = "يدان مطويتان", DescriptionEn = "Folded Hands" }
                },
                ["security"] = new List<EmojiInfo>
                {
                    new EmojiInfo { Name = "🔒", DescriptionAr = "قفل مغلق", DescriptionEn = "Locked" },
                    new EmojiInfo { Name = "🔓", DescriptionAr = "قفل مفتوح", DescriptionEn = "Unlocked" },
                    new EmojiInfo { Name = "⚠️", DescriptionAr = "تحذير", DescriptionEn = "Warning" },
                    new EmojiInfo { Name = "✅", DescriptionAr = "علامة صح", DescriptionEn = "Check Mark" }
                }
            };

            LoadEmojis();
        }

        private void LoadEmojis()
        {
            if (_emojiCategories == null) return;

            // تحديد ترتيب التصنيفات
            var categoryOrder = new[] { "faces", "gestures", "security", "work", "status" };
            var categoryNames = new Dictionary<string, string>
            {
                ["faces"] = "الوجوه",
                ["gestures"] = "الإيماءات",
                ["security"] = "الأمان",
                ["work"] = "العمل",
                ["status"] = "الحالة"
            };

            foreach (var categoryKey in categoryOrder)
            {
                if (!_emojiCategories.ContainsKey(categoryKey)) continue;

                var categoryName = categoryNames.ContainsKey(categoryKey) ? categoryNames[categoryKey] : categoryKey;
                LoadEmojiCategory(categoryKey, categoryName, _emojiCategories[categoryKey]);
            }
        }

        private void LoadEmojiCategory(string categoryKey, string categoryName, List<EmojiInfo> emojis)
        {
            // إنشاء عنوان التصنيف
            var categoryHeader = new TextBlock
            {
                Text = categoryName,
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Colors.White),
                Margin = new Thickness(5, 10, 5, 5),
                HorizontalAlignment = HorizontalAlignment.Right
            };

            // إنشاء WrapPanel للإيموجي
            var emojiPanel = new WrapPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(5, 0, 5, 10),
                MaxWidth = 280
            };

            foreach (var emoji in emojis)
            {
                var button = new Button
                {
                    Width = 28,
                    Height = 28,
                    Margin = new Thickness(1),
                    Background = new SolidColorBrush(Color.FromArgb(0, 0, 0, 0)),
                    BorderBrush = new SolidColorBrush(Color.FromArgb(30, 255, 255, 255)),
                    BorderThickness = new Thickness(1),
                    ToolTip = emoji.DescriptionAr ?? emoji.Name,
                    Cursor = System.Windows.Input.Cursors.Hand,
                    Style = null // Remove default button style
                };

                // إنشاء محتوى SVG للإيموجي
                if (!string.IsNullOrEmpty(emoji.File))
                {
                    try
                    {
                        var svgIcon = new Controls.SvgIcon
                        {
                            SvgFile = emoji.File,
                            Width = 18,
                            Height = 18
                        };
                        button.Content = svgIcon;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ Failed to load emoji SVG {emoji.File}: {ex.Message}");
                        // استخدام النص كبديل
                        button.Content = emoji.Name ?? "😊";
                        button.FontSize = 14;
                    }
                }
                else
                {
                    // استخدام النص كبديل
                    button.Content = emoji.Name ?? "😊";
                    button.FontSize = 14;
                }

                // تأثيرات التفاعل
                button.MouseEnter += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.FromArgb(30, 255, 255, 255));
                };

                button.MouseLeave += (s, e) =>
                {
                    button.Background = new SolidColorBrush(Color.FromArgb(0, 0, 0, 0));
                };

                button.Click += (s, e) =>
                {
                    EmojiSelected?.Invoke(emoji.Name);
                };

                emojiPanel.Children.Add(button);
            }

            // إضافة العنوان والإيموجي إلى الحاوية الرئيسية
            var targetPanel = FindCategoryPanel(categoryKey);
            if (targetPanel != null)
            {
                targetPanel.Children.Clear();
                targetPanel.Children.Add(categoryHeader);
                targetPanel.Children.Add(emojiPanel);
            }
        }

        private Panel FindCategoryPanel(string categoryKey)
        {
            switch (categoryKey)
            {
                case "faces": return SmileysPanel;
                case "gestures": return PeoplePanel;
                case "security": return TechPanel;
                case "work": return ObjectsPanel;
                case "status": return SymbolsPanel;
                default: return SmileysPanel;
            }
        }
    }
}
