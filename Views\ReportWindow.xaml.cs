using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using SafeLink.Core;

namespace SafeLink.Views
{
    public partial class ReportWindow : Window
    {
        public bool IncludeBasicReport { get; private set; } = true;
        public bool IncludeCustomDescription { get; private set; } = false;
        public string CustomDescription { get; private set; } = string.Empty;

        public ReportWindow()
        {
            InitializeComponent();
            UpdateLanguage();

            // Set initial state
            BasicReportCheckBox.IsChecked = true;
        }

        private void UpdateLanguage()
        {
            try
            {
                // Update window title
                Title = LanguageManager.GetString("SendReport") ?? "SafeLink - إرسال تقرير";

                // Update header texts
                if (LanguageManager.CurrentLanguage == "ar")
                {
                    HeaderText.Text = "إرسال تقرير";
                    SubHeaderText.Text = "اختر المعلومات المطلوب إرسالها مع التقرير";

                    // Basic report option
                    BasicReportTitle.Text = "تقرير أساسي شامل";
                    BasicReportDesc.Text = "لقطة شاشة + معلومات النظام + تقرير الحالة";

                    // Custom description option
                    CustomDescTitle.Text = "إضافة وصف للمشكلة";
                    CustomDescDesc.Text = "اكتب وصف تفصيلي للمشكلة أو الملاحظات";
                    CustomDescLabel.Text = "اكتب وصف المشكلة بالتفصيل:";

                    // Buttons
                    SendButton.Content = "إرسال التقرير";
                    CancelButton.Content = "إلغاء";

                    FlowDirection = FlowDirection.RightToLeft;
                }
                else
                {
                    HeaderText.Text = "Send Report";
                    SubHeaderText.Text = "Choose the information to include with the report";

                    // Basic report option
                    BasicReportTitle.Text = "Comprehensive Basic Report";
                    BasicReportDesc.Text = "Screenshot + System Info + Status Report";

                    // Custom description option
                    CustomDescTitle.Text = "Add Problem Description";
                    CustomDescDesc.Text = "Write detailed description of the issue or notes";
                    CustomDescLabel.Text = "Write detailed problem description:";

                    // Buttons
                    SendButton.Content = "Send Report";
                    CancelButton.Content = "Cancel";

                    FlowDirection = FlowDirection.LeftToRight;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating language in ReportWindow: {ex.Message}");
            }
        }

        private void BasicReportCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            IncludeBasicReport = true;

            // Add visual feedback
            BasicReportIcon.IconColor = new SolidColorBrush(Color.FromRgb(0x00, 0xD4, 0xAA));
        }

        private void BasicReportCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            IncludeBasicReport = false;

            // Reset visual feedback
            BasicReportIcon.IconColor = new SolidColorBrush(Colors.White);
        }

        private void CustomDescCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            IncludeCustomDescription = true;
            CustomDescPanel.Visibility = Visibility.Visible;
            CustomDescTextBox.Focus();

            // Add visual feedback
            CustomDescIcon.IconColor = new SolidColorBrush(Color.FromRgb(0x00, 0xD4, 0xAA));
        }

        private void CustomDescCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            IncludeCustomDescription = false;
            CustomDescPanel.Visibility = Visibility.Collapsed;
            CustomDescTextBox.Clear();

            // Reset visual feedback
            CustomDescIcon.IconColor = new SolidColorBrush(Colors.White);
        }

        private void SendButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate that at least basic report is selected
            if (!IncludeBasicReport && !IncludeCustomDescription)
            {
                var message = LanguageManager.CurrentLanguage == "ar"
                    ? "يجب اختيار نوع واحد على الأقل من التقرير"
                    : "Please select at least one report type";

                System.Windows.MessageBox.Show(message, "SafeLink", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Get custom description if provided
            if (IncludeCustomDescription && !string.IsNullOrWhiteSpace(CustomDescTextBox.Text))
            {
                CustomDescription = CustomDescTextBox.Text.Trim();
            }

            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.Key == Key.Escape)
            {
                CancelButton_Click(this, new RoutedEventArgs());
            }
            else if (e.Key == Key.Enter && CustomDescCheckBox.IsChecked == true)
            {
                // If custom description is selected and Enter is pressed, send the report
                SendButton_Click(this, new RoutedEventArgs());
            }

            base.OnKeyDown(e);
        }

        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            // Get custom description if provided
            if (IncludeCustomDescription && !string.IsNullOrWhiteSpace(CustomDescTextBox.Text))
            {
                CustomDescription = CustomDescTextBox.Text.Trim();
            }

            base.OnClosing(e);
        }
    }
}
