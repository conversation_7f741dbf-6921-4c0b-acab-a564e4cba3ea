using System.Windows;
using System.Windows.Controls;
using SafeLink.Models;

namespace SafeLink.Controls
{
    public partial class MessageStatusIndicator : UserControl
    {
        public static readonly DependencyProperty StatusProperty =
            DependencyProperty.Register("Status", typeof(MessageStatus), typeof(MessageStatusIndicator),
                new PropertyMetadata(MessageStatus.Sending, OnStatusChanged));

        public MessageStatus Status
        {
            get { return (MessageStatus)GetValue(StatusProperty); }
            set { SetValue(StatusProperty, value); }
        }

        public MessageStatusIndicator()
        {
            InitializeComponent();
            UpdateStatusDisplay();
        }

        private static void OnStatusChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is MessageStatusIndicator indicator)
            {
                indicator.UpdateStatusDisplay();
            }
        }

        private void UpdateStatusDisplay()
        {
            // Hide all icons first
            SendingIcon.Visibility = Visibility.Collapsed;
            SentIcon.Visibility = Visibility.Collapsed;
            DeliveredIcon.Visibility = Visibility.Collapsed;
            ReadIcon.Visibility = Visibility.Collapsed;
            FailedIcon.Visibility = Visibility.Collapsed;

            // Show appropriate icon based on status
            switch (Status)
            {
                case MessageStatus.Sending:
                    SendingIcon.Visibility = Visibility.Visible;
                    break;
                case MessageStatus.Sent:
                    SentIcon.Visibility = Visibility.Visible;
                    break;
                case MessageStatus.Delivered:
                    DeliveredIcon.Visibility = Visibility.Visible;
                    break;
                case MessageStatus.Read:
                    ReadIcon.Visibility = Visibility.Visible;
                    break;
                case MessageStatus.Failed:
                    FailedIcon.Visibility = Visibility.Visible;
                    break;
            }
        }
    }
}
