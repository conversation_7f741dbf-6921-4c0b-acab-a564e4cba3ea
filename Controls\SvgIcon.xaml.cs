using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Xml;

namespace SafeLink.Controls
{
    public partial class SvgIcon : UserControl
    {
        public static readonly DependencyProperty SvgFileProperty =
            DependencyProperty.Register("SvgFile", typeof(string), typeof(SvgIcon),
                new PropertyMetadata(string.Empty, OnSvgFileChanged));

        public static readonly DependencyProperty IconColorProperty =
            DependencyProperty.Register("IconColor", typeof(Brush), typeof(SvgIcon),
                new PropertyMetadata(Brushes.White, OnIconColorChanged));

        public string SvgFile
        {
            get { return (string)GetValue(SvgFileProperty); }
            set { SetValue(SvgFileProperty, value); }
        }

        public Brush IconColor
        {
            get { return (Brush)GetValue(IconColorProperty); }
            set { SetValue(IconColorProperty, value); }
        }

        public SvgIcon()
        {
            InitializeComponent();
        }

        private static void OnSvgFileChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SvgIcon icon)
            {
                icon.LoadSvg();
            }
        }

        private static void OnIconColorChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is SvgIcon icon)
            {
                icon.UpdateColor();
            }
        }

        private void LoadSvg()
        {
            try
            {
                if (SvgCanvas == null || string.IsNullOrEmpty(SvgFile)) return;

                SvgCanvas.Children.Clear();

                // Try to find SVG file in multiple locations
                var uploadsPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "uploads", SvgFile);
                var iconsPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Icons", SvgFile);
                var resourcesPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", SvgFile);
                var emojisPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Emojis", SvgFile);

                string svgPath = null;
                if (File.Exists(uploadsPath))
                {
                    svgPath = uploadsPath;
                }
                else if (File.Exists(iconsPath))
                {
                    svgPath = iconsPath;
                }
                else if (File.Exists(resourcesPath))
                {
                    svgPath = resourcesPath;
                }
                else if (File.Exists(emojisPath))
                {
                    svgPath = emojisPath;
                }

                if (svgPath != null)
                {
                    var svgContent = File.ReadAllText(svgPath);
                    ParseAndRenderSvg(svgContent);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ SVG file not found: {SvgFile}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error loading SVG: {ex.Message}");
            }
        }

        private void ParseAndRenderSvg(string svgContent)
        {
            try
            {
                var doc = new XmlDocument();
                doc.LoadXml(svgContent);

                var svgElement = doc.DocumentElement;
                if (svgElement?.Name == "svg")
                {
                    foreach (XmlNode child in svgElement.ChildNodes)
                    {
                        var element = CreateElementFromXml(child);
                        if (element != null)
                        {
                            SvgCanvas.Children.Add(element);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error parsing SVG: {ex.Message}");
            }
        }

        private UIElement CreateElementFromXml(XmlNode node)
        {
            try
            {
                switch (node.Name.ToLower())
                {
                    case "path":
                        return CreatePath(node);
                    case "line":
                        return CreateLine(node);
                    case "circle":
                        return CreateCircle(node);
                    case "ellipse":
                        return CreateEllipse(node);
                    case "rect":
                        return CreateRectangle(node);
                    default:
                        return null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error creating element from XML: {ex.Message}");
                return null;
            }
        }

        private System.Windows.Shapes.Path CreatePath(XmlNode node)
        {
            var path = new System.Windows.Shapes.Path();
            var dAttribute = node.Attributes?["d"]?.Value;
            
            if (!string.IsNullOrEmpty(dAttribute))
            {
                try
                {
                    path.Data = Geometry.Parse(dAttribute);
                    path.Stroke = IconColor ?? Brushes.White;
                    path.StrokeThickness = 2;
                    path.Fill = Brushes.Transparent;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Error parsing path data: {ex.Message}");
                }
            }
            
            return path;
        }

        private Line CreateLine(XmlNode node)
        {
            var line = new Line();
            
            if (double.TryParse(node.Attributes?["x1"]?.Value, out double x1))
                line.X1 = x1;
            if (double.TryParse(node.Attributes?["y1"]?.Value, out double y1))
                line.Y1 = y1;
            if (double.TryParse(node.Attributes?["x2"]?.Value, out double x2))
                line.X2 = x2;
            if (double.TryParse(node.Attributes?["y2"]?.Value, out double y2))
                line.Y2 = y2;
                
            line.Stroke = IconColor ?? Brushes.White;
            line.StrokeThickness = 2;
            line.StrokeStartLineCap = PenLineCap.Round;
            line.StrokeEndLineCap = PenLineCap.Round;
            
            return line;
        }

        private Ellipse CreateCircle(XmlNode node)
        {
            var ellipse = new Ellipse();
            
            if (double.TryParse(node.Attributes?["r"]?.Value, out double r))
            {
                ellipse.Width = r * 2;
                ellipse.Height = r * 2;
            }
            
            if (double.TryParse(node.Attributes?["cx"]?.Value, out double cx))
                Canvas.SetLeft(ellipse, cx - ellipse.Width / 2);
            if (double.TryParse(node.Attributes?["cy"]?.Value, out double cy))
                Canvas.SetTop(ellipse, cy - ellipse.Height / 2);
                
            ellipse.Stroke = IconColor ?? Brushes.White;
            ellipse.StrokeThickness = 2;
            ellipse.Fill = Brushes.Transparent;
            
            return ellipse;
        }

        private Ellipse CreateEllipse(XmlNode node)
        {
            var ellipse = new Ellipse();
            
            if (double.TryParse(node.Attributes?["rx"]?.Value, out double rx))
                ellipse.Width = rx * 2;
            if (double.TryParse(node.Attributes?["ry"]?.Value, out double ry))
                ellipse.Height = ry * 2;
            
            if (double.TryParse(node.Attributes?["cx"]?.Value, out double cx))
                Canvas.SetLeft(ellipse, cx - ellipse.Width / 2);
            if (double.TryParse(node.Attributes?["cy"]?.Value, out double cy))
                Canvas.SetTop(ellipse, cy - ellipse.Height / 2);
                
            ellipse.Stroke = IconColor ?? Brushes.White;
            ellipse.StrokeThickness = 2;
            ellipse.Fill = Brushes.Transparent;
            
            return ellipse;
        }

        private Rectangle CreateRectangle(XmlNode node)
        {
            var rect = new Rectangle();
            
            if (double.TryParse(node.Attributes?["width"]?.Value, out double width))
                rect.Width = width;
            if (double.TryParse(node.Attributes?["height"]?.Value, out double height))
                rect.Height = height;
            
            if (double.TryParse(node.Attributes?["x"]?.Value, out double x))
                Canvas.SetLeft(rect, x);
            if (double.TryParse(node.Attributes?["y"]?.Value, out double y))
                Canvas.SetTop(rect, y);
                
            rect.Stroke = IconColor ?? Brushes.White;
            rect.StrokeThickness = 2;
            rect.Fill = Brushes.Transparent;
            
            return rect;
        }

        private void UpdateColor()
        {
            foreach (UIElement child in SvgCanvas.Children)
            {
                if (child is Shape shape)
                {
                    shape.Stroke = IconColor ?? Brushes.White;
                }
            }
        }
    }
}
