<Window x:Class="SafeLink.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink - Authentication"
        Height="620" Width="420"
        Background="Transparent"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        ShowInTaskbar="True"
        AllowsTransparency="True"
        KeyDown="LoginWindow_KeyDown"
        Loaded="LoginWindow_Loaded">

    <Window.Resources>
        <!-- Professional Login Gradient -->
        <LinearGradientBrush x:Key="LoginGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#0D1421" Offset="0"/>
            <GradientStop Color="#1A2332" Offset="0.5"/>
            <GradientStop Color="#243447" Offset="1"/>
        </LinearGradientBrush>

        <!-- Accent Colors -->
        <SolidColorBrush x:Key="PrimaryAccent" Color="#00E5FF"/>
        <SolidColorBrush x:Key="SecondaryAccent" Color="#1DE9B6"/>
        <SolidColorBrush x:Key="InputBackground" Color="#1E2A3A"/>
        <SolidColorBrush x:Key="TextPrimary" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextSecondary" Color="#B0BEC5"/>

        <!-- Input Field Style -->
        <Style x:Key="ModernInput" TargetType="Control">
            <Setter Property="Background" Value="{StaticResource InputBackground}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
            <Setter Property="BorderBrush" Value="#30FFFFFF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Control">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost" Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryAccent}"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Button Style -->
        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryAccent}"/>
            <Setter Property="Foreground" Value="#0D1421"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="25,12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#00B8CC"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#008A9B"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondary}"/>
            <Setter Property="BorderBrush" Value="#30FFFFFF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="25,12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryAccent}"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryAccent}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="{StaticResource LoginGradient}"
           BorderBrush="{StaticResource PrimaryAccent}"
           BorderThickness="1"
           CornerRadius="15">
        <Border.Effect>
            <DropShadowEffect Color="#000000" BlurRadius="25" ShadowDepth="0" Opacity="0.7"/>
        </Border.Effect>

        <Grid Margin="35">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>



            <!-- Professional Header -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,10,0,25">
                <!-- Shield Logo -->
                <controls:ShieldIcon ShieldColor="{StaticResource SecondaryAccent}"
                                   Width="40" Height="48"
                                   HorizontalAlignment="Center"
                                   Margin="0,0,0,20"/>

                <!-- Brand Title -->
                <TextBlock x:Name="SecureAccessText"
                          Text="SECURE ACCESS"
                          FontSize="18"
                          FontWeight="Light"
                          Foreground="{StaticResource TextPrimary}"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,8"/>
                <TextBlock x:Name="AuthPortalText"
                          Text="SafeLink Authentication Portal"
                          FontSize="12"
                          Foreground="{StaticResource TextSecondary}"
                          HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- Username Field -->
            <StackPanel Grid.Row="1" Margin="0,0,0,15">
                <TextBlock x:Name="UsernameLabel"
                          Text="Username"
                          FontSize="13"
                          FontWeight="Medium"
                          Foreground="{StaticResource TextSecondary}"
                          Margin="0,0,0,6"/>
                <Grid>
                    <TextBox x:Name="UsernameTextBox"
                            Style="{StaticResource ModernInput}"
                            VerticalContentAlignment="Center"/>
                    <controls:FeatherIcon IconName="user"
                                        IconColor="#60FFFFFF"
                                        Width="16" Height="16"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Margin="0,0,15,0"/>
                </Grid>
            </StackPanel>

            <!-- Password Field -->
            <StackPanel Grid.Row="2" Margin="0,0,0,15">
                <TextBlock x:Name="PasswordLabel"
                          Text="Password"
                          FontSize="13"
                          FontWeight="Medium"
                          Foreground="{StaticResource TextSecondary}"
                          Margin="0,0,0,6"/>
                <Grid>
                    <PasswordBox x:Name="PasswordBox"
                                Style="{StaticResource ModernInput}"
                                VerticalContentAlignment="Center"
                                Padding="15,8,45,8"/>

                    <TextBox x:Name="PasswordTextBox"
                            Style="{StaticResource ModernInput}"
                            Visibility="Collapsed"
                            VerticalContentAlignment="Center"
                            Padding="15,8,45,8"/>

                    <!-- Modern Eye Toggle Button -->
                    <Button x:Name="TogglePasswordButton"
                           Background="Transparent"
                           BorderThickness="0"
                           Width="35" Height="35"
                           HorizontalAlignment="Right"
                           VerticalAlignment="Center"
                           Margin="0,0,10,0"
                           Click="TogglePasswordButton_Click"
                           Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                   CornerRadius="17">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#20FFFFFF"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                        <controls:FeatherIcon x:Name="EyeIcon"
                                            IconName="eye"
                                            IconColor="#60FFFFFF"
                                            Width="16" Height="16"/>
                    </Button>
                </Grid>
            </StackPanel>

            <!-- Remember Login Toggle -->
            <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,0,0,20">
                <CheckBox x:Name="RememberLoginCheckBox"
                         IsChecked="True"
                         VerticalAlignment="Center">
                    <CheckBox.Style>
                        <Style TargetType="CheckBox">
                            <Setter Property="Foreground" Value="{StaticResource TextSecondary}"/>
                            <Setter Property="FontSize" Value="12"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="CheckBox">
                                        <StackPanel Orientation="Horizontal">
                                            <!-- Modern Toggle Switch -->
                                            <Border x:Name="ToggleTrack"
                                                   Width="50" Height="26"
                                                   Background="#4A5568"
                                                   CornerRadius="13"
                                                   Margin="0,0,12,0">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#000000" BlurRadius="3" ShadowDepth="1" Opacity="0.3"/>
                                                </Border.Effect>

                                                <Grid>
                                                    <!-- ON/OFF Text -->
                                                    <TextBlock x:Name="OnText" Text="ON" FontSize="8" FontWeight="Bold"
                                                              Foreground="White" HorizontalAlignment="Left"
                                                              VerticalAlignment="Center" Margin="6,0,0,0" Opacity="0"/>
                                                    <TextBlock x:Name="OffText" Text="OFF" FontSize="8" FontWeight="Bold"
                                                              Foreground="White" HorizontalAlignment="Right"
                                                              VerticalAlignment="Center" Margin="0,0,4,0" Opacity="1"/>

                                                    <!-- Toggle Circle -->
                                                    <Border x:Name="ToggleThumb"
                                                           Width="20" Height="20"
                                                           Background="White"
                                                           CornerRadius="10"
                                                           HorizontalAlignment="Left"
                                                           VerticalAlignment="Center"
                                                           Margin="3,0,0,0">
                                                        <Border.Effect>
                                                            <DropShadowEffect Color="#000000" BlurRadius="2" ShadowDepth="1" Opacity="0.2"/>
                                                        </Border.Effect>
                                                        <Border.RenderTransform>
                                                            <TranslateTransform x:Name="ThumbTransform" X="0"/>
                                                        </Border.RenderTransform>
                                                    </Border>
                                                </Grid>
                                            </Border>
                                            <ContentPresenter VerticalAlignment="Center"/>
                                        </StackPanel>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsChecked" Value="True">
                                                <Setter TargetName="ToggleTrack" Property="Background" Value="#1DE9B6"/>
                                                <Setter TargetName="OnText" Property="Opacity" Value="1"/>
                                                <Setter TargetName="OffText" Property="Opacity" Value="0"/>
                                                <Trigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                                           Storyboard.TargetProperty="X"
                                                                           To="24" Duration="0:0:0.2">
                                                                <DoubleAnimation.EasingFunction>
                                                                    <QuadraticEase EasingMode="EaseOut"/>
                                                                </DoubleAnimation.EasingFunction>
                                                            </DoubleAnimation>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </Trigger.EnterActions>
                                                <Trigger.ExitActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetName="ThumbTransform"
                                                                           Storyboard.TargetProperty="X"
                                                                           To="0" Duration="0:0:0.2">
                                                                <DoubleAnimation.EasingFunction>
                                                                    <QuadraticEase EasingMode="EaseOut"/>
                                                                </DoubleAnimation.EasingFunction>
                                                            </DoubleAnimation>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </Trigger.ExitActions>
                                            </Trigger>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter TargetName="ToggleTrack" Property="Effect">
                                                    <Setter.Value>
                                                        <DropShadowEffect Color="#00E5FF" BlurRadius="8" ShadowDepth="0" Opacity="0.5"/>
                                                    </Setter.Value>
                                                </Setter>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </CheckBox.Style>
                    Remember my login
                </CheckBox>
            </StackPanel>

            <!-- Action Buttons -->
            <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,20">
                <Button x:Name="LoginButton"
                       Style="{StaticResource PrimaryButton}"
                       Click="LoginButton_Click"
                       Width="140" Height="45"
                       Margin="0,0,15,0">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="log-in"
                                            IconColor="#0D1421"
                                            Width="16" Height="16"
                                            Margin="0,0,8,0"/>
                        <TextBlock x:Name="LoginButtonText" Text="SIGN IN" FontWeight="SemiBold"/>
                    </StackPanel>
                </Button>

                <Button x:Name="RegisterButton"
                       Style="{StaticResource SecondaryButton}"
                       Click="RegisterButton_Click"
                       Width="140" Height="45">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="user-plus"
                                            IconColor="{StaticResource TextSecondary}"
                                            Width="16" Height="16"
                                            Margin="0,0,8,0"/>
                        <TextBlock x:Name="RegisterButtonText" Text="REGISTER" FontWeight="Medium"/>
                    </StackPanel>
                </Button>
            </StackPanel>

            <!-- Security Badge -->
            <Border Grid.Row="5"
                   Background="#10FFFFFF"
                   BorderBrush="{StaticResource SecondaryAccent}"
                   BorderThickness="1"
                   CornerRadius="10"
                   Padding="15,8"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,15">
                <TextBlock Text="SECURE CONNECTION"
                          FontSize="10"
                          FontWeight="Medium"
                          Foreground="{StaticResource SecondaryAccent}"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"/>
            </Border>



            <!-- Device ID -->
            <StackPanel Grid.Row="6" Margin="0,0,0,25">
                <TextBlock x:Name="DeviceIdDisplay"
                          FontSize="10"
                          Foreground="#4DFFFFFF"
                          HorizontalAlignment="Center"
                          TextAlignment="Center"
                          Text="Loading device information..."
                          FontFamily="Consolas"
                          Margin="0,0,0,10"
                          Opacity="0.3"/>

                <!-- Power Button -->
                <Button x:Name="PowerButton"
                       Background="Transparent"
                       BorderThickness="0"
                       Width="24" Height="24"
                       HorizontalAlignment="Center"
                       Cursor="Hand"
                       Click="PowerButton_Click"
                       MouseEnter="PowerButton_MouseEnter"
                       MouseLeave="PowerButton_MouseLeave">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="12">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#30FF0000"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <controls:FeatherIcon x:Name="PowerIcon"
                                        IconName="power"
                                        IconColor="#00E5FF"
                                        Width="16" Height="16">
                        <controls:FeatherIcon.Effect>
                            <DropShadowEffect Color="#00E5FF" BlurRadius="8" ShadowDepth="0" Opacity="0.8"/>
                        </controls:FeatherIcon.Effect>
                    </controls:FeatherIcon>
                </Button>
            </StackPanel>

            <!-- Footer -->
            <StackPanel Grid.Row="7" HorizontalAlignment="Center" Margin="0,15,0,20">
                <TextBlock x:Name="VersionText"
                          Text="SafeLink v1.0.0 | Enterprise Edition"
                          FontSize="11"
                          Foreground="#80FFFFFF"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,5"/>
                <TextBlock Text="© 2025 Drikon Technologies. All rights reserved."
                          FontSize="10"
                          Foreground="#60FFFFFF"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,0"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
