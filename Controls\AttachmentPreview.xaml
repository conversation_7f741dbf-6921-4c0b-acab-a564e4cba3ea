<UserControl x:Class="SafeLink.Controls.AttachmentPreview"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controls="clr-namespace:SafeLink.Controls">
    
    <UserControl.Resources>
        <!-- Drop Shadow Effect -->
        <DropShadowEffect x:Key="DropShadowEffect" Color="Black" BlurRadius="8" 
                         ShadowDepth="2" Opacity="0.3"/>
        
        <!-- Hover Animation -->
        <Storyboard x:Key="HoverEnterAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1.05" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1.05" Duration="0:0:0.1"/>
        </Storyboard>
        
        <Storyboard x:Key="HoverExitAnimation">
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                           To="1" Duration="0:0:0.1"/>
            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                           To="1" Duration="0:0:0.1"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Border x:Name="MainBorder"
            Background="#2C3E50"
            BorderBrush="#00D4AA"
            BorderThickness="1"
            CornerRadius="8"
            Padding="10"
            Margin="5"
            MaxWidth="200"
            Effect="{StaticResource DropShadowEffect}"
            Cursor="Hand"
            MouseEnter="MainBorder_MouseEnter"
            MouseLeave="MainBorder_MouseLeave"
            MouseLeftButtonUp="MainBorder_MouseLeftButtonUp">
        
        <Border.RenderTransform>
            <ScaleTransform/>
        </Border.RenderTransform>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- File Icon/Preview -->
            <Border Grid.Row="0"
                   Width="60" Height="60"
                   Background="#34495E"
                   CornerRadius="8"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10">

                <Grid>
                    <!-- Image Preview -->
                    <Image x:Name="ImagePreview"
                          Stretch="UniformToFill"
                          Visibility="Collapsed"/>

                    <!-- File Icon -->
                    <TextBlock x:Name="FileIcon"
                              Text="📎"
                              FontSize="24"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Visibility="Visible"/>
                </Grid>
            </Border>
            
            <!-- File Name -->
            <TextBlock Grid.Row="1"
                      x:Name="FileNameText"
                      Text="filename.ext"
                      FontSize="12"
                      FontWeight="SemiBold"
                      Foreground="White"
                      TextTrimming="CharacterEllipsis"
                      HorizontalAlignment="Center"
                      MaxWidth="180"
                      Margin="0,0,0,5"/>
            
            <!-- File Size -->
            <TextBlock Grid.Row="2"
                      x:Name="FileSizeText"
                      Text="0 KB"
                      FontSize="10"
                      Foreground="#B0BEC5"
                      HorizontalAlignment="Center"
                      Margin="0,0,0,5"/>
            
            <!-- Action Buttons -->
            <StackPanel Grid.Row="3" 
                       Orientation="Horizontal" 
                       HorizontalAlignment="Center">
                
                <!-- Download Button -->
                <Button x:Name="DownloadButton"
                       Background="#00D4AA"
                       BorderThickness="0"
                       Width="30" Height="25"
                       Margin="2"
                       ToolTip="تحميل"
                       Click="DownloadButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="4">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#1DE9B6"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    
                    <controls:FeatherIcon IconName="download" IconColor="White" Width="12" Height="12"/>
                </Button>
                
                <!-- Open Button -->
                <Button x:Name="OpenButton"
                       Background="#3498DB"
                       BorderThickness="0"
                       Width="30" Height="25"
                       Margin="2"
                       ToolTip="فتح"
                       Click="OpenButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="4">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#5DADE2"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    
                    <controls:FeatherIcon IconName="external-link" IconColor="White" Width="12" Height="12"/>
                </Button>
                
                <!-- Remove Button (for pending attachments) -->
                <Button x:Name="RemoveButton"
                       Background="#E74C3C"
                       BorderThickness="0"
                       Width="30" Height="25"
                       Margin="2"
                       ToolTip="إزالة"
                       Click="RemoveButton_Click"
                       Visibility="Collapsed">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="4">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#F1948A"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    
                    <controls:FeatherIcon IconName="x" IconColor="White" Width="12" Height="12"/>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
