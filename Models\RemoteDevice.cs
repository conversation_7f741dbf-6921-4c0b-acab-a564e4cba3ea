using System;
using System.Collections.Generic;

namespace SafeLink.Models
{
    public class RemoteDevice
    {
        public string DeviceId { get; set; }
        public string DeviceName { get; set; }
        public string IPAddress { get; set; }
        public string Username { get; set; }
        public UserRole UserRole { get; set; }
        public DeviceStatus Status { get; set; }
        public DateTime LastSeen { get; set; }
        public string OSVersion { get; set; }
        public string SafeLinkVersion { get; set; }
        public SystemInfo SystemInfo { get; set; }
        public List<string> ActiveProcesses { get; set; }
        public NetworkInfo NetworkInfo { get; set; }
    }

    public class SystemInfo
    {
        public string OSPlatform { get; set; }
        public string WindowsVersion { get; set; }
        public string WindowsBuild { get; set; }
        public string MachineName { get; set; }
        public string ProcessorName { get; set; }
        public int ProcessorCount { get; set; }
        public long TotalMemory { get; set; }
        public long AvailableMemory { get; set; }
        public double CpuUsage { get; set; }
        public List<NetworkAdapter> NetworkAdapters { get; set; }
    }

    public class NetworkInfo
    {
        public int UdpPort { get; set; }
        public int TcpPort { get; set; }
        public bool IsNetworkActive { get; set; }
        public int ConnectedPeers { get; set; }
        public DateTime LastNetworkActivity { get; set; }
    }

    public class NetworkAdapter
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string IPAddress { get; set; }
        public string MACAddress { get; set; }
        public bool IsActive { get; set; }
    }

    public enum DeviceStatus
    {
        Online,
        Offline,
        Busy,
        Away,
        Maintenance
    }
}
