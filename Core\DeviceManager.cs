using System;
using System.Collections.Generic;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Win32;

namespace SafeLink.Core
{
    public static class DeviceManager
    {
        private static string _cachedDeviceId = null;

        public static string GetDeviceId()
        {
            // Return cached device ID if available
            if (!string.IsNullOrEmpty(_cachedDeviceId))
            {
                return _cachedDeviceId;
            }

            try
            {
                var deviceInfo = GetDeviceInfo();
                var combinedInfo = $"{deviceInfo.ProcessorId}_{deviceInfo.MotherboardSerial}_{deviceInfo.BiosSerial}";

                // If we got valid device info, use it
                if (!string.IsNullOrEmpty(deviceInfo.ProcessorId) ||
                    !string.IsNullOrEmpty(deviceInfo.MotherboardSerial) ||
                    !string.IsNullOrEmpty(deviceInfo.BiosSerial))
                {
                    using (var sha256 = SHA256.Create())
                    {
                        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combinedInfo));
                        var base64Hash = Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "");
                        // Ensure we have at least 32 characters
                        if (base64Hash.Length >= 32)
                        {
                            _cachedDeviceId = base64Hash.Substring(0, 32);
                        }
                        else
                        {
                            // Pad with machine name if too short
                            _cachedDeviceId = (base64Hash + Environment.MachineName.Replace("-", "")).Substring(0, Math.Min(32, (base64Hash + Environment.MachineName.Replace("-", "")).Length));
                        }
                        System.Diagnostics.Debug.WriteLine($"✅ Device ID generated from hardware: {_cachedDeviceId}");
                        return _cachedDeviceId;
                    }
                }
                else
                {
                    // Fallback to stable machine-based ID
                    var fallback = $"{Environment.MachineName}_{Environment.UserName}_SafeLink2025";
                    using (var sha256 = SHA256.Create())
                    {
                        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallback));
                        var base64Hash = Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "");
                        // Ensure we have exactly 32 characters
                        if (base64Hash.Length >= 32)
                        {
                            _cachedDeviceId = base64Hash.Substring(0, 32);
                        }
                        else
                        {
                            // Pad with additional data if too short
                            var padding = "SafeLink2025DeviceID";
                            _cachedDeviceId = (base64Hash + padding).Substring(0, 32);
                        }
                        System.Diagnostics.Debug.WriteLine($"✅ Device ID generated from fallback: {_cachedDeviceId}");
                        return _cachedDeviceId;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to get device ID: {ex.Message}");
                // Stable fallback that doesn't change
                var fallback = $"{Environment.MachineName}_{Environment.UserName}_SafeLink2025";
                using (var sha256 = SHA256.Create())
                {
                    var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(fallback));
                    var base64Hash = Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "");
                    // Ensure we have exactly 32 characters
                    if (base64Hash.Length >= 32)
                    {
                        _cachedDeviceId = base64Hash.Substring(0, 32);
                    }
                    else
                    {
                        // Pad with additional data if too short
                        var padding = "SafeLink2025StableFallback";
                        _cachedDeviceId = (base64Hash + padding).Substring(0, 32);
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ Device ID generated from stable fallback: {_cachedDeviceId}");
                    return _cachedDeviceId;
                }
            }
        }

        public static void ResetDeviceIdCache()
        {
            _cachedDeviceId = null;
            System.Diagnostics.Debug.WriteLine("🔄 Device ID cache reset");
        }

        public static string GenerateDeviceId()
        {
            // Generate a new unique device ID
            var timestamp = DateTime.Now.Ticks.ToString();
            var random = Guid.NewGuid().ToString("N");
            var machine = Environment.MachineName;

            var combined = $"{timestamp}_{random}_{machine}_SafeLink";

            using (var sha256 = SHA256.Create())
            {
                var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(combined));
                var base64Hash = Convert.ToBase64String(hash).Replace("+", "").Replace("/", "").Replace("=", "");

                // Return first 32 characters
                return base64Hash.Length >= 32 ? base64Hash.Substring(0, 32) : base64Hash.PadRight(32, '0');
            }
        }

        private static DeviceInfo GetDeviceInfo()
        {
            var deviceInfo = new DeviceInfo();

            try
            {
                // Get processor ID
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        deviceInfo.ProcessorId = obj["ProcessorId"]?.ToString() ?? "";
                        break;
                    }
                }

                // Get motherboard serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        deviceInfo.MotherboardSerial = obj["SerialNumber"]?.ToString() ?? "";
                        break;
                    }
                }

                // Get BIOS serial
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BIOS"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        deviceInfo.BiosSerial = obj["SerialNumber"]?.ToString() ?? "";
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting device info: {ex.Message}");
            }

            return deviceInfo;
        }

        public static SystemInfo GetSystemInfo()
        {
            var systemInfo = new SystemInfo();

            try
            {
                // Operating System
                systemInfo.OSVersion = Environment.OSVersion.ToString();
                systemInfo.OSPlatform = Environment.OSVersion.Platform.ToString();
                systemInfo.MachineName = Environment.MachineName;
                systemInfo.UserName = Environment.UserName;
                systemInfo.ProcessorCount = Environment.ProcessorCount;

                // Memory information
                using (var searcher = new ManagementObjectSearcher("SELECT TotalPhysicalMemory FROM Win32_ComputerSystem"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        if (obj["TotalPhysicalMemory"] != null)
                        {
                            systemInfo.TotalMemory = Convert.ToInt64(obj["TotalPhysicalMemory"]);
                        }
                        break;
                    }
                }

                // CPU information
                using (var searcher = new ManagementObjectSearcher("SELECT Name, MaxClockSpeed FROM Win32_Processor"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        systemInfo.ProcessorName = obj["Name"]?.ToString() ?? "";
                        if (obj["MaxClockSpeed"] != null)
                        {
                            systemInfo.ProcessorSpeed = Convert.ToInt32(obj["MaxClockSpeed"]);
                        }
                        break;
                    }
                }

                // Network adapters
                using (var searcher = new ManagementObjectSearcher("SELECT Name, MACAddress FROM Win32_NetworkAdapter WHERE NetEnabled = True"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var name = obj["Name"]?.ToString();
                        var mac = obj["MACAddress"]?.ToString();
                        if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(mac))
                        {
                            systemInfo.NetworkAdapters.Add($"{name} ({mac})");
                        }
                    }
                }

                // Windows version from registry
                try
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows NT\CurrentVersion"))
                    {
                        if (key != null)
                        {
                            systemInfo.WindowsVersion = key.GetValue("ProductName")?.ToString() ?? "";
                            systemInfo.WindowsBuild = key.GetValue("CurrentBuild")?.ToString() ?? "";
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error reading Windows version: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting system info: {ex.Message}");
            }

            return systemInfo;
        }

        public static bool IsAdministrator()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                return principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }

        public static string GetLocalIPAddress()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT IPAddress FROM Win32_NetworkAdapterConfiguration WHERE IPEnabled = True"))
                {
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        var ipAddresses = obj["IPAddress"] as string[];
                        if (ipAddresses != null)
                        {
                            foreach (var ip in ipAddresses)
                            {
                                if (System.Net.IPAddress.TryParse(ip, out var addr) &&
                                    addr.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork &&
                                    !System.Net.IPAddress.IsLoopback(addr))
                                {
                                    return ip;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting IP address: {ex.Message}");
            }

            return "127.0.0.1";
        }
    }

    public class DeviceInfo
    {
        public string ProcessorId { get; set; } = "";
        public string MotherboardSerial { get; set; } = "";
        public string BiosSerial { get; set; } = "";
    }

    public class SystemInfo
    {
        public string OSVersion { get; set; } = "";
        public string OSPlatform { get; set; } = "";
        public string MachineName { get; set; } = "";
        public string UserName { get; set; } = "";
        public int ProcessorCount { get; set; }
        public long TotalMemory { get; set; }
        public string ProcessorName { get; set; } = "";
        public int ProcessorSpeed { get; set; }
        public string WindowsVersion { get; set; } = "";
        public string WindowsBuild { get; set; } = "";
        public List<string> NetworkAdapters { get; set; } = new List<string>();
    }
}
