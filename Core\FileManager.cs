using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;

namespace SafeLink.Core
{
    public static class FileManager
    {
        private static readonly string AttachmentsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Attachments");
        private static readonly string TempPath = Path.Combine(AttachmentsPath, "Temp");
        
        // Supported file types
        private static readonly HashSet<string> AllowedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // Images
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg",
            // Documents
            ".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt",
            // Spreadsheets
            ".xls", ".xlsx", ".csv", ".ods",
            // Presentations
            ".ppt", ".pptx", ".odp",
            // Archives
            ".zip", ".rar", ".7z", ".tar", ".gz",
            // Audio
            ".mp3", ".wav", ".flac", ".aac", ".ogg",
            // Video
            ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv",
            // Code
            ".cs", ".js", ".html", ".css", ".xml", ".json", ".sql"
        };

        // File size limits (in bytes)
        private const long MaxFileSize = 50 * 1024 * 1024; // 50 MB
        private const long MaxTotalSize = 200 * 1024 * 1024; // 200 MB per conversation

        static FileManager()
        {
            // Create directories if they don't exist
            Directory.CreateDirectory(AttachmentsPath);
            Directory.CreateDirectory(TempPath);
        }

        public static async Task<FileValidationResult> ValidateFileAsync(string filePath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    if (!File.Exists(filePath))
                        return new FileValidationResult { IsValid = false, ErrorMessage = "الملف غير موجود" };

                    var fileInfo = new FileInfo(filePath);

                    // Check file size
                    if (fileInfo.Length > MaxFileSize)
                        return new FileValidationResult { IsValid = false, ErrorMessage = $"حجم الملف كبير جداً. الحد الأقصى {MaxFileSize / (1024 * 1024)} ميجابايت" };

                    // Check file extension
                    if (!AllowedExtensions.Contains(fileInfo.Extension))
                        return new FileValidationResult { IsValid = false, ErrorMessage = "نوع الملف غير مدعوم" };

                    // Check if file is accessible
                    try
                    {
                        using var stream = File.OpenRead(filePath);
                        // File is accessible
                    }
                    catch
                    {
                        return new FileValidationResult { IsValid = false, ErrorMessage = "لا يمكن الوصول للملف" };
                    }

                    return new FileValidationResult { IsValid = true };
                });
            }
            catch (Exception ex)
            {
                return new FileValidationResult { IsValid = false, ErrorMessage = $"خطأ في التحقق من الملف: {ex.Message}" };
            }
        }

        public static async Task<string> SaveAttachmentAsync(string sourceFilePath, int fromUserId, int toUserId)
        {
            try
            {
                var fileInfo = new FileInfo(sourceFilePath);
                var fileName = $"{DateTime.Now:yyyyMMdd_HHmmss}_{fromUserId}_{toUserId}_{fileInfo.Name}";
                var destinationPath = Path.Combine(AttachmentsPath, fileName);

                // Copy file to attachments directory asynchronously
                await Task.Run(() => File.Copy(sourceFilePath, destinationPath, true));

                return fileName;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حفظ المرفق: {ex.Message}");
            }
        }

        public static string GetAttachmentPath(string fileName)
        {
            return Path.Combine(AttachmentsPath, fileName);
        }

        public static bool AttachmentExists(string fileName)
        {
            return File.Exists(GetAttachmentPath(fileName));
        }

        public static async Task<string> GetFileHashAsync(string filePath)
        {
            try
            {
                using var sha256 = SHA256.Create();
                using var stream = File.OpenRead(filePath);
                var hash = await Task.Run(() => sha256.ComputeHash(stream));
                return Convert.ToBase64String(hash);
            }
            catch
            {
                return string.Empty;
            }
        }

        public static string GetFileIcon(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            
            return extension switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => "🖼️",
                ".pdf" => "📄",
                ".doc" or ".docx" => "📝",
                ".xls" or ".xlsx" => "📊",
                ".ppt" or ".pptx" => "📋",
                ".zip" or ".rar" or ".7z" => "🗜️",
                ".mp3" or ".wav" or ".flac" => "🎵",
                ".mp4" or ".avi" or ".mkv" => "🎬",
                ".txt" => "📃",
                ".cs" or ".js" or ".html" => "💻",
                _ => "📎"
            };
        }

        public static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        public static async Task CleanupTempFilesAsync()
        {
            try
            {
                await Task.Run(() =>
                {
                    var tempFiles = Directory.GetFiles(TempPath);
                    var cutoffTime = DateTime.Now.AddHours(-1); // Remove files older than 1 hour

                    foreach (var file in tempFiles)
                    {
                        var fileInfo = new FileInfo(file);
                        if (fileInfo.CreationTime < cutoffTime)
                        {
                            File.Delete(file);
                        }
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error cleaning temp files: {ex.Message}");
            }
        }

        public static List<string> GetSupportedExtensions()
        {
            return AllowedExtensions.ToList();
        }

        public static bool IsImageFile(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return new[] { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp" }.Contains(extension);
        }

        public static bool IsVideoFile(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return new[] { ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv" }.Contains(extension);
        }

        public static bool IsAudioFile(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return new[] { ".mp3", ".wav", ".flac", ".aac", ".ogg" }.Contains(extension);
        }
    }

    public class FileValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }
}
