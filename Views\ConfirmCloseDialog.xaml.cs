using System;
using System.Windows;
using SafeLink.Core;

namespace SafeLink.Views
{
    public partial class ConfirmCloseDialog : Window
    {
        public bool Result { get; private set; } = false;

        public ConfirmCloseDialog()
        {
            InitializeComponent();

            // Subscribe to language changes
            LanguageManager.LanguageChanged += UpdateLanguage;
            UpdateLanguage(); // Initial language setup

            // Set focus to No button by default
            Loaded += (s, e) => NoButton.Focus();

            // Handle Escape key to close as No
            KeyDown += (s, e) =>
            {
                if (e.Key == System.Windows.Input.Key.Escape)
                {
                    Result = false;
                    Close();
                }
            };

            // Handle window closing to unsubscribe from events
            Closing += (s, e) => LanguageManager.LanguageChanged -= UpdateLanguage;
        }

        private void YesButton_Click(object sender, RoutedEventArgs e)
        {
            Result = true;
            Close();
        }

        private void NoButton_Click(object sender, RoutedEventArgs e)
        {
            Result = false;
            Close();
        }

        private void UpdateLanguage()
        {
            try
            {
                var currentLanguage = LanguageManager.CurrentLanguage;

                // Update window title
                Title = LanguageManager.GetString("ConfirmClose") ?? "SafeLink - تأكيد الإغلاق";

                // Update dialog texts based on language
                if (currentLanguage == "ar")
                {
                    MainQuestionText.Text = "هل تريد إغلاق البرنامج؟";
                    SubMessageText.Text = "سيتم قطع جميع الاتصالات النشطة";
                    FlowDirection = FlowDirection.RightToLeft;
                }
                else
                {
                    MainQuestionText.Text = "Do you want to close the program?";
                    SubMessageText.Text = "All active connections will be terminated";
                    FlowDirection = FlowDirection.LeftToRight;
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update language: {ex.Message}");
            }
        }

        public static bool ShowDialog(Window owner)
        {
            try
            {
                var dialog = new ConfirmCloseDialog();

                if (owner != null)
                {
                    dialog.Owner = owner;
                }

                dialog.ShowDialog();
                return dialog.Result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing close confirmation dialog: {ex.Message}");
                // Default to not closing if there's an error
                return false;
            }
        }
    }
}
