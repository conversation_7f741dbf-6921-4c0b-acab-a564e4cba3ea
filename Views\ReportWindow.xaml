<Window x:Class="SafeLink.Views.ReportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink - إرسال تقرير"
        Height="600" Width="500"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        ShowInTaskbar="False">

    <Border Background="#1A1A2E" 
            BorderBrush="#00D4AA" 
            BorderThickness="2" 
            CornerRadius="15"
            Effect="{StaticResource WindowShadow}">
        
        <Grid Margin="25">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,15,0,25">
                <controls:FeatherIcon IconName="send"
                                    IconColor="#00D4AA"
                                    Width="36" Height="36"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,12">
                    <controls:FeatherIcon.Effect>
                        <DropShadowEffect Color="#00D4AA" BlurRadius="10" ShadowDepth="0" Opacity="0.7"/>
                    </controls:FeatherIcon.Effect>
                </controls:FeatherIcon>
                
                <TextBlock x:Name="HeaderText"
                          Text="إرسال تقرير"
                          FontSize="20"
                          FontWeight="Bold"
                          Foreground="#FFFFFF"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,8"/>
                
                <TextBlock x:Name="SubHeaderText"
                          Text="اختر نوع التقرير المطلوب إرساله"
                          FontSize="14"
                          Foreground="#B0FFFFFF"
                          HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- Report Options -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <StackPanel Margin="0,0,0,20">

                    <!-- Basic Report Option -->
                    <Border Background="#2C3E50"
                            BorderBrush="#34495E"
                            BorderThickness="1"
                            CornerRadius="10"
                            Margin="0,0,0,15">
                        <StackPanel Margin="20">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <controls:FeatherIcon x:Name="BasicReportIcon"
                                                    Grid.Column="0"
                                                    IconName="camera"
                                                    IconColor="#FFFFFF"
                                                    Width="24" Height="24"
                                                    VerticalAlignment="Center"
                                                    Margin="0,0,15,0"/>

                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <TextBlock x:Name="BasicReportTitle"
                                              Text="تقرير أساسي شامل"
                                              FontSize="16"
                                              FontWeight="SemiBold"
                                              Foreground="#FFFFFF"
                                              Margin="0,0,0,5"/>
                                    <TextBlock x:Name="BasicReportDesc"
                                              Text="لقطة شاشة + معلومات النظام + تقرير الحالة"
                                              FontSize="12"
                                              Foreground="#BDC3C7"
                                              TextWrapping="Wrap"/>
                                </StackPanel>

                                <CheckBox x:Name="BasicReportCheckBox"
                                         Grid.Column="2"
                                         VerticalAlignment="Center"
                                         IsChecked="True"
                                         Checked="BasicReportCheckBox_Checked"
                                         Unchecked="BasicReportCheckBox_Unchecked"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Custom Description Option -->
                    <Border Background="#2C3E50"
                            BorderBrush="#34495E"
                            BorderThickness="1"
                            CornerRadius="10"
                            Margin="0,0,0,15">
                        <StackPanel Margin="20">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <controls:FeatherIcon x:Name="CustomDescIcon"
                                                    Grid.Column="0"
                                                    IconName="edit-3"
                                                    IconColor="#FFFFFF"
                                                    Width="24" Height="24"
                                                    VerticalAlignment="Center"
                                                    Margin="0,0,15,0"/>

                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <TextBlock x:Name="CustomDescTitle"
                                              Text="إضافة وصف للمشكلة"
                                              FontSize="16"
                                              FontWeight="SemiBold"
                                              Foreground="#FFFFFF"
                                              Margin="0,0,0,5"/>
                                    <TextBlock x:Name="CustomDescDesc"
                                              Text="اكتب وصف تفصيلي للمشكلة أو الملاحظات"
                                              FontSize="12"
                                              Foreground="#BDC3C7"
                                              TextWrapping="Wrap"/>
                                </StackPanel>

                                <CheckBox x:Name="CustomDescCheckBox"
                                         Grid.Column="2"
                                         VerticalAlignment="Center"
                                         Checked="CustomDescCheckBox_Checked"
                                         Unchecked="CustomDescCheckBox_Unchecked"/>
                            </Grid>

                            <!-- Custom Description Text Area (Initially Hidden) -->
                            <StackPanel x:Name="CustomDescPanel"
                                       Visibility="Collapsed"
                                       Margin="0,15,0,0">
                                <TextBlock x:Name="CustomDescLabel"
                                          Text="اكتب وصف المشكلة بالتفصيل:"
                                          FontSize="14"
                                          Foreground="#FFFFFF"
                                          Margin="0,0,0,8"/>
                                <TextBox x:Name="CustomDescTextBox"
                                        Background="#34495E"
                                        Foreground="#FFFFFF"
                                        BorderBrush="#00D4AA"
                                        BorderThickness="1"
                                        Padding="12"
                                        Height="100"
                                        TextWrapping="Wrap"
                                        AcceptsReturn="True"
                                        VerticalScrollBarVisibility="Auto"
                                        FontSize="13"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>

            <!-- Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,15">
                <!-- Send Button -->
                <Button x:Name="SendButton"
                       Content="إرسال التقرير"
                       Width="140" Height="45"
                       Background="#00D4AA"
                       BorderThickness="0"
                       Foreground="White"
                       FontSize="14"
                       FontWeight="Bold"
                       Margin="0,0,15,0"
                       Cursor="Hand"
                       Click="SendButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="8"
                                               BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            <Border.Effect>
                                                <DropShadowEffect Color="#00D4AA" BlurRadius="10" ShadowDepth="0" Opacity="0.6"/>
                                            </Border.Effect>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#00E5BB"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#00C399"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Cancel Button -->
                <Button x:Name="CancelButton"
                       Content="إلغاء"
                       Width="100" Height="45"
                       Background="#E74C3C"
                       BorderThickness="0"
                       Foreground="White"
                       FontSize="14"
                       FontWeight="Bold"
                       Cursor="Hand"
                       Click="CancelButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="8"
                                               BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            <Border.Effect>
                                                <DropShadowEffect Color="#E74C3C" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                                            </Border.Effect>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#C0392B"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#A93226"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
