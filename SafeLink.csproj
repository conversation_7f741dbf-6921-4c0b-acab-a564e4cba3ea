<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <ApplicationIcon>Icons\SafeLink.ico</ApplicationIcon>
    <ApplicationHighDpiMode>PerMonitorV2</ApplicationHighDpiMode>

    <AssemblyTitle>SafeLink - Secure Communication</AssemblyTitle>
    <AssemblyDescription>SafeLink - Secure Communication Platform by Drikon</AssemblyDescription>
    <AssemblyCompany>Drikon</AssemblyCompany>
    <AssemblyProduct>SafeLink</AssemblyProduct>
    <AssemblyCopyright>© 2024 Drikon. All rights reserved.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <StartupObject>SafeLink.App</StartupObject>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="System.Management" Version="8.0.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    <PackageReference Include="Microsoft.Win32.Registry" Version="5.0.0" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="System.IO.Compression.ZipFile" Version="4.3.0" />
  </ItemGroup>

  <ItemGroup>
    <Resource Include="Icons\SafeLink.ico" />
  </ItemGroup>



  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
