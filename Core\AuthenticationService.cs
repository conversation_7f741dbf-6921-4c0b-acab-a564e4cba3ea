using System;
using System.Threading.Tasks;
using SafeLink.Models;

namespace SafeLink.Core
{
    public class AuthenticationService
    {
        private readonly DatabaseManager _databaseManager;

        public AuthenticationService()
        {
            _databaseManager = App.GetDatabaseManager();
            if (_databaseManager == null)
            {
                throw new InvalidOperationException("DatabaseManager is not initialized. Please ensure the application is properly started.");
            }
        }

        public async Task<User> AuthenticateAsync(string username, string password)
        {
            try
            {
                // Temporarily disable device ID check for development
                var deviceId = "TEMP_DEVICE_ID"; // DeviceManager.GetDeviceId();
                return await _databaseManager.AuthenticateUserAsync(username, password, deviceId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Authentication error: {ex.Message}");
                throw;
            }
        }

        public async Task<User> AuthenticateAsync(string username, string password, string deviceId)
        {
            try
            {
                // Temporarily disable device ID verification for development
                System.Diagnostics.Debug.WriteLine($"🔧 TEMP: Device ID verification disabled for development");
                System.Diagnostics.Debug.WriteLine($"🔐 Authenticating user: {username}");

                // Use temporary device ID for all users
                var tempDeviceId = "TEMP_DEVICE_ID";
                var result = await _databaseManager.AuthenticateUserAsync(username, password, tempDeviceId);

                if (result != null)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Authentication successful for: {username} (Device ID check bypassed)");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Authentication failed for: {username}");
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"🔴 Authentication error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"🔴 Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<bool> RegisterAsync(string username, string password, UserRole role = UserRole.User)
        {
            try
            {
                // Temporarily disable device ID check for development
                var deviceId = "TEMP_DEVICE_ID"; // DeviceManager.GetDeviceId();
                return await _databaseManager.RegisterUserAsync(username, password, deviceId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Registration error: {ex.Message}");
                throw;
            }
        }

        public async Task<bool> RegisterAsync(string username, string password, string deviceId, UserRole role = UserRole.User)
        {
            try
            {
                return await _databaseManager.RegisterUserAsync(username, password, deviceId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Registration error: {ex.Message}");
                throw;
            }
        }

        public bool ValidateCredentials(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                return false;

            if (username.Length < 3 || password.Length < 4)
                return false;

            return true;
        }

        public string HashPassword(string password)
        {
            return BCrypt.Net.BCrypt.HashPassword(password);
        }

        public bool VerifyPassword(string password, string hash)
        {
            return BCrypt.Net.BCrypt.Verify(password, hash);
        }
    }
}
