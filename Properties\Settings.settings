<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="SafeLink.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="Language" Type="System.String" Scope="User">
      <Value Profile="(Default)">ar</Value>
    </Setting>
    <Setting Name="NetworkPort" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">8888</Value>
    </Setting>
    <Setting Name="AutoDiscovery" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="BroadcastPresence" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="EnablePrinterSharing" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="SharedPrinters" Type="System.Collections.Specialized.StringCollection" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="LogActivity" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ReportShortcut" Type="System.String" Scope="User">
      <Value Profile="(Default)">F8</Value>
    </Setting>
    <Setting Name="WindowWidth" Type="System.Double" Scope="User">
      <Value Profile="(Default)">1200</Value>
    </Setting>
    <Setting Name="WindowHeight" Type="System.Double" Scope="User">
      <Value Profile="(Default)">800</Value>
    </Setting>
    <Setting Name="WindowLeft" Type="System.Double" Scope="User">
      <Value Profile="(Default)">-1</Value>
    </Setting>
    <Setting Name="WindowTop" Type="System.Double" Scope="User">
      <Value Profile="(Default)">-1</Value>
    </Setting>
    <Setting Name="WindowState" Type="System.Windows.WindowState" Scope="User">
      <Value Profile="(Default)">Normal</Value>
    </Setting>
    <Setting Name="SavedUsername" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="SavedPassword" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="RememberLogin" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="EncryptMessages" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
  </Settings>
</SettingsFile>
