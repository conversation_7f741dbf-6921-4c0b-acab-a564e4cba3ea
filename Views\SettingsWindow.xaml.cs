using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SafeLink.Core;
using System.Printing;
using System.IO;
using System.Diagnostics;

namespace SafeLink.Views
{
    public partial class SettingsWindow : Window
    {
        public SettingsWindow()
        {
            InitializeComponent();

            // Subscribe to language changes
            LanguageManager.LanguageChanged += UpdateLanguage;

            LoadSettings();
            LoadDeviceInfo();
            LoadPrinters();
            UpdateLanguage();

            // Handle window closing to unsubscribe from events
            Closing += (s, e) => LanguageManager.LanguageChanged -= UpdateLanguage;
        }

        private void LoadSettings()
        {
            try
            {
                // Load language setting from saved preferences
                string savedLanguage = Properties.Settings.Default.Language;
                if (string.IsNullOrEmpty(savedLanguage))
                {
                    savedLanguage = "en"; // Default to English
                }

                // Set ComboBox selection based on saved language
                foreach (ComboBoxItem item in LanguageComboBox.Items)
                {
                    if (item.Tag.ToString() == savedLanguage)
                    {
                        LanguageComboBox.SelectedItem = item;
                        break;
                    }
                }

                // If no match found, default to English
                if (LanguageComboBox.SelectedItem == null)
                {
                    LanguageComboBox.SelectedIndex = 0;
                }

                System.Diagnostics.Debug.WriteLine($"✅ Language loaded: {savedLanguage}");

                // Load network settings - default values
                PortTextBox.Text = "8888";
                AutoDiscoveryCheckBox.IsChecked = true;
                BroadcastPresenceCheckBox.IsChecked = true;

                // Load printer settings - default values
                EnablePrinterSharingCheckBox.IsChecked = false;

                // Load security settings - default values
                LogActivityCheckBox.IsChecked = true;
                ReportShortcutTextBox.Text = "F8";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load settings: {ex.Message}");
                // Fallback to default
                LanguageComboBox.SelectedIndex = 0;
            }
        }

        private void LoadDeviceInfo()
        {
            try
            {
                var deviceId = DeviceManager.GetDeviceId();
                var systemInfo = DeviceManager.GetSystemInfo();

                DeviceIdTextBlock.Text = $"{deviceId.Substring(0, 8)}...";
                MachineNameTextBlock.Text = systemInfo.MachineName;
                OSVersionTextBlock.Text = $"{systemInfo.WindowsVersion} ({systemInfo.WindowsBuild})";
                IPAddressTextBlock.Text = DeviceManager.GetLocalIPAddress();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load device info: {ex.Message}");
            }
        }

        private void LoadPrinters()
        {
            try
            {
                PrintersListBox.Items.Clear();

                // Get local printers
                var printServer = new LocalPrintServer();
                var printers = printServer.GetPrintQueues();

                foreach (var printer in printers)
                {
                    var item = new CheckBox
                    {
                        Content = printer.Name,
                        Foreground = System.Windows.Media.Brushes.LightGreen,
                        Margin = new Thickness(5),
                        IsChecked = false
                    };

                    PrintersListBox.Items.Add(item);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to load printers: {ex.Message}");
                PrintersListBox.Items.Add(new TextBlock
                {
                    Text = "No printers available",
                    Foreground = System.Windows.Media.Brushes.Gray
                });
            }
        }

        private void LanguageComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (LanguageComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var language = selectedItem.Tag.ToString();

                // Update UI direction for Arabic
                if (language == "ar")
                {
                    FlowDirection = FlowDirection.RightToLeft;
                }
                else
                {
                    FlowDirection = FlowDirection.LeftToRight;
                }
            }
        }

        private void EnablePrinterSharingCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            PrintersListBox.IsEnabled = true;
            AuthorizedUsersListBox.IsEnabled = true;
        }

        private void EnablePrinterSharingCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            PrintersListBox.IsEnabled = false;
            AuthorizedUsersListBox.IsEnabled = false;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Save language setting
                if (LanguageComboBox.SelectedItem is ComboBoxItem selectedItem)
                {
                    string languageCode = selectedItem.Tag.ToString();
                    SafeLink.Core.LanguageManager.SetLanguage(languageCode);

                    // Save to user settings
                    Properties.Settings.Default.Language = languageCode;
                    Properties.Settings.Default.Save();

                    System.Diagnostics.Debug.WriteLine($"✅ Language saved to settings: {languageCode}");
                }

                CustomMessageBox.Show(SafeLink.Core.LanguageManager.GetString("SettingsSaved"), "SafeLink",
                    CustomMessageBox.MessageBoxType.Information,
                    CustomMessageBox.MessageBoxButtons.OK, this);

                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                CustomMessageBox.Show($"Failed to save settings: {ex.Message}", "Error",
                    CustomMessageBox.MessageBoxType.Error,
                    CustomMessageBox.MessageBoxButtons.OK, this);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void UpdateLanguage()
        {
            try
            {
                // Update window title
                Title = LanguageManager.GetString("Settings");

                // Update group box headers
                if (LanguageGroupBox != null)
                    LanguageGroupBox.Header = LanguageManager.GetString("LanguageSettings");

                if (NetworkGroupBox != null)
                    NetworkGroupBox.Header = LanguageManager.GetString("NetworkSettings");

                if (PrinterGroupBox != null)
                    PrinterGroupBox.Header = LanguageManager.GetString("PrinterSharing");

                if (SecurityGroupBox != null)
                    SecurityGroupBox.Header = LanguageManager.GetString("SecuritySettings");

                if (DeviceGroupBox != null)
                    DeviceGroupBox.Header = LanguageManager.GetString("DeviceInformation");

                // Update labels
                if (InterfaceLanguageLabel != null)
                    InterfaceLanguageLabel.Content = LanguageManager.GetString("InterfaceLanguage");

                if (LanguageRestartNote != null)
                    LanguageRestartNote.Text = LanguageManager.GetString("LanguageRestartNote");

                if (DiscoveryPortLabel != null)
                    DiscoveryPortLabel.Content = LanguageManager.GetString("DiscoveryPort");

                if (AutoDiscoveryCheckBox != null)
                    AutoDiscoveryCheckBox.Content = LanguageManager.GetString("EnableAutoDiscovery");

                if (BroadcastPresenceCheckBox != null)
                    BroadcastPresenceCheckBox.Content = LanguageManager.GetString("BroadcastPresence");

                if (EnablePrinterSharingCheckBox != null)
                    EnablePrinterSharingCheckBox.Content = LanguageManager.GetString("EnablePrinterSharing");

                if (AvailablePrintersLabel != null)
                    AvailablePrintersLabel.Content = LanguageManager.GetString("AvailablePrinters");

                if (AuthorizedUsersLabel != null)
                    AuthorizedUsersLabel.Content = LanguageManager.GetString("AuthorizedUsers");

                if (EncryptMessagesCheckBox != null)
                    EncryptMessagesCheckBox.Content = LanguageManager.GetString("EncryptMessages");

                if (LogActivityCheckBox != null)
                    LogActivityCheckBox.Content = LanguageManager.GetString("LogActivity");

                if (ReportShortcutLabel != null)
                    ReportShortcutLabel.Content = LanguageManager.GetString("ReportShortcut");

                if (DeviceIdLabel != null)
                    DeviceIdLabel.Content = LanguageManager.GetString("DeviceID");

                if (MachineNameLabel != null)
                    MachineNameLabel.Content = LanguageManager.GetString("MachineName");

                if (OSVersionLabel != null)
                    OSVersionLabel.Content = LanguageManager.GetString("OSVersion");

                if (IPAddressLabel != null)
                    IPAddressLabel.Content = LanguageManager.GetString("IPAddress");

                // Update buttons
                if (SaveButtonText != null)
                    SaveButtonText.Text = LanguageManager.GetString("Save");

                if (CancelButtonText != null)
                    CancelButtonText.Text = LanguageManager.GetString("Cancel");

                // Update UI direction for Arabic
                if (LanguageManager.CurrentLanguage == "ar")
                {
                    FlowDirection = FlowDirection.RightToLeft;
                }
                else
                {
                    FlowDirection = FlowDirection.LeftToRight;
                }

                System.Diagnostics.Debug.WriteLine($"✅ SettingsWindow language updated to: {LanguageManager.CurrentLanguage}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating SettingsWindow language: {ex.Message}");
            }
        }

        private void ClearTempDataButton_Click(object sender, RoutedEventArgs e)
        {
            // Show clear temporary data confirmation dialog
            bool shouldClear = UnifiedMessageDialog.ShowDialog(this, MessageDialogType.Custom,
                "SafeLink - Clear Data",
                LanguageManager.GetString("ClearDataQuestion"),
                LanguageManager.GetString("ClearDataExplanation"));

            if (shouldClear)
            {
                try
                {
                    // Clear temporary data
                    ClearTemporaryData();

                    // Show success message
                    CustomMessageBox.Show("تم مسح البيانات المؤقتة بنجاح", "SafeLink",
                        CustomMessageBox.MessageBoxType.Information,
                        CustomMessageBox.MessageBoxButtons.OK, this);

                    System.Diagnostics.Debug.WriteLine("✅ Temporary data cleared successfully");
                }
                catch (Exception ex)
                {
                    CustomMessageBox.Show($"فشل في مسح البيانات المؤقتة: {ex.Message}", "خطأ",
                        CustomMessageBox.MessageBoxType.Error,
                        CustomMessageBox.MessageBoxButtons.OK, this);

                    System.Diagnostics.Debug.WriteLine($"❌ Failed to clear temporary data: {ex.Message}");
                }
            }
        }

        private void FactoryResetButton_Click(object sender, RoutedEventArgs e)
        {
            // Show factory reset confirmation dialog
            bool shouldReset = UnifiedMessageDialog.ShowDialog(this, MessageDialogType.Custom,
                "SafeLink - Factory Reset",
                LanguageManager.GetString("FactoryResetQuestion"),
                LanguageManager.GetString("FactoryResetExplanation"));

            if (shouldReset)
            {
                try
                {
                    // Perform factory reset
                    PerformFactoryReset();

                    // Show success message and restart
                    CustomMessageBox.Show("تم إعادة ضبط المصنع بنجاح. سيتم إعادة تشغيل البرنامج.", "SafeLink",
                        CustomMessageBox.MessageBoxType.Information,
                        CustomMessageBox.MessageBoxButtons.OK, this);

                    System.Diagnostics.Debug.WriteLine("✅ Factory reset completed successfully");

                    // Restart application
                    RestartApplication();
                }
                catch (Exception ex)
                {
                    CustomMessageBox.Show($"فشل في إعادة ضبط المصنع: {ex.Message}", "خطأ",
                        CustomMessageBox.MessageBoxType.Error,
                        CustomMessageBox.MessageBoxButtons.OK, this);

                    System.Diagnostics.Debug.WriteLine($"❌ Failed to perform factory reset: {ex.Message}");
                }
            }
        }

        private void ClearTemporaryData()
        {
            try
            {
                // Clear login credentials completely
                Properties.Settings.Default.RememberLogin = false;
                Properties.Settings.Default.SavedUsername = "";
                Properties.Settings.Default.SavedPassword = "";

                // Clear temporary files
                var tempPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "SafeLink", "Temp");
                if (Directory.Exists(tempPath))
                {
                    Directory.Delete(tempPath, true);
                }

                // Clear cached data
                var cachePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "SafeLink", "Cache");
                if (Directory.Exists(cachePath))
                {
                    Directory.Delete(cachePath, true);
                }

                // Save cleared settings
                Properties.Settings.Default.Save();

                System.Diagnostics.Debug.WriteLine("✅ Temporary data cleared successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing temporary data: {ex.Message}");
                throw;
            }
        }

        private void PerformFactoryReset()
        {
            try
            {
                // Clear all login credentials first
                Properties.Settings.Default.RememberLogin = false;
                Properties.Settings.Default.SavedUsername = "";
                Properties.Settings.Default.SavedPassword = "";

                // Reset all settings to default
                Properties.Settings.Default.Reset();

                // Clear all application data
                var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "SafeLink");
                if (Directory.Exists(appDataPath))
                {
                    Directory.Delete(appDataPath, true);
                }

                // Clear roaming data
                var roamingPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SafeLink");
                if (Directory.Exists(roamingPath))
                {
                    Directory.Delete(roamingPath, true);
                }

                // Clear database
                var dbPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "SafeLink", "safelink.db");
                if (File.Exists(dbPath))
                {
                    File.Delete(dbPath);
                }

                // Reset language to default Arabic
                SafeLink.Core.LanguageManager.SetLanguage("ar");
                Properties.Settings.Default.Language = "ar";

                // Ensure all login data is cleared again
                Properties.Settings.Default.RememberLogin = false;
                Properties.Settings.Default.SavedUsername = "";
                Properties.Settings.Default.SavedPassword = "";

                // Save reset settings
                Properties.Settings.Default.Save();

                System.Diagnostics.Debug.WriteLine("✅ Factory reset completed successfully - all data cleared");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error performing factory reset: {ex.Message}");
                throw;
            }
        }

        private void RestartApplication()
        {
            try
            {
                // Get current executable path
                var exePath = Process.GetCurrentProcess().MainModule.FileName;

                // Start new instance
                Process.Start(exePath);

                // Close current application
                Application.Current.Shutdown();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error restarting application: {ex.Message}");
                // If restart fails, just close the application
                Application.Current.Shutdown();
            }
        }
    }
}
