using System;
using System.Collections.Generic;

namespace SafeLink.Models
{
    public class Message
    {
        public int Id { get; set; }
        public int FromUserId { get; set; }
        public int ToUserId { get; set; }
        public string Content { get; set; }
        public MessageType Type { get; set; }
        public string FilePath { get; set; }
        public DateTime Timestamp { get; set; }
        public bool IsRead { get; set; }
        public string SenderName { get; set; }
        public bool IsEncrypted { get; set; }
        public MessageStatus Status { get; set; } = MessageStatus.Sending;
        public DateTime? DeliveredAt { get; set; }
        public DateTime? ReadAt { get; set; }
        public string MessageId { get; set; } = Guid.NewGuid().ToString();

        // Attachments support
        public virtual List<Attachment> Attachments { get; set; } = new List<Attachment>();
        public bool HasAttachments => Attachments?.Count > 0;
    }

    public enum MessageType
    {
        Text = 0,
        File = 1,
        Image = 2,
        System = 3
    }

    public enum MessageStatus
    {
        Sending = 0,    // جاري الإرسال
        Sent = 1,       // تم الإرسال
        Delivered = 2,  // تم التسليم
        Read = 3,       // تم القراءة
        Failed = 4      // فشل الإرسال
    }

    public class ChatSession
    {
        public int Id { get; set; }
        public int User1Id { get; set; }
        public int User2Id { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime LastActivity { get; set; }
        public bool IsActive { get; set; }
    }

    public class ProblemReport
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string ScreenshotPath { get; set; }
        public string Description { get; set; }
        public string UserAgent { get; set; }
        public DateTime Timestamp { get; set; }
        public ReportStatus Status { get; set; }
    }

    public enum ReportStatus
    {
        Pending = 0,
        InProgress = 1,
        Resolved = 2,
        Closed = 3
    }

    public class SystemLog
    {
        public int Id { get; set; }
        public int? UserId { get; set; }
        public string Action { get; set; }
        public string Details { get; set; }
        public string IpAddress { get; set; }
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
    }

    public enum LogLevel
    {
        Info = 0,
        Warning = 1,
        Error = 2,
        Critical = 3
    }
}
