using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;

namespace SafeLink.Controls
{
    public partial class FeatherIcon : UserControl
    {
        public static readonly DependencyProperty IconNameProperty =
            DependencyProperty.Register("IconName", typeof(string), typeof(FeatherIcon),
                new PropertyMetadata(string.Empty, OnIconNameChanged));

        public static readonly DependencyProperty IconColorProperty =
            DependencyProperty.Register("IconColor", typeof(Brush), typeof(FeatherIcon),
                new PropertyMetadata(Brushes.White));

        public string IconName
        {
            get { return (string)GetValue(IconNameProperty); }
            set { SetValue(IconNameProperty, value); }
        }

        public Brush IconColor
        {
            get { return (Brush)GetValue(IconColorProperty); }
            set { SetValue(IconColorProperty, value); }
        }

        public FeatherIcon()
        {
            InitializeComponent();
        }

        private static void OnIconNameChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is FeatherIcon icon)
            {
                icon.UpdateIcon();
            }
        }

        private void UpdateIcon()
        {
            try
            {
                if (IconCanvas == null) return;

                IconCanvas.Children.Clear();

                var iconPath = GetIconPath(IconName);
                if (iconPath != null)
                {
                    var path = new Path
                    {
                        Data = iconPath,
                        Stroke = IconColor ?? Brushes.White,
                        StrokeThickness = 2,
                        Fill = Brushes.Transparent
                    };

                    IconCanvas.Children.Add(path);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating icon: {ex.Message}");
            }
        }

        private Geometry GetIconPath(string iconName)
        {
            return iconName?.ToLower() switch
            {
                "settings" => Geometry.Parse("M12 15a3 3 0 1 0 0-6 3 3 0 0 0 0 6z M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"),
                "alert-triangle" => Geometry.Parse("M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z M12 9v4 M12 17h.01"),
                "activity" => Geometry.Parse("M22 12h-4l-3 9L9 3l-3 9H2"),
                "log-out" => Geometry.Parse("M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4 M16 17l5-5-5-5 M21 12H9"),
                "warning" => Geometry.Parse("M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z M12 9v4 M12 17h.01"),
                "search" => Geometry.Parse("M11 19c4.4 0 8-3.6 8-8s-3.6-8-8-8-8 3.6-8 8 3.6 8 8 8z M21 21l-4.35-4.35"),
                "logout" => Geometry.Parse("M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4 M16 17l5-5-5-5 M21 12H9"),
                "send" => Geometry.Parse("M22 2L11 13 M22 2l-7 20-4-9-9-4 20-7z"),
                "users" => Geometry.Parse("M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2 M23 21v-2a4 4 0 0 0-3-3.87 M16 3.13a4 4 0 0 1 0 7.75 M13 7a4 4 0 1 1-8 0 4 4 0 0 1 8 0z"),
                "message-circle" => Geometry.Parse("M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"),
                "eye" => Geometry.Parse("M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z M12 9a3 3 0 1 1 0 6 3 3 0 0 1 0-6z"),
                "eye-off" => Geometry.Parse("M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24 M1 1l22 22"),
                "lock" => Geometry.Parse("M19 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2z M7 11V7a5 5 0 0 1 10 0v4"),
                "shield" => Geometry.Parse("M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"),
                "power" => Geometry.Parse("M18.36 6.64a9 9 0 1 1-12.73 0 M12 2v10"),
                "minus" => Geometry.Parse("M5 12h14"),
                "maximize-2" => Geometry.Parse("M15 3h6v6 M9 21H3v-6 M21 3l-7 7 M3 21l7-7"),
                "x" => Geometry.Parse("M18 6L6 18 M6 6l12 12"),
                "check" => Geometry.Parse("M20 6L9 17l-5-5"),
                "paperclip" => Geometry.Parse("M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"),
                _ => null
            };
        }
    }
}
