<Window x:Class="SafeLink.Views.CreateUserDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="Create New User - SafeLink Developer"
        Height="500" Width="450"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        Icon="pack://application:,,,/Icons/SafeLink.ico">

    <Window.Resources>
        <!-- Professional Security Gradient -->
        <LinearGradientBrush x:Key="SecurityGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#0D1421" Offset="0"/>
            <GradientStop Color="#1A2332" Offset="0.3"/>
            <GradientStop Color="#243447" Offset="0.7"/>
            <GradientStop Color="#2C4A5C" Offset="1"/>
        </LinearGradientBrush>

        <!-- Accent Colors -->
        <SolidColorBrush x:Key="PrimaryAccent" Color="#00E5FF"/>
        <SolidColorBrush x:Key="SecondaryAccent" Color="#1DE9B6"/>
        <SolidColorBrush x:Key="WarningAccent" Color="#FFB74D"/>
        <SolidColorBrush x:Key="DangerAccent" Color="#FF5252"/>
    </Window.Resources>

    <Border Background="{StaticResource SecurityGradient}" CornerRadius="15"
           BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="2">
        <Border.Effect>
            <DropShadowEffect Color="#00E5FF" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
        </Border.Effect>

        <Grid Margin="25">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryAccent}"
                   Padding="20,15" CornerRadius="10" Margin="0,0,0,25">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <controls:FeatherIcon IconName="user-plus" IconColor="#0D1421"
                                        Width="24" Height="24" Margin="0,0,12,0"/>
                    <TextBlock Text="Create New User" FontSize="18" FontWeight="SemiBold"
                              Foreground="#0D1421" VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Form Content -->
            <StackPanel Grid.Row="1">
                <!-- Username -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="Username (English)" Foreground="{StaticResource PrimaryAccent}"
                              FontWeight="SemiBold" Margin="5,0,5,5" FontSize="12"/>
                    <TextBox x:Name="UsernameTextBox" Background="#1A1A2E" Foreground="White"
                            BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1"
                            Padding="12,8" Height="35" FontSize="13"/>
                </StackPanel>

                <!-- Arabic Username -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="Arabic Username (Optional)" Foreground="{StaticResource SecondaryAccent}"
                              FontWeight="SemiBold" Margin="5,0,5,5" FontSize="12"/>
                    <TextBox x:Name="ArabicUsernameTextBox" Background="#1A1A2E" Foreground="White"
                            BorderBrush="{StaticResource SecondaryAccent}" BorderThickness="1"
                            Padding="12,8" Height="35" FontSize="13" FlowDirection="RightToLeft"/>
                </StackPanel>

                <!-- Password -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="Password" Foreground="{StaticResource WarningAccent}"
                              FontWeight="SemiBold" Margin="5,0,5,5" FontSize="12"/>
                    <PasswordBox x:Name="PasswordBox" Background="#1A1A2E" Foreground="White"
                                BorderBrush="{StaticResource WarningAccent}" BorderThickness="1"
                                Padding="12,8" Height="35" FontSize="13"/>
                </StackPanel>

                <!-- Confirm Password -->
                <StackPanel Margin="0,0,0,15">
                    <TextBlock Text="Confirm Password" Foreground="{StaticResource WarningAccent}"
                              FontWeight="SemiBold" Margin="5,0,5,5" FontSize="12"/>
                    <PasswordBox x:Name="ConfirmPasswordBox" Background="#1A1A2E" Foreground="White"
                                BorderBrush="{StaticResource WarningAccent}" BorderThickness="1"
                                Padding="12,8" Height="35" FontSize="13"/>
                </StackPanel>

                <!-- Role Selection -->
                <StackPanel>
                    <TextBlock Text="User Role" Foreground="{StaticResource PrimaryAccent}"
                              FontWeight="SemiBold" Margin="5,0,5,5" FontSize="12"/>
                    <ComboBox x:Name="RoleComboBox" Background="#1A1A2E" Foreground="White"
                             BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1"
                             Height="35" FontSize="13" SelectedIndex="0">
                        <ComboBoxItem Content="User" Tag="User"/>
                        <ComboBoxItem Content="Moderator" Tag="Moderator"/>
                        <ComboBoxItem Content="Admin" Tag="Admin"/>
                        <ComboBoxItem Content="Developer" Tag="Developer"/>
                    </ComboBox>
                </StackPanel>
            </StackPanel>

            <!-- Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,25,0,0">
                <Button x:Name="CreateButton" Background="{StaticResource SecondaryAccent}" 
                       Foreground="#0D1421" FontWeight="SemiBold" BorderThickness="0" 
                       Padding="20,10" Margin="10" Width="120" Height="40" Click="CreateButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="check" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Create" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
                
                <Button x:Name="CancelButton" Background="{StaticResource DangerAccent}" 
                       Foreground="White" FontWeight="SemiBold" BorderThickness="0" 
                       Padding="20,10" Margin="10" Width="120" Height="40" Click="CancelButton_Click">
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="x" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Cancel" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
