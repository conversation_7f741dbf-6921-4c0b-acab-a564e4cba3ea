Write-Host "SafeLink Network Test" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

Write-Host "Checking UDP ports..." -ForegroundColor Yellow
Get-NetUDPEndpoint | Where-Object {$_.LocalPort -ge 8888 -and $_.LocalPort -le 8895} | ForEach-Object {
    Write-Host "UDP Port $($_.LocalPort) is ACTIVE" -ForegroundColor Green
}

Write-Host "Checking TCP ports..." -ForegroundColor Yellow
Get-NetTCPConnection | Where-Object {$_.LocalPort -ge 8898 -and $_.LocalPort -le 8905} | ForEach-Object {
    Write-Host "TCP Port $($_.LocalPort) is ACTIVE" -ForegroundColor Green
}

Write-Host "Test completed!" -ForegroundColor Cyan
