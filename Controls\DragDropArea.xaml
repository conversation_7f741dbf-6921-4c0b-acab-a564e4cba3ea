<UserControl x:Class="SafeLink.Controls.DragDropArea"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controls="clr-namespace:SafeLink.Controls"
             AllowDrop="True"
             Drop="DragDropArea_Drop"
             DragEnter="DragDropArea_DragEnter"
             DragLeave="DragDropArea_DragLeave"
             DragOver="DragDropArea_DragOver">
    
    <UserControl.Resources>
        <!-- Drop Shadow Effect -->
        <DropShadowEffect x:Key="DropShadowEffect" Color="Black" BlurRadius="10" 
                         ShadowDepth="3" Opacity="0.3"/>
        
        <!-- Animation Storyboards -->
        <Storyboard x:Key="DragEnterAnimation">
            <DoubleAnimation Storyboard.TargetName="MainBorder" 
                           Storyboard.TargetProperty="Opacity"
                           To="1" Duration="0:0:0.2"/>
            <ColorAnimation Storyboard.TargetName="MainBorder" 
                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                          To="#00D4AA" Duration="0:0:0.2"/>
            <ColorAnimation Storyboard.TargetName="MainBorder" 
                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                          To="#1000D4AA" Duration="0:0:0.2"/>
        </Storyboard>
        
        <Storyboard x:Key="DragLeaveAnimation">
            <DoubleAnimation Storyboard.TargetName="MainBorder" 
                           Storyboard.TargetProperty="Opacity"
                           To="0.7" Duration="0:0:0.2"/>
            <ColorAnimation Storyboard.TargetName="MainBorder" 
                          Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)"
                          To="#404040" Duration="0:0:0.2"/>
            <ColorAnimation Storyboard.TargetName="MainBorder" 
                          Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                          To="#10FFFFFF" Duration="0:0:0.2"/>
        </Storyboard>
        
        <Storyboard x:Key="PulseAnimation" RepeatBehavior="Forever" AutoReverse="True">
            <DoubleAnimation Storyboard.TargetName="UploadIcon" 
                           Storyboard.TargetProperty="Opacity"
                           From="0.5" To="1" Duration="0:0:1"/>
        </Storyboard>
    </UserControl.Resources>
    
    <Border x:Name="MainBorder"
            Background="#10FFFFFF"
            BorderBrush="#404040"
            BorderThickness="2"
            CornerRadius="12"
            Opacity="0.7"
            MinHeight="120"
            Effect="{StaticResource DropShadowEffect}">
        
        <Grid>
            <!-- Default State -->
            <StackPanel x:Name="DefaultContent" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       Visibility="Visible">
                
                <!-- Upload Icon -->
                <Border x:Name="UploadIcon"
                       Width="60" Height="60"
                       Background="#2C3E50"
                       CornerRadius="30"
                       Margin="0,0,0,15">
                    <Border.Effect>
                        <DropShadowEffect Color="#00D4AA" BlurRadius="8" ShadowDepth="0" Opacity="0.3"/>
                    </Border.Effect>
                    
                    <Grid>
                        <!-- Cloud Upload Icon -->
                        <Viewbox Width="30" Height="30">
                            <Canvas Width="24" Height="24">
                                <!-- Cloud shape -->
                                <Path Data="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96z"
                                      Fill="#00D4AA" Canvas.Left="0" Canvas.Top="2"/>
                                <!-- Arrow up -->
                                <Path Data="M14 13v4h-4v-4H7l5-5 5 5h-3z"
                                      Fill="White" Canvas.Left="0" Canvas.Top="6"/>
                            </Canvas>
                        </Viewbox>
                    </Grid>
                </Border>
                
                <!-- Text Content -->
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock x:Name="MainText"
                              Text="اسحب الملفات هنا"
                              FontSize="16"
                              FontWeight="SemiBold"
                              Foreground="#00D4AA"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,5"/>
                    
                    <TextBlock x:Name="SubText"
                              Text="أو اضغط لاختيار الملفات"
                              FontSize="12"
                              Foreground="#B0BEC5"
                              HorizontalAlignment="Center"
                              Margin="0,0,0,10"/>
                    
                    <TextBlock x:Name="SupportedFormats"
                              Text="الصور، المستندات، الفيديو، الصوت (حتى 50 ميجابايت)"
                              FontSize="10"
                              Foreground="#7F8C8D"
                              HorizontalAlignment="Center"
                              TextWrapping="Wrap"
                              MaxWidth="250"/>
                </StackPanel>
            </StackPanel>
            
            <!-- Drag Over State -->
            <StackPanel x:Name="DragOverContent" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       Visibility="Collapsed">
                
                <Border Width="80" Height="80"
                       Background="#00D4AA"
                       CornerRadius="40"
                       Margin="0,0,0,15">
                    <Border.Effect>
                        <DropShadowEffect Color="#00D4AA" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                    </Border.Effect>
                    
                    <Viewbox Width="40" Height="40">
                        <Canvas Width="24" Height="24">
                            <!-- Download arrow -->
                            <Path Data="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"
                                  Fill="White"/>
                        </Canvas>
                    </Viewbox>
                </Border>
                
                <TextBlock Text="أفلت الملفات هنا"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="#00D4AA"
                          HorizontalAlignment="Center"/>
            </StackPanel>
            
            <!-- Processing State -->
            <StackPanel x:Name="ProcessingContent" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       Visibility="Collapsed">
                
                <!-- Loading Spinner -->
                <Border Width="50" Height="50"
                       Margin="0,0,0,15">
                    <Border.RenderTransform>
                        <RotateTransform x:Name="SpinnerRotation"/>
                    </Border.RenderTransform>
                    
                    <Border.Triggers>
                        <EventTrigger RoutedEvent="Loaded">
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever">
                                    <DoubleAnimation Storyboard.TargetName="SpinnerRotation"
                                                   Storyboard.TargetProperty="Angle"
                                                   From="0" To="360" Duration="0:0:1"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Border.Triggers>
                    
                    <Ellipse Stroke="#00D4AA" StrokeThickness="4"
                            StrokeDashArray="15,5" Fill="Transparent"/>
                </Border>
                
                <TextBlock x:Name="ProcessingText"
                          Text="جاري معالجة الملفات..."
                          FontSize="14"
                          Foreground="#00D4AA"
                          HorizontalAlignment="Center"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
