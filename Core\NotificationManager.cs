using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using SafeLink.Controls;
using SafeLink.Models;

namespace SafeLink.Core
{
    public class NotificationManager
    {
        private static NotificationManager _instance;
        private readonly List<ConnectionNotification> _activeNotifications;
        private Panel _notificationContainer;

        public static NotificationManager Instance => _instance ??= new NotificationManager();

        private NotificationManager()
        {
            _activeNotifications = new List<ConnectionNotification>();
        }

        public void Initialize(Panel container)
        {
            _notificationContainer = container;
        }

        public void ShowConnectionNotification(User user, bool isConnected)
        {
            if (_notificationContainer == null) return;

            try
            {
                // Remove any existing notification for this user
                RemoveUserNotification(user.Username);

                // Create new notification
                var notification = new ConnectionNotification();
                
                // Position notification (from right side, stacked vertically)
                var topMargin = _activeNotifications.Count * 100; // 100px spacing between notifications
                
                notification.HorizontalAlignment = HorizontalAlignment.Right;
                notification.VerticalAlignment = VerticalAlignment.Top;
                notification.Margin = new Thickness(0, topMargin + 20, 20, 0);

                // Add to container and track
                _notificationContainer.Children.Add(notification);
                _activeNotifications.Add(notification);

                // Show the notification
                notification.ShowConnectionNotification(user, isConnected);

                // Auto-remove after animation completes
                var removeTimer = new System.Windows.Threading.DispatcherTimer
                {
                    Interval = TimeSpan.FromSeconds(5)
                };
                removeTimer.Tick += (s, e) =>
                {
                    removeTimer.Stop();
                    RemoveNotification(notification);
                };
                removeTimer.Start();

                // Reposition existing notifications
                RepositionNotifications();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing connection notification: {ex.Message}");
            }
        }

        private void RemoveUserNotification(string username)
        {
            var existingNotifications = _activeNotifications.ToList();
            foreach (var notification in existingNotifications)
            {
                // Check if this notification is for the same user
                if (notification.UsernameText?.Text == username)
                {
                    RemoveNotification(notification);
                }
            }
        }

        private void RemoveNotification(ConnectionNotification notification)
        {
            try
            {
                if (_activeNotifications.Contains(notification))
                {
                    _activeNotifications.Remove(notification);
                    _notificationContainer?.Children.Remove(notification);
                    notification.ForceHide();
                    
                    // Reposition remaining notifications
                    RepositionNotifications();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error removing notification: {ex.Message}");
            }
        }

        private void RepositionNotifications()
        {
            try
            {
                for (int i = 0; i < _activeNotifications.Count; i++)
                {
                    var notification = _activeNotifications[i];
                    var newTopMargin = i * 100 + 20;
                    
                    // Animate to new position
                    var animation = new System.Windows.Media.Animation.ThicknessAnimation
                    {
                        To = new Thickness(0, newTopMargin, 20, 0),
                        Duration = TimeSpan.FromMilliseconds(300)
                    };
                    
                    notification.BeginAnimation(FrameworkElement.MarginProperty, animation);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error repositioning notifications: {ex.Message}");
            }
        }

        public void ClearAllNotifications()
        {
            try
            {
                var notifications = _activeNotifications.ToList();
                foreach (var notification in notifications)
                {
                    RemoveNotification(notification);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing notifications: {ex.Message}");
            }
        }

        public void ShowDisconnectionNotification(User user)
        {
            ShowConnectionNotification(user, false);
        }

        public void ShowReconnectionNotification(User user)
        {
            ShowConnectionNotification(user, true);
        }
    }
}
