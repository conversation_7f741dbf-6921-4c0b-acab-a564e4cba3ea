<Window x:Class="SafeLink.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:SafeLink.Views"
        xmlns:converters="clr-namespace:SafeLink.Converters"
        xmlns:utils="clr-namespace:SafeLink.Utils"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink - Professional Security Platform"
        Height="720" Width="1024"
        MinHeight="600" MinWidth="800"
        Background="#1A1A2E"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize" WindowState="Normal" KeyDown="MainWindow_KeyDown"
        Closing="MainWindow_Closing" StateChanged="MainWindow_StateChanged"
        Icon="pack://application:,,,/Icons/SafeLink.ico">

    <WindowChrome.WindowChrome>
        <WindowChrome CaptionHeight="30"
                     ResizeBorderThickness="5"
                     GlassFrameThickness="0"
                     UseAeroCaptionButtons="True"/>
    </WindowChrome.WindowChrome>

    <Window.Resources>
        <!-- Converters -->
        <converters:RoleToColorConverter x:Key="RoleToColorConverter"/>
        <converters:RoleToTextConverter x:Key="RoleToTextConverter"/>
        <local:RoleToIconConverter x:Key="RoleToIconConverter"/>
        <local:RoleToArabicConverter x:Key="RoleToArabicConverter"/>
        <local:RoleToIconColorConverter x:Key="RoleToIconColorConverter"/>
    </Window.Resources>

    <Grid>
        <!-- Professional Title Bar -->
        <Border Height="30" VerticalAlignment="Top" Panel.ZIndex="1000">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="#0D1421" Offset="0"/>
                    <GradientStop Color="#1A2332" Offset="0.5"/>
                    <GradientStop Color="#243447" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- App Icon and Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="12,0,0,0">
                    <controls:ShieldIcon Width="16" Height="19"
                                       ShieldColor="White"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"
                                       Margin="0,0,8,0"/>
                    <TextBlock Text="SafeLink - Professional Security Platform"
                              Foreground="White" FontSize="12" FontWeight="Medium" VerticalAlignment="Center"/>
                </StackPanel>

                <!-- Window Controls -->
                <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button x:Name="MinimizeButton" Background="Transparent" BorderThickness="0"
                           Width="45" Height="30" Click="MinimizeButton_Click"
                           WindowChrome.IsHitTestVisibleInChrome="True">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#30FFFFFF"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <controls:FeatherIcon IconName="minus" IconColor="#B0BEC5" Width="14" Height="14"/>
                    </Button>
                    <Button x:Name="MaximizeButton" Background="Transparent" BorderThickness="0"
                           Width="45" Height="30" Click="MaximizeButton_Click"
                           WindowChrome.IsHitTestVisibleInChrome="True">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="Transparent"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#30FFFFFF"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                        <controls:FeatherIcon IconName="maximize-2" IconColor="#B0BEC5" Width="14" Height="14"/>
                    </Button>

                    <!-- Close Button (copied from LoginWindow) -->
                    <Button x:Name="CloseButton"
                           Background="Transparent"
                           BorderThickness="0"
                           Width="45" Height="30"
                           HorizontalAlignment="Center"
                           Cursor="Hand"
                           Click="CloseButton_Click"
                           MouseEnter="CloseButton_MouseEnter"
                           MouseLeave="CloseButton_MouseLeave"
                           WindowChrome.IsHitTestVisibleInChrome="True">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                   CornerRadius="8">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#30FF0000"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                        <controls:FeatherIcon x:Name="CloseIcon"
                                            IconName="x"
                                            IconColor="White"
                                            Width="16" Height="16">
                            <controls:FeatherIcon.Effect>
                                <DropShadowEffect Color="White" BlurRadius="4" ShadowDepth="0" Opacity="0.6"/>
                            </controls:FeatherIcon.Effect>
                        </controls:FeatherIcon>
                    </Button>

                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content with Top Margin -->
        <Grid Margin="0,30,0,0">
            <!-- Professional Loading Screen -->
            <Grid x:Name="WelcomeScreen" Visibility="Visible">
                <!-- Professional Security Gradient -->
                <Grid.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#0D1421" Offset="0"/>
                        <GradientStop Color="#1A2332" Offset="0.3"/>
                        <GradientStop Color="#243447" Offset="0.7"/>
                        <GradientStop Color="#2C4A5C" Offset="1"/>
                    </LinearGradientBrush>
                </Grid.Background>

                <!-- Background Glow Effect -->
                <Ellipse Width="800" Height="800"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Opacity="0.3">
                    <Ellipse.Fill>
                        <RadialGradientBrush Center="0.5,0.5" RadiusX="0.6" RadiusY="0.6">
                            <GradientStop Color="#3000E5FF" Offset="0"/>
                            <GradientStop Color="#1500E5FF" Offset="0.5"/>
                            <GradientStop Color="#0000E5FF" Offset="1"/>
                        </RadialGradientBrush>
                    </Ellipse.Fill>
                </Ellipse>

                <!-- Geometric Network Canvas -->
                <Canvas x:Name="NetworkCanvas" ClipToBounds="True" Opacity="0.4">
                    <Canvas.Resources>
                        <Storyboard x:Key="NetworkAnimation" RepeatBehavior="Forever">
                            <!-- Professional network animations will be added programmatically -->
                        </Storyboard>
                    </Canvas.Resources>
                </Canvas>

                <!-- Professional Content -->
                <Grid VerticalAlignment="Center" HorizontalAlignment="Center" Margin="40">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Professional Logo Section -->
                    <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,40">
                        <!-- Custom Shield Icon -->
                        <controls:ShieldIcon Width="120" Height="144"
                                           ShieldColor="White"
                                           HorizontalAlignment="Center"
                                           VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Brand Identity -->
                    <StackPanel Grid.Row="1" HorizontalAlignment="Center" Margin="0,0,0,30">
                        <TextBlock x:Name="AppName"
                                  Text="SAFELINK"
                                  FontSize="48"
                                  FontWeight="ExtraLight"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,10"
                                  Foreground="White"/>
                        <TextBlock x:Name="Tagline"
                                  Text="SECURE COMMUNICATION PLATFORM"
                                  FontSize="14"
                                  Foreground="#B0BEC5"
                                  HorizontalAlignment="Center"
                                  FontWeight="Normal"
                                  Opacity="0.8"/>
                    </StackPanel>

                    <!-- Security Badge -->
                    <Border Grid.Row="2"
                           Background="#15FFFFFF"
                           BorderBrush="#1DE9B6"
                           BorderThickness="1"
                           CornerRadius="15"
                           Padding="20,12"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,30">
                        <StackPanel Orientation="Horizontal">
                            <controls:FeatherIcon IconName="lock"
                                                IconColor="#1DE9B6"
                                                Width="16" Height="16"
                                                Margin="0,0,8,0"/>
                            <TextBlock Text="ENTERPRISE GRADE SECURITY"
                                      FontSize="11"
                                      FontWeight="Medium"
                                      Foreground="#1DE9B6"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Status Section -->
                    <StackPanel Grid.Row="3" HorizontalAlignment="Center" Margin="0,0,0,25">
                        <TextBlock x:Name="SecurityStatus"
                                  Text="Initializing Security Environment..."
                                  FontSize="13"
                                  Foreground="#00E5FF"
                                  HorizontalAlignment="Center"
                                  FontWeight="Medium"
                                  Margin="0,0,0,15"/>

                        <!-- Professional Progress Bar -->
                        <Grid Width="450" Height="4">
                            <Border Background="#20FFFFFF" CornerRadius="2"/>
                            <Border x:Name="ProgressFill"
                                   Background="#00E5FF"
                                   CornerRadius="2"
                                   Width="0"
                                   HorizontalAlignment="Left">
                                <Border.Effect>
                                    <DropShadowEffect Color="#00E5FF" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
                                </Border.Effect>
                            </Border>
                        </Grid>
                    </StackPanel>

                    <!-- Connection Status -->
                    <Border Grid.Row="4"
                           Background="#10FFFFFF"
                           BorderBrush="#00E5FF"
                           BorderThickness="1"
                           CornerRadius="10"
                           Padding="15,8"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,30">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse x:Name="SecurityIndicator"
                                    Width="8" Height="8"
                                    Fill="#00E5FF"
                                    Margin="0,0,8,0">
                                <Ellipse.Effect>
                                    <DropShadowEffect Color="#00E5FF" BlurRadius="6" ShadowDepth="0" Opacity="0.8"/>
                                </Ellipse.Effect>
                            </Ellipse>
                            <TextBlock Text="SECURE CONNECTION ACTIVE"
                                      FontSize="10"
                                      FontWeight="Medium"
                                      Foreground="#00E5FF"
                                      VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Footer -->
                    <StackPanel Grid.Row="5" HorizontalAlignment="Center">
                        <TextBlock Text="SafeLink v1.0.0 | Enterprise Edition"
                                  FontSize="10"
                                  Foreground="#60FFFFFF"
                                  HorizontalAlignment="Center"
                                  Margin="0,0,0,5"/>
                        <TextBlock Text="© 2025 Drikon Technologies. All rights reserved."
                                  FontSize="9"
                                  Foreground="#40FFFFFF"
                                  HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>

        <!-- Login Screen -->
        <Grid x:Name="LoginScreen" Visibility="Collapsed">
            <!-- Network Background for Login -->
            <Grid.Background>
                <RadialGradientBrush>
                    <GradientStop Color="#0A0E1A" Offset="0"/>
                    <GradientStop Color="#1A1A2E" Offset="0.6"/>
                    <GradientStop Color="#16213E" Offset="1"/>
                </RadialGradientBrush>
            </Grid.Background>

            <!-- Network Canvas for Login -->
            <Canvas x:Name="LoginNetworkCanvas" ClipToBounds="True" Opacity="0.3"/>

            <!-- Depth Field Overlay for Login -->
            <Border>
                <Border.Background>
                    <RadialGradientBrush>
                        <GradientStop Color="Transparent" Offset="0"/>
                        <GradientStop Color="Transparent" Offset="0.4"/>
                        <GradientStop Color="#1A1A2E" Offset="0.9"/>
                        <GradientStop Color="#16213E" Offset="1"/>
                    </RadialGradientBrush>
                </Border.Background>
            </Border>

            <Border Background="#2C3E50" BorderBrush="#00D4AA" BorderThickness="2"
                   CornerRadius="15" Padding="25" HorizontalAlignment="Center" VerticalAlignment="Center"
                   Width="380" MaxHeight="500">
                <Border.Effect>
                    <DropShadowEffect Color="#00D4AA" BlurRadius="20" ShadowDepth="0" Opacity="0.5"/>
                </Border.Effect>

                <StackPanel>
                    <!-- Header with Shield Icon -->
                    <Border Background="#16213E" CornerRadius="10,10,0,0" Margin="-25,-25,-25,0" Padding="20,15">
                        <StackPanel>
                            <!-- Shield Icon -->
                            <controls:ShieldIcon Width="40" Height="48"
                                               ShieldColor="#00D4AA"
                                               HorizontalAlignment="Center"
                                               Margin="0,0,0,10"/>

                            <TextBlock Text="SECURE ACCESS" FontSize="18" FontWeight="Bold"
                                      Foreground="#00D4AA" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                            <TextBlock Text="SafeLink Authentication Portal" FontSize="11" FontWeight="Medium"
                                      Foreground="#ECF0F1" HorizontalAlignment="Center" Opacity="0.8"/>
                        </StackPanel>
                    </Border>

                    <StackPanel Margin="0,20,0,0">
                        <!-- Username Field -->
                        <Label Content="👤 Username:" Foreground="#00D4AA" FontWeight="SemiBold"
                               Margin="5,0,5,3" FontSize="12"/>
                        <TextBox x:Name="UsernameTextBox" Background="#1A1A2E" Foreground="#ECF0F1"
                                BorderBrush="#00D4AA" BorderThickness="1" Padding="12,8" Margin="5,0,5,10"
                                FontSize="13" Height="36" KeyDown="UsernameTextBox_KeyDown"/>

                        <!-- Password Field -->
                        <Label Content="🔐 Password:" Foreground="#00D4AA" FontWeight="SemiBold"
                               Margin="5,5,5,3" FontSize="12"/>
                        <Grid Margin="5,0,5,15">
                            <PasswordBox x:Name="PasswordBox" Background="#1A1A2E" Foreground="#ECF0F1"
                                        BorderBrush="#00D4AA" BorderThickness="1" Padding="12,8,55,8"
                                        FontSize="13" Height="36" KeyDown="PasswordBox_KeyDown"/>
                            <TextBox x:Name="PasswordTextBox" Background="#1A1A2E" Foreground="#ECF0F1"
                                    BorderBrush="#00D4AA" BorderThickness="1" Padding="12,8,55,8"
                                    FontSize="13" Height="36" KeyDown="PasswordTextBox_KeyDown"
                                    Visibility="Collapsed"/>
                            <Button x:Name="TogglePasswordButton" Background="Transparent" BorderThickness="0"
                                   HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,8,0"
                                   Width="45" Height="45" Click="TogglePasswordButton_Click"
                                   Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Background" Value="Transparent"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}"
                                                           BorderBrush="{TemplateBinding BorderBrush}"
                                                           BorderThickness="{TemplateBinding BorderThickness}"
                                                           Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="RenderTransform">
                                            <Setter.Value>
                                                <ScaleTransform ScaleX="1" ScaleY="1"/>
                                            </Setter.Value>
                                        </Setter>
                                        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Trigger.EnterActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX" To="1.15" Duration="0:0:0.1"/>
                                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY" To="1.15" Duration="0:0:0.1"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </Trigger.EnterActions>
                                                <Trigger.ExitActions>
                                                    <BeginStoryboard>
                                                        <Storyboard>
                                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX" To="1" Duration="0:0:0.1"/>
                                                            <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY" To="1" Duration="0:0:0.1"/>
                                                        </Storyboard>
                                                    </BeginStoryboard>
                                                </Trigger.ExitActions>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                                <controls:FeatherIcon x:Name="EyeIcon" IconName="eye" IconColor="#7F8C8D" Width="24" Height="24"/>
                            </Button>
                        </Grid>

                        <!-- Remember Me Checkbox -->
                        <CheckBox x:Name="RememberMeCheckBox" Content="🔒 Remember my login"
                                 Foreground="#ECF0F1" FontSize="11" Margin="8,0,5,20"
                                 IsChecked="False">
                            <CheckBox.Style>
                                <Style TargetType="CheckBox">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="CheckBox">
                                                <StackPanel Orientation="Horizontal">
                                                    <Border x:Name="CheckBorder" Width="16" Height="16"
                                                           Background="#1A1A2E" BorderBrush="#00D4AA"
                                                           BorderThickness="1" CornerRadius="3" Margin="0,0,8,0">
                                                        <TextBlock x:Name="CheckMark" Text="✓" FontSize="10"
                                                                  Foreground="#00D4AA" HorizontalAlignment="Center"
                                                                  VerticalAlignment="Center" Visibility="Collapsed"/>
                                                    </Border>
                                                    <ContentPresenter/>
                                                </StackPanel>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsChecked" Value="True">
                                                        <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                                        <Setter TargetName="CheckBorder" Property="Background" Value="#00D4AA"/>
                                                        <Setter TargetName="CheckMark" Property="Foreground" Value="#1A1A2E"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </CheckBox.Style>
                        </CheckBox>

                        <!-- Action Buttons -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                            <Button x:Name="LoginButton" Background="#00D4AA" Foreground="#1A1A2E"
                                   FontWeight="Bold" BorderThickness="0" Padding="18,8" Margin="5"
                                   Width="110" Height="36">
                                <StackPanel Orientation="Horizontal">
                                    <controls:FeatherIcon IconName="lock" IconColor="#1A1A2E" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="LOGIN" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                            <Button x:Name="RegisterButton" Background="#E94560" Foreground="#ECF0F1"
                                   FontWeight="Bold" BorderThickness="0" Padding="18,8" Margin="5"
                                   Width="110" Height="36">
                                <StackPanel Orientation="Horizontal">
                                    <controls:FeatherIcon IconName="users" IconColor="#ECF0F1" Width="16" Height="16" Margin="0,0,5,0"/>
                                    <TextBlock Text="REGISTER" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- Device Info -->
                        <Border Background="#16213E" BorderBrush="#00D4AA" BorderThickness="1"
                               CornerRadius="5" Margin="5,20,5,0" Padding="8">
                            <TextBlock x:Name="DeviceIdText" Text="🔧 Device ID: Loading..." FontSize="9"
                                      Foreground="#2C3E50" HorizontalAlignment="Center"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Main Application -->
        <Grid x:Name="MainApplication" Visibility="Collapsed">
            <!-- Professional Background -->
            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#0D1421" Offset="0"/>
                    <GradientStop Color="#1A2332" Offset="0.5"/>
                    <GradientStop Color="#243447" Offset="1"/>
                </LinearGradientBrush>
            </Grid.Background>

            <Grid.RowDefinitions>
                <RowDefinition Height="80"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Notification Container (Top Layer) -->
            <Canvas x:Name="NotificationContainer" Panel.ZIndex="1000" ClipToBounds="False"/>

            <!-- Professional Header -->
            <Border Grid.Row="0" BorderBrush="#00E5FF" BorderThickness="0,0,0,1">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#1A2332" Offset="0"/>
                        <GradientStop Color="#243447" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Grid Margin="20,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- User Info Section -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- Logo -->
                        <controls:ShieldIcon Width="32" Height="38"
                                           ShieldColor="White"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Margin="0,0,15,0"/>

                        <!-- User Details -->
                        <StackPanel VerticalAlignment="Center" MaxWidth="250">
                            <TextBlock Text="SafeLink" FontSize="16" FontWeight="Bold" Foreground="White" Margin="0,0,0,2"/>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock x:Name="CurrentUserText" Text="User" Foreground="#00E5FF" FontWeight="SemiBold" FontSize="12"
                                          TextTrimming="CharacterEllipsis" MaxWidth="120"/>
                                <TextBlock Text=" • " Foreground="#B0BEC5" FontSize="12" Margin="5,0"/>
                                <TextBlock x:Name="UserRoleText" Text="Role" Foreground="#1DE9B6" FontSize="12"
                                          TextTrimming="CharacterEllipsis" MaxWidth="80"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>

                    <!-- Connection Status and Toggle -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                        <!-- Connection Status Display -->
                        <Border x:Name="ConnectionStatusBorder" Background="#10FFFFFF" BorderBrush="#1DE9B6" BorderThickness="1"
                               CornerRadius="20" Padding="15,8" Margin="0,0,15,0" MinWidth="120">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Ellipse x:Name="ConnectionIndicator" Width="10" Height="10" Fill="#1DE9B6" Margin="0,0,8,0">
                                    <Ellipse.Effect>
                                        <DropShadowEffect Color="#1DE9B6" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
                                    </Ellipse.Effect>
                                </Ellipse>
                                <TextBlock x:Name="ConnectionText" Text="متصل" Foreground="#1DE9B6" FontSize="12" FontWeight="SemiBold"/>
                            </StackPanel>
                        </Border>

                        <!-- Professional Toggle Switch -->
                        <Border x:Name="ConnectionToggleSwitch"
                               Width="60" Height="30"
                               CornerRadius="15"
                               Background="#FF6B6B"
                               BorderThickness="2"
                               BorderBrush="#FF5252"
                               Cursor="Hand"
                               MouseLeftButtonUp="ConnectionToggle_Click">
                            <Border.Effect>
                                <DropShadowEffect Color="#FF6B6B" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                            </Border.Effect>

                            <Grid>
                                <!-- Toggle Circle -->
                                <Border x:Name="ToggleCircle"
                                       Width="22" Height="22"
                                       CornerRadius="11"
                                       Background="White"
                                       HorizontalAlignment="Left"
                                       VerticalAlignment="Center"
                                       Margin="4,0,0,0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="Black" BlurRadius="4" ShadowDepth="1" Opacity="0.3"/>
                                    </Border.Effect>

                                    <!-- Icon inside circle -->
                                    <controls:FeatherIcon x:Name="ToggleIcon"
                                                        IconName="wifi-off"
                                                        IconColor="#FF6B6B"
                                                        Width="12" Height="12"
                                                        HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                </Border>

                                <!-- Toggle Text -->
                                <TextBlock x:Name="ToggleText"
                                          Text="OFF"
                                          FontSize="9"
                                          FontWeight="Bold"
                                          Foreground="White"
                                          HorizontalAlignment="Right"
                                          VerticalAlignment="Center"
                                          Margin="0,0,8,0"/>
                            </Grid>
                        </Border>
                    </StackPanel>

                    <!-- Action Buttons -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <!-- Settings Button -->
                        <Button x:Name="SettingsButton" Background="Transparent" BorderThickness="0"
                               Width="40" Height="40" Click="SettingsButton_Click"
                               ToolTip="الإعدادات" Cursor="Hand" Margin="8,0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" CornerRadius="8">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#30FFFFFF"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <controls:FeatherIcon x:Name="SettingsIcon" IconName="settings" IconColor="White" Width="28" Height="28"/>
                        </Button>
                        <!-- Report Button -->
                        <Button x:Name="ReportButton" Background="Transparent" BorderThickness="0"
                               Width="40" Height="40" Click="ReportButton_Click"
                               ToolTip="الإبلاغ عن مشكلة" Cursor="Hand" Margin="8,0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" CornerRadius="8">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#30FFFFFF"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <controls:FeatherIcon x:Name="ReportIcon" IconName="alert-triangle" IconColor="White" Width="28" Height="28"/>
                        </Button>
                        <!-- Diagnostic Button -->
                        <Button x:Name="DiagnosticButton" Background="Transparent" BorderThickness="0"
                               Width="40" Height="40" Click="DiagnosticButton_Click"
                               ToolTip="تشخيص الشبكة" Cursor="Hand" Margin="8,0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" CornerRadius="8">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#30FFFFFF"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <controls:FeatherIcon x:Name="DiagnosticIcon" IconName="activity" IconColor="White" Width="28" Height="28"/>
                        </Button>

                        <!-- Logout Button -->
                        <Button x:Name="LogoutButton" Background="Transparent" BorderThickness="0"
                               Width="40" Height="40" Click="LogoutButton_Click"
                               ToolTip="تسجيل الخروج" Cursor="Hand" Margin="8,0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" CornerRadius="8">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#30FFFFFF"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                            <controls:FeatherIcon x:Name="LogoutIcon" IconName="log-out" IconColor="White" Width="28" Height="28"/>
                        </Button>


                    </StackPanel>
                </Grid>
            </Border>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition x:Name="UserListColumn" Width="380"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- User List -->
                <Border Grid.Column="0" BorderBrush="#00E5FF" BorderThickness="0,0,1,0">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                            <GradientStop Color="#1A2332" Offset="0"/>
                            <GradientStop Color="#243447" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="70"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Professional Header -->
                        <Border Grid.Row="0" BorderBrush="#00E5FF" BorderThickness="0,0,0,1" Padding="20,15">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                    <GradientStop Color="#243447" Offset="0"/>
                                    <GradientStop Color="#2C4A5C" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Grid VerticalAlignment="Center">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,6">
                                    <Border Background="#00E5FF" CornerRadius="8" Width="20" Height="20" Margin="0,0,8,0">
                                        <controls:FeatherIcon IconName="users" IconColor="#0D1421" Width="12" Height="12"
                                                            HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <TextBlock x:Name="ActiveUsersText" Text="ACTIVE USERS" FontSize="14" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>

                                <TextBlock Grid.Row="1" x:Name="UserCountText" Text="● 0 online" Foreground="#1DE9B6" FontSize="11" Margin="28,0,0,0"/>
                            </Grid>
                        </Border>

                        <ListBox x:Name="UserListBox" Grid.Row="1" Background="Transparent" BorderThickness="0"
                                Margin="15" SelectionChanged="UserListBox_SelectionChanged">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="15,12" Margin="0,6"
                                           BorderBrush="#00E5FF" BorderThickness="1">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                                                <GradientStop Color="#20FFFFFF" Offset="0"/>
                                                <GradientStop Color="#10FFFFFF" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>
                                        <Border.Effect>
                                            <DropShadowEffect Color="#00E5FF" BlurRadius="6" ShadowDepth="0" Opacity="0.3"/>
                                        </Border.Effect>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- Role Icon -->
                                            <Border Grid.Column="0" CornerRadius="18"
                                                   Width="36" Height="36" VerticalAlignment="Center" Margin="0,0,12,0">
                                                <Border.Background>
                                                    <SolidColorBrush Color="{Binding Role, Converter={StaticResource RoleToColorConverter}}"/>
                                                </Border.Background>
                                                <Border.Effect>
                                                    <DropShadowEffect BlurRadius="8" ShadowDepth="0" Opacity="0.5">
                                                        <DropShadowEffect.Color>
                                                            <Binding Path="Role" Converter="{StaticResource RoleToColorConverter}"/>
                                                        </DropShadowEffect.Color>
                                                    </DropShadowEffect>
                                                </Border.Effect>
                                                <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center"
                                                          FontSize="18" FontWeight="Bold">
                                                    <TextBlock.Foreground>
                                                        <SolidColorBrush Color="{Binding Role, Converter={StaticResource RoleToIconColorConverter}}"/>
                                                    </TextBlock.Foreground>
                                                    <TextBlock.Effect>
                                                        <DropShadowEffect BlurRadius="8" ShadowDepth="0" Opacity="0.8">
                                                            <DropShadowEffect.Color>
                                                                <Binding Path="Role" Converter="{StaticResource RoleToIconColorConverter}"/>
                                                            </DropShadowEffect.Color>
                                                        </DropShadowEffect>
                                                    </TextBlock.Effect>
                                                    <TextBlock.Text>
                                                        <Binding Path="Role" Converter="{StaticResource RoleToIconConverter}"/>
                                                    </TextBlock.Text>
                                                </TextBlock>
                                            </Border>

                                            <!-- User Info -->
                                            <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                <TextBlock Text="{Binding DisplayName}" FontWeight="SemiBold" FontSize="14">
                                                    <TextBlock.Foreground>
                                                        <SolidColorBrush Color="{Binding Role, Converter={StaticResource RoleToColorConverter}}"/>
                                                    </TextBlock.Foreground>
                                                </TextBlock>
                                                <TextBlock FontSize="11" Foreground="#B0BEC5" Margin="0,2,0,0">
                                                    <TextBlock.Text>
                                                        <Binding Path="Role" Converter="{StaticResource RoleToArabicConverter}"/>
                                                    </TextBlock.Text>
                                                </TextBlock>
                                            </StackPanel>

                                            <!-- Status Indicator -->
                                            <Border Grid.Column="2" Background="#1DE9B6" CornerRadius="8"
                                                   Padding="6,3" VerticalAlignment="Center">
                                                <Border.Effect>
                                                    <DropShadowEffect Color="#1DE9B6" BlurRadius="4" ShadowDepth="0" Opacity="0.6"/>
                                                </Border.Effect>
                                                <StackPanel Orientation="Horizontal">
                                                    <Ellipse Width="6" Height="6" Fill="#0D1421" Margin="0,0,4,0"/>
                                                    <TextBlock Text="متصل" FontSize="9" FontWeight="Medium"
                                                              Foreground="#0D1421"/>
                                                </StackPanel>
                                            </Border>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Grid>
                </Border>

                <!-- Chat Area -->
                <Grid Grid.Column="1">
                    <Grid.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                            <GradientStop Color="#0D1421" Offset="0"/>
                            <GradientStop Color="#1A2332" Offset="0.7"/>
                            <GradientStop Color="#243447" Offset="1"/>
                        </LinearGradientBrush>
                    </Grid.Background>

                    <!-- Welcome Message -->
                    <StackPanel x:Name="WelcomeMessage" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <!-- Professional Logo Area -->
                        <controls:ShieldIcon Width="80" Height="96"
                                           ShieldColor="White"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"
                                           Margin="0,0,0,30"/>

                        <!-- Brand Identity -->
                        <StackPanel HorizontalAlignment="Center" Margin="0,0,0,25">
                            <TextBlock x:Name="WelcomeBrandText" Text="SAFELINK" FontSize="32" FontWeight="ExtraLight"
                                      HorizontalAlignment="Center" Margin="0,0,0,8" Foreground="White"/>
                            <TextBlock x:Name="WelcomeTaglineText" Text="SECURE COMMUNICATION PLATFORM" FontSize="12"
                                      Foreground="#B0BEC5" HorizontalAlignment="Center" FontWeight="Normal" Opacity="0.8"/>
                        </StackPanel>

                        <!-- Status Section -->
                        <Border Background="#15FFFFFF" BorderBrush="#1DE9B6" BorderThickness="1"
                               CornerRadius="12" Padding="15,10" HorizontalAlignment="Center" Margin="0,0,0,20">
                            <StackPanel Orientation="Horizontal">
                                <Ellipse Width="8" Height="8" Fill="#1DE9B6" Margin="0,0,8,0">
                                    <Ellipse.Effect>
                                        <DropShadowEffect Color="#1DE9B6" BlurRadius="6" ShadowDepth="0" Opacity="0.8"/>
                                    </Ellipse.Effect>
                                </Ellipse>
                                <TextBlock x:Name="SystemReadyText" Text="SYSTEM READY" FontSize="11" FontWeight="Medium" Foreground="#1DE9B6"/>
                            </StackPanel>
                        </Border>

                        <TextBlock x:Name="WelcomeSubText" Text="Select a user to start secure communication"
                                  FontSize="12" Foreground="#B0BEC5" HorizontalAlignment="Center"/>

                        <!-- Developer Mode Indicator - REMOVED -->
                        <!-- This indicator has been permanently removed for cleaner UI -->
                    </StackPanel>

                    <!-- Chat Window -->
                    <Grid x:Name="ChatWindow" Visibility="Collapsed">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="60"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="80"/>
                        </Grid.RowDefinitions>

                        <!-- Chat Header -->
                        <Border Grid.Row="0" Background="#16213E" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" VerticalAlignment="Center">
                                    <TextBlock x:Name="ChatTitleText" Text="Secure Chat" FontSize="16" FontWeight="Bold" Foreground="#00D4AA"/>
                                    <TextBlock Text="End-to-End Encrypted" FontSize="10" Foreground="#ECF0F1"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Orientation="Horizontal">
                                    <Button Background="#00D4AA" Foreground="#1A1A2E" Padding="8" Margin="2"
                                           Click="ShareFileButton_Click" ToolTip="Share File">
                                        <controls:FeatherIcon IconName="paperclip" IconColor="#1A1A2E" Width="16" Height="16"/>
                                    </Button>
                                    <Button Background="#FF6B6B" Foreground="#ECF0F1" Padding="8" Margin="2"
                                           Click="CloseChatButton_Click" ToolTip="Close Chat">
                                        <controls:FeatherIcon IconName="x" IconColor="#ECF0F1" Width="16" Height="16"/>
                                    </Button>
                                </StackPanel>
                            </Grid>
                        </Border>

                        <!-- Messages -->
                        <Grid Grid.Row="1">
                            <ScrollViewer x:Name="MessagesScrollViewer" Background="#1A1A2E"
                                         VerticalScrollBarVisibility="Auto">
                                <StackPanel x:Name="MessagesPanel" Margin="15"/>
                            </ScrollViewer>

                            <!-- Drag & Drop Overlay -->
                            <controls:DragDropArea x:Name="ChatDragDropArea"
                                                  Visibility="Collapsed"
                                                  Background="#80000000"
                                                  FilesDropped="ChatDragDropArea_FilesDropped"
                                                  ErrorOccurred="ChatDragDropArea_ErrorOccurred"/>
                        </Grid>

                        <!-- Attachments Area -->
                        <Border Grid.Row="2" x:Name="AttachmentsArea"
                               Background="#0F1419"
                               BorderBrush="#00D4AA"
                               BorderThickness="0,1,0,0"
                               Visibility="Collapsed"
                               MaxHeight="200">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Attachments Header -->
                                <Border Grid.Row="0" Background="#16213E" Padding="15,8">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                                            <controls:FeatherIcon IconName="paperclip" IconColor="#00D4AA" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock x:Name="AttachmentsCountText" Text="المرفقات (0)"
                                                      FontSize="12" FontWeight="SemiBold" Foreground="#00D4AA"/>
                                        </StackPanel>

                                        <Button Grid.Column="1" x:Name="ClearAttachmentsButton"
                                               Background="Transparent" BorderThickness="0"
                                               Width="24" Height="24"
                                               Click="ClearAttachmentsButton_Click"
                                               ToolTip="مسح جميع المرفقات">
                                            <controls:FeatherIcon IconName="x" IconColor="#E74C3C" Width="14" Height="14"/>
                                        </Button>
                                    </Grid>
                                </Border>

                                <!-- Attachments Content -->
                                <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled">
                                    <StackPanel x:Name="AttachmentsPanel" Orientation="Horizontal" Margin="15,10"/>
                                </ScrollViewer>
                            </Grid>
                        </Border>

                        <!-- Input -->
                        <Border Grid.Row="3" Background="#16213E" Padding="15">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Attachment Button -->
                                <Button x:Name="AttachmentButton" Grid.Column="0"
                                       Background="#2C3E50"
                                       BorderBrush="#00D4AA"
                                       BorderThickness="1"
                                       Width="45" Height="45"
                                       Margin="0,0,8,0"
                                       Cursor="Hand"
                                       Click="AttachmentButton_Click"
                                       ToolTip="إضافة مرفق">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="8">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                            <Border.Effect>
                                                                <DropShadowEffect Color="#00D4AA" BlurRadius="5" ShadowDepth="0" Opacity="0.3"/>
                                                            </Border.Effect>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#34495E"/>
                                                                <Setter Property="BorderBrush" Value="#1DE9B6"/>
                                                            </Trigger>
                                                            <Trigger Property="IsPressed" Value="True">
                                                                <Setter Property="Background" Value="#00D4AA"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <controls:FeatherIcon IconName="paperclip" IconColor="#00D4AA" Width="20" Height="20"/>
                                </Button>

                                <!-- Emoji Button -->
                                <Button x:Name="EmojiButton" Grid.Column="1"
                                       Background="#2C3E50"
                                       BorderBrush="#00D4AA"
                                       BorderThickness="1"
                                       Width="45" Height="45"
                                       Margin="0,0,8,0"
                                       Cursor="Hand"
                                       Click="EmojiButton_Click"
                                       ToolTip="اختر إيموجي">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                               BorderBrush="{TemplateBinding BorderBrush}"
                                                               BorderThickness="{TemplateBinding BorderThickness}"
                                                               CornerRadius="8">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                            <Border.Effect>
                                                                <DropShadowEffect Color="#00D4AA" BlurRadius="5" ShadowDepth="0" Opacity="0.3"/>
                                                            </Border.Effect>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#34495E"/>
                                                                <Setter Property="BorderBrush" Value="#1DE9B6"/>
                                                            </Trigger>
                                                            <Trigger Property="IsPressed" Value="True">
                                                                <Setter Property="Background" Value="#00D4AA"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <TextBlock Text="😊" FontSize="20"/>
                                </Button>

                                <!-- Message Input -->
                                <Border Grid.Column="2"
                                       Background="#2C3E50"
                                       BorderBrush="#00D4AA"
                                       BorderThickness="1"
                                       CornerRadius="8"
                                       Margin="0,0,8,0">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#00D4AA" BlurRadius="5" ShadowDepth="0" Opacity="0.2"/>
                                    </Border.Effect>
                                    <TextBox x:Name="MessageTextBox"
                                            Background="Transparent"
                                            Foreground="#ECF0F1"
                                            BorderThickness="0"
                                            Padding="15,12"
                                            FontSize="14"
                                            VerticalAlignment="Center"
                                            KeyDown="MessageTextBox_KeyDown"
                                            TextWrapping="Wrap"
                                            AcceptsReturn="True"
                                            MaxHeight="100"
                                            VerticalScrollBarVisibility="Auto"/>
                                </Border>

                                <!-- Send Button -->
                                <Button x:Name="SendButton" Grid.Column="3"
                                       Background="#00D4AA"
                                       BorderThickness="0"
                                       Width="80" Height="45"
                                       Cursor="Hand"
                                       Click="SendButton_Click">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}"
                                                               CornerRadius="8">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                            <Border.Effect>
                                                                <DropShadowEffect Color="#00D4AA" BlurRadius="10" ShadowDepth="0" Opacity="0.6"/>
                                                            </Border.Effect>
                                                        </Border>
                                                        <ControlTemplate.Triggers>
                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                <Setter Property="Background" Value="#1DE9B6"/>
                                                            </Trigger>
                                                            <Trigger Property="IsPressed" Value="True">
                                                                <Setter Property="Background" Value="#00C399"/>
                                                            </Trigger>
                                                        </ControlTemplate.Triggers>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                        </Style>
                                    </Button.Style>
                                    <StackPanel Orientation="Horizontal">
                                        <controls:FeatherIcon IconName="send" IconColor="#1A1A2E" Width="18" Height="18" Margin="0,0,6,0"/>
                                        <TextBlock Text="إرسال"
                                                  Foreground="#1A1A2E"
                                                  FontWeight="Bold"
                                                  FontSize="12"
                                                  VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </Grid>
                        </Border>
                    </Grid>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    </Grid>
</Window>
