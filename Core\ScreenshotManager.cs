using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using System.Windows;
using WinForms = System.Windows.Forms;

namespace SafeLink.Core
{
    public static class ScreenshotManager
    {
        [DllImport("user32.dll")]
        private static extern IntPtr GetDesktopWindow();

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowDC(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr ReleaseDC(IntPtr hWnd, IntPtr hDC);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleDC(IntPtr hdc);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleBitmap(IntPtr hdc, int nWidth, int nHeight);

        [DllImport("gdi32.dll")]
        private static extern IntPtr SelectObject(IntPtr hdc, IntPtr hgdiobj);

        [DllImport("gdi32.dll")]
        private static extern bool BitBlt(IntPtr hdc, int nXDest, int nYDest, int nWidth, int nHeight,
            IntPtr hdcSrc, int nXSrc, int nYSrc, uint dwRop);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteDC(IntPtr hdc);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        private const uint SRCCOPY = 0x00CC0020;

        public static async Task<byte[]> TakeScreenshotAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    return TakeScreenshot();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Screenshot failed: {ex.Message}");
                    throw;
                }
            });
        }

        private static byte[] TakeScreenshot()
        {
            // Get screen dimensions
            var screenWidth = WinForms.Screen.PrimaryScreen.Bounds.Width;
            var screenHeight = WinForms.Screen.PrimaryScreen.Bounds.Height;

            // Get desktop window handle
            var desktopHandle = GetDesktopWindow();
            var desktopDC = GetWindowDC(desktopHandle);

            try
            {
                // Create compatible DC and bitmap
                var memoryDC = CreateCompatibleDC(desktopDC);
                var bitmap = CreateCompatibleBitmap(desktopDC, screenWidth, screenHeight);
                var oldBitmap = SelectObject(memoryDC, bitmap);

                // Copy screen to bitmap
                BitBlt(memoryDC, 0, 0, screenWidth, screenHeight, desktopDC, 0, 0, SRCCOPY);

                // Create managed bitmap from handle
                using (var managedBitmap = Image.FromHbitmap(bitmap))
                {
                    // Convert to byte array
                    using (var stream = new MemoryStream())
                    {
                        managedBitmap.Save(stream, ImageFormat.Png);
                        return stream.ToArray();
                    }
                }
            }
            finally
            {
                // Clean up resources
                ReleaseDC(desktopHandle, desktopDC);
            }
        }

        public static async Task<string> SaveScreenshotAsync(byte[] screenshotData, string filename = null)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var screenshotsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Screenshots");
                    if (!Directory.Exists(screenshotsPath))
                    {
                        Directory.CreateDirectory(screenshotsPath);
                    }

                    if (string.IsNullOrEmpty(filename))
                    {
                        filename = $"screenshot_{DateTime.Now:yyyyMMdd_HHmmss}.png";
                    }

                    var filePath = Path.Combine(screenshotsPath, filename);
                    File.WriteAllBytes(filePath, screenshotData);

                    return filePath;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to save screenshot: {ex.Message}");
                    throw;
                }
            });
        }

        public static async Task<byte[]> TakeWindowScreenshotAsync(IntPtr windowHandle)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // Get window rectangle
                    if (!GetWindowRect(windowHandle, out var rect))
                    {
                        throw new InvalidOperationException("Failed to get window rectangle");
                    }

                    var width = rect.Right - rect.Left;
                    var height = rect.Bottom - rect.Top;

                    if (width <= 0 || height <= 0)
                    {
                        throw new InvalidOperationException("Invalid window dimensions");
                    }

                    // Get window DC
                    var windowDC = GetWindowDC(windowHandle);

                    try
                    {
                        // Create compatible DC and bitmap
                        var memoryDC = CreateCompatibleDC(windowDC);
                        var bitmap = CreateCompatibleBitmap(windowDC, width, height);
                        var oldBitmap = SelectObject(memoryDC, bitmap);

                        // Copy window to bitmap
                        BitBlt(memoryDC, 0, 0, width, height, windowDC, 0, 0, SRCCOPY);

                        // Create managed bitmap from handle
                        using (var managedBitmap = Image.FromHbitmap(bitmap))
                        {
                            // Convert to byte array
                            using (var stream = new MemoryStream())
                            {
                                managedBitmap.Save(stream, ImageFormat.Png);
                                return stream.ToArray();
                            }
                        }
                    }
                    finally
                    {
                        ReleaseDC(windowHandle, windowDC);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Window screenshot failed: {ex.Message}");
                    throw;
                }
            });
        }

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        public static async Task<byte[]> TakeRegionScreenshotAsync(Rectangle region)
        {
            return await Task.Run(() =>
            {
                try
                {
                    // Get desktop window handle
                    var desktopHandle = GetDesktopWindow();
                    var desktopDC = GetWindowDC(desktopHandle);

                    try
                    {
                        // Create compatible DC and bitmap
                        var memoryDC = CreateCompatibleDC(desktopDC);
                        var bitmap = CreateCompatibleBitmap(desktopDC, region.Width, region.Height);
                        var oldBitmap = SelectObject(memoryDC, bitmap);

                        // Copy region to bitmap
                        BitBlt(memoryDC, 0, 0, region.Width, region.Height,
                               desktopDC, region.X, region.Y, SRCCOPY);

                        // Create managed bitmap from handle
                        using (var managedBitmap = Image.FromHbitmap(bitmap))
                        {
                            // Convert to byte array
                            using (var stream = new MemoryStream())
                            {
                                managedBitmap.Save(stream, ImageFormat.Png);
                                return stream.ToArray();
                            }
                        }
                    }
                    finally
                    {
                        ReleaseDC(desktopHandle, desktopDC);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Region screenshot failed: {ex.Message}");
                    throw;
                }
            });
        }

        public static async Task<string> TakeAndSaveScreenshotAsync(string filename = null)
        {
            var screenshotData = await TakeScreenshotAsync();
            return await SaveScreenshotAsync(screenshotData, filename);
        }
    }
}
