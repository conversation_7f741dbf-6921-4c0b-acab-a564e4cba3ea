Write-Host "SafeLink Status Check" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Cyan

Write-Host "`nChecking SafeLink processes..." -ForegroundColor Yellow
$processes = Get-Process | Where-Object {$_.ProcessName -like "*SafeLink*"}
if ($processes) {
    foreach ($proc in $processes) {
        Write-Host "Process: $($proc.ProcessName) (PID: $($proc.Id)) - Memory: $([math]::Round($proc.WorkingSet64/1MB, 2)) MB" -ForegroundColor Green
    }
} else {
    Write-Host "No SafeLink processes found" -ForegroundColor Red
}

Write-Host "`nChecking network ports..." -ForegroundColor Yellow
$udpPorts = Get-NetUDPEndpoint | Where-Object {$_.LocalPort -ge 8888 -and $_.LocalPort -le 8895}
if ($udpPorts) {
    foreach ($port in $udpPorts) {
        Write-Host "UDP Port $($port.LocalPort) is ACTIVE" -ForegroundColor Green
    }
} else {
    Write-Host "No UDP ports 8888-8895 active" -ForegroundColor Gray
}

$tcpPorts = Get-NetTCPConnection | Where-Object {$_.LocalPort -ge 8888 -and $_.LocalPort -le 8895}
if ($tcpPorts) {
    foreach ($port in $tcpPorts) {
        Write-Host "TCP Port $($port.LocalPort) is ACTIVE (State: $($port.State))" -ForegroundColor Green
    }
} else {
    Write-Host "No TCP ports 8888-8895 active" -ForegroundColor Gray
}

Write-Host "`nStatus check completed!" -ForegroundColor Cyan
