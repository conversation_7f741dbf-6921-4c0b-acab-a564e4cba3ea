@echo off
echo 🚀 Starting SafeLink - Secure Communication Platform
echo ================================================
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET Runtime is not installed!
    echo Please install .NET 8.0 Runtime from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ .NET Runtime detected
echo 🔥 Adding firewall rules...
echo.

REM Start SafeLink
echo 🚀 Starting SafeLink...
SafeLink.exe

if %errorlevel% neq 0 (
    echo.
    echo ❌ SafeLink failed to start
    echo Check the error messages above
    pause
)
