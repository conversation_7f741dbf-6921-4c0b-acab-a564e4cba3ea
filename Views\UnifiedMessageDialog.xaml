<Window x:Class="SafeLink.Views.UnifiedMessageDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink"
        Height="480" Width="650"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        ShowInTaskbar="False">

    <Border CornerRadius="12" BorderThickness="1" BorderBrush="#00E5FF">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#0D1421" Offset="0"/>
                <GradientStop Color="#1A2332" Offset="0.5"/>
                <GradientStop Color="#243447" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>
        <Border.Effect>
            <DropShadowEffect Color="#00E5FF" BlurRadius="20" ShadowDepth="0" Opacity="0.5"/>
        </Border.Effect>

        <Grid Margin="35">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Professional Header -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,30">
                <!-- Shield Icon -->
                <controls:ShieldIcon x:Name="MessageShield"
                                   Width="60" Height="60"
                                   ShieldColor="#00E5FF"
                                   Margin="0,0,0,15"
                                   HorizontalAlignment="Center"/>
                <TextBlock Text="SafeLink" FontSize="18" FontWeight="SemiBold" Foreground="White"
                          HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- Professional Message -->
            <Border Grid.Row="1" Background="#15FFFFFF" BorderBrush="#30FFFFFF" BorderThickness="1"
                   CornerRadius="12" Padding="35,25" Margin="0,0,0,35">
                <StackPanel>
                    <TextBlock x:Name="MainQuestionText" Text="هل تريد إغلاق البرنامج؟"
                              FontSize="20" FontWeight="SemiBold" Foreground="White"
                              HorizontalAlignment="Center" Margin="0,0,0,25" TextWrapping="Wrap"/>

                    <!-- Detailed Explanation -->
                    <Border Background="#10FFFFFF" BorderBrush="#20FFFFFF" BorderThickness="1"
                           CornerRadius="10" Padding="30,22" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock x:Name="ExplanationTitle" Text="ماذا سيحدث:"
                                      FontSize="16" FontWeight="SemiBold" Foreground="#00E5FF"
                                      HorizontalAlignment="Right" Margin="0,0,0,15"/>
                            <TextBlock x:Name="ExplanationText"
                                      Text="• سيتم قطع جميع الاتصالات النشطة&#x0a;• سيتم حفظ الرسائل المرسلة&#x0a;• سيتم إغلاق جميع النوافذ المفتوحة"
                                      FontSize="15" Foreground="#B0BEC5" LineHeight="26"
                                      HorizontalAlignment="Right" TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <TextBlock x:Name="SubMessageText" Text="هذا الإجراء آمن ولن يؤثر على بياناتك المحفوظة"
                              FontSize="13" Foreground="#4CAF50"
                              HorizontalAlignment="Center" FontStyle="Italic"/>
                </StackPanel>
            </Border>

            <!-- Professional Action Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="YesButton" Background="#FF5252" Foreground="White"
                       FontWeight="SemiBold" BorderThickness="0" Padding="35,18" Margin="0,0,25,0"
                       Click="YesButton_Click" Width="160" Height="55" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="8"
                                               Padding="{TemplateBinding Padding}">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#FF5252" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                                            </Border.Effect>
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E53E3E"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon x:Name="YesIcon" IconName="check" IconColor="White" Width="20" Height="20" Margin="0,0,12,0"/>
                        <TextBlock x:Name="YesText" Text="نعم" FontSize="16" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button x:Name="NoButton" Background="Transparent" Foreground="#00E5FF"
                       FontWeight="SemiBold" BorderThickness="1" BorderBrush="#00E5FF" Padding="35,18"
                       Click="NoButton_Click" Width="160" Height="55" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               BorderBrush="{TemplateBinding BorderBrush}"
                                               BorderThickness="{TemplateBinding BorderThickness}"
                                               CornerRadius="8"
                                               Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#00E5FF"/>
                                                <Setter Property="BorderBrush" Value="#00E5FF"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon x:Name="NoIcon" IconName="x" IconColor="#00E5FF" Width="20" Height="20" Margin="0,0,12,0"/>
                        <TextBlock x:Name="NoText" Text="لا" FontSize="16" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
