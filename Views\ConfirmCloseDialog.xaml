<Window x:Class="SafeLink.Views.ConfirmCloseDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink - تأكيد الإغلاق"
        Height="220" Width="450"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        ShowInTaskbar="False">

    <Border CornerRadius="12" BorderThickness="1" BorderBrush="#00E5FF">
        <Border.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#0D1421" Offset="0"/>
                <GradientStop Color="#1A2332" Offset="0.5"/>
                <GradientStop Color="#243447" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>
        <Border.Effect>
            <DropShadowEffect Color="#00E5FF" BlurRadius="20" ShadowDepth="0" Opacity="0.5"/>
        </Border.Effect>

        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Professional Header -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,25">
                <Border Background="#FF5252" CornerRadius="25" Width="50" Height="50" Margin="0,0,0,15">
                    <Border.Effect>
                        <DropShadowEffect Color="#FF5252" BlurRadius="10" ShadowDepth="0" Opacity="0.5"/>
                    </Border.Effect>
                    <controls:FeatherIcon IconName="alert-triangle" IconColor="White" Width="24" Height="24"
                                         HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <TextBlock Text="SafeLink" FontSize="16" FontWeight="SemiBold" Foreground="White"
                          HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- Professional Message -->
            <Border Grid.Row="1" Background="#15FFFFFF" BorderBrush="#30FFFFFF" BorderThickness="1"
                   CornerRadius="8" Padding="20,15" Margin="0,0,0,25">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock x:Name="MainQuestionText" Text="هل تريد إغلاق البرنامج؟"
                              FontSize="16" FontWeight="SemiBold" Foreground="White"
                              HorizontalAlignment="Center" Margin="0,0,0,8"/>
                    <TextBlock x:Name="SubMessageText" Text="سيتم قطع جميع الاتصالات النشطة"
                              FontSize="12" Foreground="#B0BEC5"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Professional Action Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="YesButton" Background="#FF5252" Foreground="White"
                       FontWeight="SemiBold" BorderThickness="0" Padding="25,12" Margin="0,0,15,0"
                       Click="YesButton_Click" Width="120" Height="40" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="8"
                                               Padding="{TemplateBinding Padding}">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#FF5252" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                                            </Border.Effect>
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E53E3E"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="x" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="نعم" FontSize="12" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button x:Name="NoButton" Background="Transparent" Foreground="#00E5FF"
                       FontWeight="SemiBold" BorderThickness="1" BorderBrush="#00E5FF" Padding="25,12"
                       Click="NoButton_Click" Width="120" Height="40" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               BorderBrush="{TemplateBinding BorderBrush}"
                                               BorderThickness="{TemplateBinding BorderThickness}"
                                               CornerRadius="8"
                                               Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#00E5FF"/>
                                                <Setter Property="BorderBrush" Value="#00E5FF"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="arrow-left" IconColor="#00E5FF" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="لا" FontSize="12" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
