using System.Windows;
using SafeLink.Core;

namespace SafeLink.Views
{
    public partial class ConfirmExitDialog : Window
    {
        public bool? Result { get; private set; }

        public ConfirmExitDialog()
        {
            InitializeComponent();

            // Update language
            UpdateLanguage();

            // Set focus to No button by default
            NoButton.Focus();
        }

        private void UpdateLanguage()
        {
            try
            {
                // Update window title
                Title = LanguageManager.GetString("ConfirmExitTitle");

                // Update header texts
                var headerText = this.FindName("HeaderText") as System.Windows.Controls.TextBlock;
                var subHeaderText = this.FindName("SubHeaderText") as System.Windows.Controls.TextBlock;

                if (headerText != null)
                {
                    headerText.Text = LanguageManager.GetString("ConfirmExit");
                    // Hide sub header text for single language display
                    if (subHeaderText != null)
                        subHeaderText.Visibility = System.Windows.Visibility.Collapsed;
                }

                // Update message texts
                var messageText = this.FindName("MessageText") as System.Windows.Controls.TextBlock;
                var subMessageText = this.FindName("SubMessageText") as System.Windows.Controls.TextBlock;

                if (messageText != null)
                {
                    messageText.Text = LanguageManager.GetString("ExitQuestion");
                    // Hide sub message text for single language display
                    if (subMessageText != null)
                        subMessageText.Visibility = System.Windows.Visibility.Collapsed;
                }

                // Update button texts - single language only
                YesButton.Content = LanguageManager.GetString("Yes");
                NoButton.Content = LanguageManager.GetString("No");

                // Set RTL for Arabic
                if (LanguageManager.CurrentLanguage == "ar")
                {
                    FlowDirection = System.Windows.FlowDirection.RightToLeft;
                }
                else
                {
                    FlowDirection = System.Windows.FlowDirection.LeftToRight;
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error updating language in ConfirmExitDialog: {ex.Message}");
            }
        }

        private void YesButton_Click(object sender, RoutedEventArgs e)
        {
            Result = true;
            DialogResult = true;
            Close();
        }

        private void NoButton_Click(object sender, RoutedEventArgs e)
        {
            Result = false;
            DialogResult = false;
            Close();
        }

        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Escape)
            {
                Result = false;
                DialogResult = false;
                Close();
            }
            else if (e.Key == System.Windows.Input.Key.Enter)
            {
                // Default action is No (safer option)
                Result = false;
                DialogResult = false;
                Close();
            }
            
            base.OnKeyDown(e);
        }
    }
}
