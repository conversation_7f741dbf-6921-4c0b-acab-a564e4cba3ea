﻿#pragma checksum "..\..\..\..\Views\LoginWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1AAB039E33A6697B495597BF15C1F3CDF1E4A2D7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Views {
    
    
    /// <summary>
    /// LoginWindow
    /// </summary>
    public partial class LoginWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 149 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SecureAccessText;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AuthPortalText;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsernameLabel;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PasswordLabel;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PasswordTextBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TogglePasswordButton;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon EyeIcon;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberLoginCheckBox;
        
        #line default
        #line hidden
        
        
        #line 343 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoginButton;
        
        #line default
        #line hidden
        
        
        #line 353 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LoginButtonText;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterButton;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RegisterButtonText;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceIdDisplay;
        
        #line default
        #line hidden
        
        
        #line 403 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PowerButton;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon PowerIcon;
        
        #line default
        #line hidden
        
        
        #line 444 "..\..\..\..\Views\LoginWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VersionText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/views/loginwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\LoginWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 13 "..\..\..\..\Views\LoginWindow.xaml"
            ((SafeLink.Views.LoginWindow)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.LoginWindow_KeyDown);
            
            #line default
            #line hidden
            
            #line 14 "..\..\..\..\Views\LoginWindow.xaml"
            ((SafeLink.Views.LoginWindow)(target)).Loaded += new System.Windows.RoutedEventHandler(this.LoginWindow_Loaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SecureAccessText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.AuthPortalText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.UsernameLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.UsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.PasswordLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 8:
            this.PasswordTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.TogglePasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 212 "..\..\..\..\Views\LoginWindow.xaml"
            this.TogglePasswordButton.Click += new System.Windows.RoutedEventHandler(this.TogglePasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.EyeIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 11:
            this.RememberLoginCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.LoginButton = ((System.Windows.Controls.Button)(target));
            
            #line 345 "..\..\..\..\Views\LoginWindow.xaml"
            this.LoginButton.Click += new System.Windows.RoutedEventHandler(this.LoginButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.LoginButtonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.RegisterButton = ((System.Windows.Controls.Button)(target));
            
            #line 359 "..\..\..\..\Views\LoginWindow.xaml"
            this.RegisterButton.Click += new System.Windows.RoutedEventHandler(this.RegisterButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RegisterButtonText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.DeviceIdDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.PowerButton = ((System.Windows.Controls.Button)(target));
            
            #line 409 "..\..\..\..\Views\LoginWindow.xaml"
            this.PowerButton.Click += new System.Windows.RoutedEventHandler(this.PowerButton_Click);
            
            #line default
            #line hidden
            
            #line 410 "..\..\..\..\Views\LoginWindow.xaml"
            this.PowerButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.PowerButton_MouseEnter);
            
            #line default
            #line hidden
            
            #line 411 "..\..\..\..\Views\LoginWindow.xaml"
            this.PowerButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.PowerButton_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 18:
            this.PowerIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 19:
            this.VersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

