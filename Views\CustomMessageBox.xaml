<Window x:Class="SafeLink.Views.CustomMessageBox"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink"
        Height="280" Width="450"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        ShowInTaskbar="False"
        AllowsTransparency="True">

    <Window.Resources>
        <!-- Professional Message Box Gradient -->
        <LinearGradientBrush x:Key="MessageBoxGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#0D1421" Offset="0"/>
            <GradientStop Color="#1A2332" Offset="0.5"/>
            <GradientStop Color="#243447" Offset="1"/>
        </LinearGradientBrush>

        <!-- Accent Colors -->
        <SolidColorBrush x:Key="PrimaryAccent" Color="#00E5FF"/>
        <SolidColorBrush x:Key="ErrorAccent" Color="#FF5252"/>
        <SolidColorBrush x:Key="WarningAccent" Color="#FFB74D"/>
        <SolidColorBrush x:Key="SuccessAccent" Color="#1DE9B6"/>
        <SolidColorBrush x:Key="TextPrimary" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextSecondary" Color="#B0BEC5"/>

        <!-- Modern Button Style -->
        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryAccent}"/>
            <Setter Property="Foreground" Value="#0D1421"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#00B8CC"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondary}"/>
            <Setter Property="BorderBrush" Value="#30FFFFFF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="{StaticResource PrimaryAccent}"/>
                                <Setter Property="Foreground" Value="{StaticResource PrimaryAccent}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="{StaticResource MessageBoxGradient}"
           BorderBrush="{StaticResource PrimaryAccent}"
           BorderThickness="1"
           CornerRadius="12">
        <Border.Effect>
            <DropShadowEffect Color="#000000" BlurRadius="25" ShadowDepth="0" Opacity="0.7"/>
        </Border.Effect>

        <Grid Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Professional Header -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,0,0,25">
                <!-- Shield Icon -->
                <controls:ShieldIcon x:Name="MessageShield"
                                   Width="50" Height="50"
                                   ShieldColor="{StaticResource PrimaryAccent}"
                                   Margin="0,0,0,15"
                                   HorizontalAlignment="Center"/>
                <TextBlock x:Name="TitleText"
                          Text="SafeLink"
                          FontSize="16"
                          FontWeight="SemiBold"
                          Foreground="{StaticResource TextPrimary}"
                          HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- Message Content -->
            <Border Grid.Row="1"
                   Background="#10FFFFFF"
                   BorderBrush="#20FFFFFF"
                   BorderThickness="1"
                   CornerRadius="8"
                   Padding="20,15"
                   Margin="0,0,0,25">
                <TextBlock x:Name="MessageText"
                          Text="Message content here"
                          FontSize="13"
                          Foreground="{StaticResource TextPrimary}"
                          TextWrapping="Wrap"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center"
                          TextAlignment="Center"
                          LineHeight="20"/>
            </Border>

            <!-- Action Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="OkButton"
                       Style="{StaticResource PrimaryButton}"
                       Click="OkButton_Click"
                       Width="110" Height="40"
                       Margin="0,0,15,0">
                    <TextBlock x:Name="OkButtonText" Text="OK" FontWeight="SemiBold"/>
                </Button>

                <Button x:Name="CancelButton"
                       Style="{StaticResource SecondaryButton}"
                       Click="CancelButton_Click"
                       Width="110" Height="40"
                       Visibility="Collapsed">
                    <TextBlock x:Name="CancelButtonText" Text="Cancel" FontWeight="Medium"/>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
