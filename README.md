# 🛡️ SafeLink - Professional Security Communication Platform

**SafeLink** is a comprehensive, secure communication program developed by **Drikon Security Solutions** featuring device-based authentication, professional security-themed UI, file sharing, remote printing, developer controls, Arabic/English support, and end-to-end encryption. Built as a **native Windows application** using **C# and WPF** with enterprise-grade security design.

## 🚀 Features

### 🔐 Security & Authentication
- **Device-based authentication** with unique device keys
- **End-to-end encryption** using AES-256
- **Offline mode support** within local networks
- **Developer bypass** for multi-device access
- **Secure credential storage** with SQLite database

### 🎨 User Interface
- **Native Windows application** (no Electron dependency)
- **Retro Atari-style** color scheme with neon glowing effects
- **Bilingual support** (English/Arabic) with RTL layout
- **Real-time status indicators** (Green/Gray/Red dots)
- **Role-based avatars** with special glowing effects
- **Responsive WPF design** for different screen sizes

### 💬 Communication
- **Private chat windows** between users
- **Group chats** (developer-created only)
- **File sharing** with drag-and-drop support
- **Image preview** and file management
- **Message history** with timestamps

### 🖨️ Remote Printing
- **Virtual printer driver** installation
- **Cross-device print sharing** between authorized users
- **Admin-controlled** printer permissions
- **Seamless printing** without repeated prompts

### 👨‍💻 Developer Controls
- **Complete user management** (reset, delete accounts)
- **Remote command execution** (PowerShell, CMD)
- **System monitoring** (CPU, memory, network)
- **Device health alerts** and performance monitoring
- **Remote program updates** across all devices

### 🌍 Multi-language Support
- **Dynamic language switching** (English ↔ Arabic)
- **Complete RTL support** for Arabic interface
- **Contextual username display** with tooltips
- **Mixed content support** for bilingual environments

## 📋 System Requirements

- **Windows 7, 8, 10, or 11**
- **.NET 8.0 Runtime** (automatically installed)
- **Minimum 4GB RAM**
- **100MB free disk space**
- **Network connectivity** for multi-device communication

## 🛠️ Installation & Setup

### Quick Start

1. **Download** the SafeLink package
2. **Extract** to desired location
3. **Build** the application:
   ```bash
   dotnet build
   ```
4. **Run** the application:
   ```bash
   dotnet run
   ```

### Alternative - Direct Executable

1. **Build Release Version:**
   ```bash
   dotnet publish -c Release -r win-x64 --self-contained
   ```
2. **Run SafeLink.exe** from the publish folder

### First Time Setup

1. **Launch SafeLink** - The loading screen will appear
2. **Developer Account** (automatically created):
   - Username: `Drikon`
   - Password: `DragonSafe2024!`
3. **Register new users** or login with existing accounts
4. **Configure settings** including language preferences

## 🔑 Default Accounts

### Developer Account
- **Username:** `Drikon`
- **Password:** `DragonSafe2024!`
- **Privileges:** Full system access, multi-device login, user management

### User Registration
- New users must register with username/password
- Device key automatically generated and linked
- Arabic username optional for bilingual support

## 🎮 Usage Guide

### Basic Communication
1. **Login** with your credentials
2. **Select a user** from the user list
3. **Start chatting** in the opened chat window
4. **Share files** by dragging and dropping
5. **Use F8** to report problems to developer

### Developer Features
- **Access Developer Panel:** `Ctrl + Shift + D`
- **User Management:** Reset device keys, delete users
- **System Monitoring:** View real-time system stats
- **Remote Commands:** Execute commands on target devices
- **Problem Reports:** Review user-submitted issues

### Language Switching
1. **Click Settings** (⚙️ icon)
2. **Select Language:** English or العربية
3. **Interface updates** automatically with RTL support

### Printer Sharing
1. **Admin users** can link devices
2. **Select target printer** in settings
3. **Print jobs** route automatically to shared printers
4. **Toggle sharing** on/off per user

## 🔧 Configuration

### Network Settings
- **Default Port:** 8888 (UDP Discovery)
- **TCP Port:** 8889 (Direct Communication)
- **Local Network:** Automatic discovery
- **Firewall:** Ensure ports are open

### Security Settings
- **Encryption:** AES-256 (automatic)
- **Device Keys:** Generated per device
- **Session Management:** Automatic timeout

### File Storage
- **Chat Files:** `./Data/ChatFiles/`
- **Screenshots:** `./Screenshots/`
- **System Reports:** `./Data/Reports/`
- **Database:** `./Data/SafeLink.db`

## 🚨 Troubleshooting

### Common Issues

**Connection Problems:**
- Check if ports 8888/8889 are available
- Verify network connectivity
- Restart the application

**Login Issues:**
- Verify username/password
- Check device authorization
- Contact developer for device reset

**Performance Issues:**
- Monitor system resources
- Check for background processes
- Review system logs in developer panel

### Error Reporting
- **F8 Key:** Quick screenshot report
- **Developer Panel:** Detailed system logs
- **Automatic Logging:** All actions recorded

## 📁 Project Structure

```
SafeLink/
├── SafeLink.csproj      # Project file
├── App.xaml             # Application resources
├── App.xaml.cs          # Application startup
├── app.manifest         # Windows manifest
├── Views/
│   ├── MainWindow.xaml  # Main UI
│   ├── SettingsWindow.xaml
│   └── DeveloperWindow.xaml
├── Core/
│   ├── DatabaseManager.cs
│   ├── NetworkManager.cs
│   ├── DeviceManager.cs
│   └── ScreenshotManager.cs
├── Models/
│   ├── User.cs
│   └── Message.cs
├── Properties/
│   └── Settings.settings
└── Data/
    └── SafeLink.db      # SQLite database
```

## 🔒 Security Features

- **AES-256 Encryption** for all communications
- **Device-based Authentication** prevents unauthorized access
- **Secure Key Exchange** during registration
- **Audit Logging** of all user actions
- **Developer Access Controls** with special privileges
- **Offline Security** maintains protection without internet

## 🌟 Special Features

### Native Windows Application
- **No Electron dependency** - pure Windows application
- **Fast startup** and low memory usage
- **Native Windows integration**
- **System tray support**

### Atari-Style UI
- **Neon glowing effects** throughout interface
- **Retro color scheme** (cyan, green, yellow)
- **Animated elements** with smooth transitions
- **Starfield background** for developer account

### Arabic Support
- **Complete RTL layout** transformation
- **Arabic font rendering** with proper shaping
- **Bidirectional text** support
- **Cultural UI adaptations**

### Developer Tools
- **Golden aura** for developer avatar
- **System command execution** on remote devices
- **Real-time monitoring** of all connected devices
- **Comprehensive logging** and reporting

## 📞 Support

For technical support or feature requests:
- **Developer:** Drikon
- **Report Issues:** Use F8 in application
- **System Logs:** Available in developer panel

## 📄 License

© 2024 Drikon. All rights reserved.

---

**SafeLink** - Native Windows secure communication with retro style and modern security.
