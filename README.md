# 🚀 SafeLink - منصة التواصل الآمن

## 📋 **معلومات المشروع**
- **الإصدار:** Professional Clean Edition
- **التاريخ:** 2025-01-06
- **الحالة:** ✅ جاهز للإنتاج
- **التقنية:** .NET 8.0 WPF

---

## 🎯 **كيفية التشغيل**

### **الطريقة المباشرة:**
```bash
dotnet run --project SafeLink.csproj
```

### **أو بناء وتشغيل:**
```bash
dotnet build SafeLink.csproj
dotnet bin/Debug/net8.0-windows/SafeLink.exe
```

---

## 🔧 **متطلبات التشغيل**

### **✅ مطلوب:**
- **Windows 10/11** (مُوصى به)
- **.NET 8.0 Runtime**
- **صلاحيات المدير** (لإضافة قواعد جدار الحماية)

### **🌐 الشبكة:**
- **نفس الشبكة المحلية** (WiFi أو Ethernet)
- **🔥 جدار حماية ذكي** - قاعدة واحدة تشمل Inbound + Outbound

---

## 🧪 **اختبار جدار الحماية**

```powershell
# تشغيل اختبار جدار الحماية
.\FirewallTest.ps1
```

---

## 🔍 **أدوات التشخيص**

### **لوحة تحكم المطور:**
```
⌨️ في SafeLink اضغط: Ctrl + Shift + D
🔥 اختبر: Check Firewall
🔥 اختبر: Add Firewall Rules
```

---

## 📊 **معلومات تقنية**

### **البورتات المستخدمة:**
- **UDP 8888-8938:** اكتشاف المستخدمين
- **TCP 8889-8939:** الرسائل المباشرة

### **قاعدة جدار الحماية:**
- **SafeLink - Secure Communication:** قاعدة شاملة (Inbound + Outbound)

---

## ✅ **النجاح المتوقع**

### **يجب أن ترى:**
- **🔐 نافذة تسجيل دخول** عند البدء
- **💬 واجهة الدردشة** بعد تسجيل الدخول
- **👥 قائمة المستخدمين** تظهر المستخدمين الآخرين
- **📡 رسائل تصل وتُرسل** بنجاح

### **🔥 جدار الحماية:**
- **نافذة Windows Defender واحدة فقط** ✅
- **موافقة على Inbound** ✅
- **Outbound يعمل تلقائياً** ✅

---

## 🏆 **الميزات**

- **🔒 تشفير آمن** للرسائل
- **📎 مشاركة الملفات** مع دعم متعدد الأنواع
- **😊 دعم الرموز التعبيرية** الملونة
- **🌐 اكتشاف تلقائي** للمستخدمين
- **🔔 إشعارات ذكية** للاتصالات
- **🎨 واجهة عصرية** مع تأثيرات بصرية

---

## 🛠️ **للمطورين**

### **هيكل المشروع:**
```
SafeLink/
├── Core/           # المنطق الأساسي
├── Views/          # واجهات المستخدم
├── Controls/       # عناصر التحكم المخصصة
├── Models/         # نماذج البيانات
├── Tools/          # أدوات التشخيص
└── Resources/      # الموارد والأصول
```

### **الملفات المهمة:**
- **Core/FirewallManager.cs** - إدارة جدار الحماية (مبسط)
- **Core/NetworkManager.cs** - إدارة الشبكة
- **Views/MainWindow.xaml** - الواجهة الرئيسية
- **FirewallTest.ps1** - اختبار جدار الحماية

---

**🎉 مشروع SafeLink جاهز للاستخدام! 🚀✨**
