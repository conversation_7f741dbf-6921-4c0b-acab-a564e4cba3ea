using System;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;

namespace SafeLink.Tools
{
    public static class NetworkTester
    {
        private static readonly int TestPort = 8888;

        public static async Task<bool> TestNetworkConnectivity()
        {
            try
            {
                Debug.WriteLine("🔍 Starting network connectivity test...");

                // Test 1: Check if port is available
                var portAvailable = await TestPortAvailability();
                Debug.WriteLine($"📡 Port {TestPort} available: {portAvailable}");

                // Test 2: Test UDP broadcast
                var udpTest = await TestUDPBroadcast();
                Debug.WriteLine($"📡 UDP broadcast test: {udpTest}");

                // Test 3: Test local network discovery
                var networkTest = await TestLocalNetworkDiscovery();
                Debug.WriteLine($"📡 Local network discovery: {networkTest}");

                // Test 4: Check firewall status
                var firewallTest = await TestFirewallStatus();
                Debug.WriteLine($"🔥 Firewall test: {firewallTest}");

                return portAvailable && udpTest && networkTest;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Network test failed: {ex.Message}");
                return false;
            }
        }

        private static async Task<bool> TestPortAvailability()
        {
            try
            {
                using var udpClient = new UdpClient();
                udpClient.Client.Bind(new IPEndPoint(IPAddress.Any, TestPort));
                Debug.WriteLine($"✅ Port {TestPort} is available");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Port {TestPort} is not available: {ex.Message}");
                return false;
            }
        }

        private static async Task<bool> TestUDPBroadcast()
        {
            try
            {
                using var udpClient = new UdpClient();
                udpClient.EnableBroadcast = true;

                var testMessage = "SAFELINK_TEST_MESSAGE";
                var data = Encoding.UTF8.GetBytes(testMessage);
                var broadcastEndPoint = new IPEndPoint(IPAddress.Broadcast, TestPort + 1);

                await udpClient.SendAsync(data, data.Length, broadcastEndPoint);
                Debug.WriteLine($"✅ UDP broadcast sent successfully");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ UDP broadcast failed: {ex.Message}");
                return false;
            }
        }

        private static async Task<bool> TestLocalNetworkDiscovery()
        {
            try
            {
                var localIP = GetLocalIPAddress();
                if (localIP == null)
                {
                    Debug.WriteLine($"❌ Could not get local IP address");
                    return false;
                }

                Debug.WriteLine($"📍 Local IP: {localIP}");

                // Test ping to gateway
                var gateway = GetDefaultGateway();
                if (gateway != null)
                {
                    var ping = new Ping();
                    var reply = await ping.SendPingAsync(gateway, 3000);
                    Debug.WriteLine($"🏠 Gateway {gateway} ping: {reply.Status}");
                    return reply.Status == IPStatus.Success;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Local network discovery failed: {ex.Message}");
                return false;
            }
        }

        private static async Task<bool> TestFirewallStatus()
        {
            try
            {
                // Simple test to check if we can create a listener
                using var tcpListener = new TcpListener(IPAddress.Any, TestPort + 2);
                tcpListener.Start();
                tcpListener.Stop();
                Debug.WriteLine($"✅ TCP listener test passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ TCP listener test failed (possible firewall): {ex.Message}");
                return false;
            }
        }

        private static IPAddress GetLocalIPAddress()
        {
            try
            {
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        return ip;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Failed to get local IP: {ex.Message}");
            }
            return null;
        }

        private static IPAddress GetDefaultGateway()
        {
            try
            {
                foreach (NetworkInterface ni in NetworkInterface.GetAllNetworkInterfaces())
                {
                    if (ni.OperationalStatus == OperationalStatus.Up &&
                        ni.NetworkInterfaceType != NetworkInterfaceType.Loopback)
                    {
                        foreach (GatewayIPAddressInformation gateway in ni.GetIPProperties().GatewayAddresses)
                        {
                            if (gateway.Address.AddressFamily == AddressFamily.InterNetwork)
                            {
                                return gateway.Address;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"⚠️ Failed to get default gateway: {ex.Message}");
            }
            return null;
        }

        public static async Task<string> GetNetworkDiagnosticReport()
        {
            var report = new StringBuilder();
            report.AppendLine("🔍 SafeLink Network Diagnostic Report");
            report.AppendLine("=====================================");
            report.AppendLine($"📅 Date: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            // Basic network info
            var localIP = GetLocalIPAddress();
            var gateway = GetDefaultGateway();
            
            report.AppendLine("📡 Network Information:");
            report.AppendLine($"   Local IP: {localIP?.ToString() ?? "Not available"}");
            report.AppendLine($"   Gateway: {gateway?.ToString() ?? "Not available"}");
            report.AppendLine($"   Machine Name: {Environment.MachineName}");
            report.AppendLine();

            // Port tests
            report.AppendLine("🔌 Port Tests:");
            var portTest = await TestPortAvailability();
            report.AppendLine($"   Port {TestPort} Available: {(portTest ? "✅ Yes" : "❌ No")}");
            report.AppendLine();

            // UDP tests
            report.AppendLine("📡 UDP Tests:");
            var udpTest = await TestUDPBroadcast();
            report.AppendLine($"   UDP Broadcast: {(udpTest ? "✅ Working" : "❌ Failed")}");
            report.AppendLine();

            // Network interfaces
            report.AppendLine("🌐 Network Interfaces:");
            foreach (NetworkInterface ni in NetworkInterface.GetAllNetworkInterfaces())
            {
                if (ni.OperationalStatus == OperationalStatus.Up)
                {
                    report.AppendLine($"   {ni.Name}: {ni.OperationalStatus} ({ni.NetworkInterfaceType})");
                }
            }
            report.AppendLine();

            // Recommendations
            report.AppendLine("💡 Recommendations:");
            if (!portTest)
            {
                report.AppendLine($"   ⚠️ Port {TestPort} is blocked. Try running as Administrator.");
            }
            if (!udpTest)
            {
                report.AppendLine($"   ⚠️ UDP broadcast failed. Check Windows Firewall settings.");
                report.AppendLine($"   💡 Add SafeLink.exe to Windows Firewall exceptions.");
            }
            if (localIP == null)
            {
                report.AppendLine($"   ⚠️ No network connection detected. Check network adapter.");
            }

            return report.ToString();
        }

        public static async Task RunQuickNetworkFix()
        {
            try
            {
                Debug.WriteLine("🔧 Running quick network fix...");

                // Try to add firewall exception
                var processInfo = new ProcessStartInfo
                {
                    FileName = "netsh",
                    Arguments = $"advfirewall firewall add rule name=\"SafeLink\" dir=in action=allow protocol=UDP localport={TestPort}",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                };

                using var process = Process.Start(processInfo);
                if (process != null)
                {
                    await process.WaitForExitAsync();
                    if (process.ExitCode == 0)
                    {
                        Debug.WriteLine("✅ Firewall rule added successfully");
                    }
                    else
                    {
                        Debug.WriteLine("⚠️ Failed to add firewall rule (may need Administrator)");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"❌ Quick network fix failed: {ex.Message}");
            }
        }
    }
}
