using System;
using System.IO;
using System.Media;
using System.Threading.Tasks;

namespace SafeLink.Core
{
    public static class SoundManager
    {
        private static readonly string SoundsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Sounds");

        static SoundManager()
        {
            // Create sounds directory if it doesn't exist
            if (!Directory.Exists(SoundsPath))
            {
                Directory.CreateDirectory(SoundsPath);
            }
        }

        public static void PlayConnectionSound()
        {
            try
            {
                // Play a pleasant connection sound
                Task.Run(() =>
                {
                    // Use system sounds for now - can be replaced with custom sounds later
                    SystemSounds.Asterisk.Play();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing connection sound: {ex.Message}");
            }
        }

        public static void PlayDisconnectionSound()
        {
            try
            {
                // Play a subtle disconnection sound
                Task.Run(() =>
                {
                    // Use system sounds for now - can be replaced with custom sounds later
                    SystemSounds.Hand.Play();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing disconnection sound: {ex.Message}");
            }
        }

        public static void PlayNotificationSound()
        {
            try
            {
                // Play a general notification sound
                Task.Run(() =>
                {
                    SystemSounds.Beep.Play();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing notification sound: {ex.Message}");
            }
        }

        public static void PlayCustomSound(string soundFile)
        {
            try
            {
                var soundPath = Path.Combine(SoundsPath, soundFile);
                if (File.Exists(soundPath))
                {
                    Task.Run(() =>
                    {
                        using var player = new SoundPlayer(soundPath);
                        player.Play();
                    });
                }
                else
                {
                    // Fallback to system sound
                    PlayNotificationSound();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing custom sound: {ex.Message}");
                // Fallback to system sound
                PlayNotificationSound();
            }
        }
    }
}
