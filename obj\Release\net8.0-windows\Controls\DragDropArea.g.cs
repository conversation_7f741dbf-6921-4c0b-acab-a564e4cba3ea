﻿#pragma checksum "..\..\..\..\Controls\DragDropArea.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1A27DBAF3251476427EB1726AD3CA391D5583472"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Controls {
    
    
    /// <summary>
    /// DragDropArea
    /// </summary>
    public partial class DragDropArea : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 48 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MainBorder;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultContent;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UploadIcon;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MainText;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubText;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupportedFormats;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DragOverContent;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ProcessingContent;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.RotateTransform SpinnerRotation;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Controls\DragDropArea.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcessingText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/controls/dragdroparea.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Controls\DragDropArea.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 6 "..\..\..\..\Controls\DragDropArea.xaml"
            ((SafeLink.Controls.DragDropArea)(target)).Drop += new System.Windows.DragEventHandler(this.DragDropArea_Drop);
            
            #line default
            #line hidden
            
            #line 7 "..\..\..\..\Controls\DragDropArea.xaml"
            ((SafeLink.Controls.DragDropArea)(target)).DragEnter += new System.Windows.DragEventHandler(this.DragDropArea_DragEnter);
            
            #line default
            #line hidden
            
            #line 8 "..\..\..\..\Controls\DragDropArea.xaml"
            ((SafeLink.Controls.DragDropArea)(target)).DragLeave += new System.Windows.DragEventHandler(this.DragDropArea_DragLeave);
            
            #line default
            #line hidden
            
            #line 9 "..\..\..\..\Controls\DragDropArea.xaml"
            ((SafeLink.Controls.DragDropArea)(target)).DragOver += new System.Windows.DragEventHandler(this.DragDropArea_DragOver);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MainBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.DefaultContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            this.UploadIcon = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.MainText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.SubText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.SupportedFormats = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.DragOverContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 9:
            this.ProcessingContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.SpinnerRotation = ((System.Windows.Media.RotateTransform)(target));
            return;
            case 11:
            this.ProcessingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

