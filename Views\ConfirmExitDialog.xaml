<Window x:Class="SafeLink.Views.ConfirmExitDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="تأكيد الإغلاق - Confirm Exit"
        Height="280" Width="450"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        ShowInTaskbar="False">

    <Border Background="#1A1A2E" 
            BorderBrush="#00D4AA" 
            BorderThickness="2" 
            CornerRadius="15"
            Effect="{StaticResource WindowShadow}">
        
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" HorizontalAlignment="Center" Margin="0,10,0,20">
                <controls:FeatherIcon IconName="alert-triangle"
                                    IconColor="#FFB347"
                                    Width="32" Height="32"
                                    HorizontalAlignment="Center"
                                    Margin="0,0,0,10">
                    <controls:FeatherIcon.Effect>
                        <DropShadowEffect Color="#FFB347" BlurRadius="8" ShadowDepth="0" Opacity="0.6"/>
                    </controls:FeatherIcon.Effect>
                </controls:FeatherIcon>
                
                <TextBlock x:Name="HeaderText"
                          Text="تأكيد الإغلاق"
                          FontSize="18"
                          FontWeight="Bold"
                          Foreground="#FFFFFF"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,5"/>

                <TextBlock x:Name="SubHeaderText"
                          Text="Confirm Exit"
                          FontSize="14"
                          Foreground="#80FFFFFF"
                          HorizontalAlignment="Center"
                          Visibility="Collapsed"/>
            </StackPanel>

            <!-- Message -->
            <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock x:Name="MessageText"
                          Text="هل تريد إغلاق التطبيق؟"
                          FontSize="16"
                          Foreground="#FFFFFF"
                          HorizontalAlignment="Center"
                          TextAlignment="Center"
                          Margin="0,0,0,8"/>

                <TextBlock x:Name="SubMessageText"
                          Text="Do you want to close the application?"
                          FontSize="14"
                          Foreground="#B0FFFFFF"
                          HorizontalAlignment="Center"
                          TextAlignment="Center"
                          Visibility="Collapsed"/>
            </StackPanel>

            <!-- Buttons -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,10">
                <!-- Yes Button -->
                <Button x:Name="YesButton"
                       Content="نعم - Yes"
                       Width="120" Height="40"
                       Background="#FF4444"
                       BorderThickness="0"
                       Foreground="White"
                       FontSize="14"
                       FontWeight="Bold"
                       Margin="0,0,15,0"
                       Cursor="Hand"
                       Click="YesButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="8"
                                               BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            <Border.Effect>
                                                <DropShadowEffect Color="#FF4444" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                                            </Border.Effect>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#FF6666"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#FF2222"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- No Button -->
                <Button x:Name="NoButton"
                       Content="لا - No"
                       Width="120" Height="40"
                       Background="#00D4AA"
                       BorderThickness="0"
                       Foreground="White"
                       FontSize="14"
                       FontWeight="Bold"
                       Cursor="Hand"
                       Click="NoButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                               CornerRadius="8"
                                               BorderThickness="0">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            <Border.Effect>
                                                <DropShadowEffect Color="#00D4AA" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                                            </Border.Effect>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#00E5BB"/>
                                            </Trigger>
                                            <Trigger Property="IsPressed" Value="True">
                                                <Setter Property="Background" Value="#00C399"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>
