﻿#pragma checksum "..\..\..\..\Views\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1F0EF76B05A1B46599FE2032BF1791F9B1BD0E80"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using SafeLink.Converters;
using SafeLink.Utils;
using SafeLink.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon MinimizeIcon;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon MaximizeIcon;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon CloseIcon;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WelcomeScreen;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas NetworkCanvas;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppName;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Tagline;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SecurityStatus;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ProgressFill;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse SecurityIndicator;
        
        #line default
        #line hidden
        
        
        #line 352 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainApplication;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas NotificationContainer;
        
        #line default
        #line hidden
        
        
        #line 397 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentUserText;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserRoleText;
        
        #line default
        #line hidden
        
        
        #line 409 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ConnectionStatusBorder;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ConnectionIndicator;
        
        #line default
        #line hidden
        
        
        #line 417 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionText;
        
        #line default
        #line hidden
        
        
        #line 422 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ConnectionToggleSwitch;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ToggleCircle;
        
        #line default
        #line hidden
        
        
        #line 448 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon ToggleIcon;
        
        #line default
        #line hidden
        
        
        #line 457 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ToggleText;
        
        #line default
        #line hidden
        
        
        #line 472 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 495 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon SettingsIcon;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportButton;
        
        #line default
        #line hidden
        
        
        #line 521 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon ReportIcon;
        
        #line default
        #line hidden
        
        
        #line 524 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DiagnosticButton;
        
        #line default
        #line hidden
        
        
        #line 547 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon DiagnosticIcon;
        
        #line default
        #line hidden
        
        
        #line 551 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LogoutButton;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon LogoutIcon;
        
        #line default
        #line hidden
        
        
        #line 585 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition UserListColumn;
        
        #line default
        #line hidden
        
        
        #line 622 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveUsersText;
        
        #line default
        #line hidden
        
        
        #line 625 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserCountText;
        
        #line default
        #line hidden
        
        
        #line 629 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox UserListBox;
        
        #line default
        #line hidden
        
        
        #line 727 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WelcomeMessage;
        
        #line default
        #line hidden
        
        
        #line 736 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeBrandText;
        
        #line default
        #line hidden
        
        
        #line 738 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeTaglineText;
        
        #line default
        #line hidden
        
        
        #line 751 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SystemReadyText;
        
        #line default
        #line hidden
        
        
        #line 755 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeSubText;
        
        #line default
        #line hidden
        
        
        #line 763 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ChatWindow;
        
        #line default
        #line hidden
        
        
        #line 780 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChatTitleText;
        
        #line default
        #line hidden
        
        
        #line 799 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MessagesScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 801 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MessagesPanel;
        
        #line default
        #line hidden
        
        
        #line 805 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.DragDropArea ChatDragDropArea;
        
        #line default
        #line hidden
        
        
        #line 813 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AttachmentsArea;
        
        #line default
        #line hidden
        
        
        #line 835 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AttachmentsCountText;
        
        #line default
        #line hidden
        
        
        #line 839 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAttachmentsButton;
        
        #line default
        #line hidden
        
        
        #line 851 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AttachmentsPanel;
        
        #line default
        #line hidden
        
        
        #line 867 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachmentButton;
        
        #line default
        #line hidden
        
        
        #line 908 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EmojiButton;
        
        #line default
        #line hidden
        
        
        #line 958 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MessageTextBox;
        
        #line default
        #line hidden
        
        
        #line 973 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/views/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 13 "..\..\..\..\Views\MainWindow.xaml"
            ((SafeLink.Views.MainWindow)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.MainWindow_KeyDown);
            
            #line default
            #line hidden
            
            #line 14 "..\..\..\..\Views\MainWindow.xaml"
            ((SafeLink.Views.MainWindow)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.MainWindow_Closing);
            
            #line default
            #line hidden
            
            #line 14 "..\..\..\..\Views\MainWindow.xaml"
            ((SafeLink.Views.MainWindow)(target)).StateChanged += new System.EventHandler(this.MainWindow_StateChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\Views\MainWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            
            #line 70 "..\..\..\..\Views\MainWindow.xaml"
            this.MinimizeButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.MinimizeButton_MouseEnter);
            
            #line default
            #line hidden
            
            #line 71 "..\..\..\..\Views\MainWindow.xaml"
            this.MinimizeButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.MinimizeButton_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MinimizeIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 4:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\..\Views\MainWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            
            #line 110 "..\..\..\..\Views\MainWindow.xaml"
            this.MaximizeButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.MaximizeButton_MouseEnter);
            
            #line default
            #line hidden
            
            #line 111 "..\..\..\..\Views\MainWindow.xaml"
            this.MaximizeButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.MaximizeButton_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 5:
            this.MaximizeIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 6:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 149 "..\..\..\..\Views\MainWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            
            #line 150 "..\..\..\..\Views\MainWindow.xaml"
            this.CloseButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.CloseButton_MouseEnter);
            
            #line default
            #line hidden
            
            #line 151 "..\..\..\..\Views\MainWindow.xaml"
            this.CloseButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.CloseButton_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CloseIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 8:
            this.WelcomeScreen = ((System.Windows.Controls.Grid)(target));
            return;
            case 9:
            this.NetworkCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 10:
            this.AppName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.Tagline = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SecurityStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ProgressFill = ((System.Windows.Controls.Border)(target));
            return;
            case 14:
            this.SecurityIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 15:
            this.MainApplication = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.NotificationContainer = ((System.Windows.Controls.Canvas)(target));
            return;
            case 17:
            this.CurrentUserText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.UserRoleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ConnectionStatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 20:
            this.ConnectionIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 21:
            this.ConnectionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.ConnectionToggleSwitch = ((System.Windows.Controls.Border)(target));
            
            #line 429 "..\..\..\..\Views\MainWindow.xaml"
            this.ConnectionToggleSwitch.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.ConnectionToggle_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ToggleCircle = ((System.Windows.Controls.Border)(target));
            return;
            case 24:
            this.ToggleIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 25:
            this.ToggleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 473 "..\..\..\..\Views\MainWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SettingsIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 28:
            this.ReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 499 "..\..\..\..\Views\MainWindow.xaml"
            this.ReportButton.Click += new System.Windows.RoutedEventHandler(this.ReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.ReportIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 30:
            this.DiagnosticButton = ((System.Windows.Controls.Button)(target));
            
            #line 525 "..\..\..\..\Views\MainWindow.xaml"
            this.DiagnosticButton.Click += new System.Windows.RoutedEventHandler(this.DiagnosticButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.DiagnosticIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 32:
            this.LogoutButton = ((System.Windows.Controls.Button)(target));
            
            #line 552 "..\..\..\..\Views\MainWindow.xaml"
            this.LogoutButton.Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            this.LogoutIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 34:
            this.UserListColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 35:
            this.ActiveUsersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.UserCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.UserListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 630 "..\..\..\..\Views\MainWindow.xaml"
            this.UserListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.UserListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 38:
            this.WelcomeMessage = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 39:
            this.WelcomeBrandText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.WelcomeTaglineText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.SystemReadyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 42:
            this.WelcomeSubText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 43:
            this.ChatWindow = ((System.Windows.Controls.Grid)(target));
            return;
            case 44:
            this.ChatTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            
            #line 786 "..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShareFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            
            #line 790 "..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseChatButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            this.MessagesScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 48:
            this.MessagesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 49:
            this.ChatDragDropArea = ((SafeLink.Controls.DragDropArea)(target));
            return;
            case 50:
            this.AttachmentsArea = ((System.Windows.Controls.Border)(target));
            return;
            case 51:
            this.AttachmentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.ClearAttachmentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 842 "..\..\..\..\Views\MainWindow.xaml"
            this.ClearAttachmentsButton.Click += new System.Windows.RoutedEventHandler(this.ClearAttachmentsButton_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            this.AttachmentsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 54:
            this.AttachmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 874 "..\..\..\..\Views\MainWindow.xaml"
            this.AttachmentButton.Click += new System.Windows.RoutedEventHandler(this.AttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.EmojiButton = ((System.Windows.Controls.Button)(target));
            
            #line 915 "..\..\..\..\Views\MainWindow.xaml"
            this.EmojiButton.Click += new System.Windows.RoutedEventHandler(this.EmojiButton_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.MessageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 965 "..\..\..\..\Views\MainWindow.xaml"
            this.MessageTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MessageTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 57:
            this.SendButton = ((System.Windows.Controls.Button)(target));
            
            #line 978 "..\..\..\..\Views\MainWindow.xaml"
            this.SendButton.Click += new System.Windows.RoutedEventHandler(this.SendButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

