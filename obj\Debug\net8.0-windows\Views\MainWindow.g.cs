﻿#pragma checksum "..\..\..\..\Views\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A44C2EC9566DA09F0125BC5D69D5B4A71B8D96E1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SafeLink.Controls;
using SafeLink.Converters;
using SafeLink.Utils;
using SafeLink.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SafeLink.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 62 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon CloseIcon;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WelcomeScreen;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas NetworkCanvas;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppName;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Tagline;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SecurityStatus;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ProgressFill;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse SecurityIndicator;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoginScreen;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas LoginNetworkCanvas;
        
        #line default
        #line hidden
        
        
        #line 346 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UsernameTextBox;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox PasswordBox;
        
        #line default
        #line hidden
        
        
        #line 357 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PasswordTextBox;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TogglePasswordButton;
        
        #line default
        #line hidden
        
        
        #line 409 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon EyeIcon;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberMeCheckBox;
        
        #line default
        #line hidden
        
        
        #line 448 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LoginButton;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RegisterButton;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviceIdText;
        
        #line default
        #line hidden
        
        
        #line 478 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainApplication;
        
        #line default
        #line hidden
        
        
        #line 494 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas NotificationContainer;
        
        #line default
        #line hidden
        
        
        #line 523 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentUserText;
        
        #line default
        #line hidden
        
        
        #line 526 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserRoleText;
        
        #line default
        #line hidden
        
        
        #line 535 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ConnectionStatusBorder;
        
        #line default
        #line hidden
        
        
        #line 538 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ConnectionIndicator;
        
        #line default
        #line hidden
        
        
        #line 543 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionText;
        
        #line default
        #line hidden
        
        
        #line 548 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ConnectionToggleSwitch;
        
        #line default
        #line hidden
        
        
        #line 562 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ToggleCircle;
        
        #line default
        #line hidden
        
        
        #line 574 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon ToggleIcon;
        
        #line default
        #line hidden
        
        
        #line 583 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ToggleText;
        
        #line default
        #line hidden
        
        
        #line 598 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 621 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon SettingsIcon;
        
        #line default
        #line hidden
        
        
        #line 624 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ReportButton;
        
        #line default
        #line hidden
        
        
        #line 647 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon ReportIcon;
        
        #line default
        #line hidden
        
        
        #line 650 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DiagnosticButton;
        
        #line default
        #line hidden
        
        
        #line 673 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon DiagnosticIcon;
        
        #line default
        #line hidden
        
        
        #line 677 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LogoutButton;
        
        #line default
        #line hidden
        
        
        #line 700 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.FeatherIcon LogoutIcon;
        
        #line default
        #line hidden
        
        
        #line 711 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ColumnDefinition UserListColumn;
        
        #line default
        #line hidden
        
        
        #line 748 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveUsersText;
        
        #line default
        #line hidden
        
        
        #line 751 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserCountText;
        
        #line default
        #line hidden
        
        
        #line 755 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox UserListBox;
        
        #line default
        #line hidden
        
        
        #line 853 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel WelcomeMessage;
        
        #line default
        #line hidden
        
        
        #line 862 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeBrandText;
        
        #line default
        #line hidden
        
        
        #line 864 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeTaglineText;
        
        #line default
        #line hidden
        
        
        #line 877 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SystemReadyText;
        
        #line default
        #line hidden
        
        
        #line 881 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WelcomeSubText;
        
        #line default
        #line hidden
        
        
        #line 889 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ChatWindow;
        
        #line default
        #line hidden
        
        
        #line 906 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChatTitleText;
        
        #line default
        #line hidden
        
        
        #line 925 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer MessagesScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 927 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MessagesPanel;
        
        #line default
        #line hidden
        
        
        #line 931 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal SafeLink.Controls.DragDropArea ChatDragDropArea;
        
        #line default
        #line hidden
        
        
        #line 939 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AttachmentsArea;
        
        #line default
        #line hidden
        
        
        #line 961 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AttachmentsCountText;
        
        #line default
        #line hidden
        
        
        #line 965 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearAttachmentsButton;
        
        #line default
        #line hidden
        
        
        #line 977 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AttachmentsPanel;
        
        #line default
        #line hidden
        
        
        #line 993 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachmentButton;
        
        #line default
        #line hidden
        
        
        #line 1034 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EmojiButton;
        
        #line default
        #line hidden
        
        
        #line 1084 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MessageTextBox;
        
        #line default
        #line hidden
        
        
        #line 1099 "..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SafeLink;V1.0.0.0;component/views/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 13 "..\..\..\..\Views\MainWindow.xaml"
            ((SafeLink.Views.MainWindow)(target)).KeyDown += new System.Windows.Input.KeyEventHandler(this.MainWindow_KeyDown);
            
            #line default
            #line hidden
            
            #line 14 "..\..\..\..\Views\MainWindow.xaml"
            ((SafeLink.Views.MainWindow)(target)).Closing += new System.ComponentModel.CancelEventHandler(this.MainWindow_Closing);
            
            #line default
            #line hidden
            
            #line 14 "..\..\..\..\Views\MainWindow.xaml"
            ((SafeLink.Views.MainWindow)(target)).StateChanged += new System.EventHandler(this.MainWindow_StateChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\Views\MainWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\..\Views\MainWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 92 "..\..\..\..\Views\MainWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            
            #line 93 "..\..\..\..\Views\MainWindow.xaml"
            this.CloseButton.MouseEnter += new System.Windows.Input.MouseEventHandler(this.CloseButton_MouseEnter);
            
            #line default
            #line hidden
            
            #line 94 "..\..\..\..\Views\MainWindow.xaml"
            this.CloseButton.MouseLeave += new System.Windows.Input.MouseEventHandler(this.CloseButton_MouseLeave);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CloseIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 6:
            this.WelcomeScreen = ((System.Windows.Controls.Grid)(target));
            return;
            case 7:
            this.NetworkCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 8:
            this.AppName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.Tagline = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SecurityStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ProgressFill = ((System.Windows.Controls.Border)(target));
            return;
            case 12:
            this.SecurityIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 13:
            this.LoginScreen = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.LoginNetworkCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 15:
            this.UsernameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 348 "..\..\..\..\Views\MainWindow.xaml"
            this.UsernameTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.UsernameTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 16:
            this.PasswordBox = ((System.Windows.Controls.PasswordBox)(target));
            
            #line 356 "..\..\..\..\Views\MainWindow.xaml"
            this.PasswordBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.PasswordBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 17:
            this.PasswordTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 359 "..\..\..\..\Views\MainWindow.xaml"
            this.PasswordTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.PasswordTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 18:
            this.TogglePasswordButton = ((System.Windows.Controls.Button)(target));
            
            #line 363 "..\..\..\..\Views\MainWindow.xaml"
            this.TogglePasswordButton.Click += new System.Windows.RoutedEventHandler(this.TogglePasswordButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.EyeIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 20:
            this.RememberMeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.LoginButton = ((System.Windows.Controls.Button)(target));
            return;
            case 22:
            this.RegisterButton = ((System.Windows.Controls.Button)(target));
            return;
            case 23:
            this.DeviceIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.MainApplication = ((System.Windows.Controls.Grid)(target));
            return;
            case 25:
            this.NotificationContainer = ((System.Windows.Controls.Canvas)(target));
            return;
            case 26:
            this.CurrentUserText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.UserRoleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.ConnectionStatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 29:
            this.ConnectionIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 30:
            this.ConnectionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.ConnectionToggleSwitch = ((System.Windows.Controls.Border)(target));
            
            #line 555 "..\..\..\..\Views\MainWindow.xaml"
            this.ConnectionToggleSwitch.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.ConnectionToggle_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.ToggleCircle = ((System.Windows.Controls.Border)(target));
            return;
            case 33:
            this.ToggleIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 34:
            this.ToggleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 599 "..\..\..\..\Views\MainWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.SettingsIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 37:
            this.ReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 625 "..\..\..\..\Views\MainWindow.xaml"
            this.ReportButton.Click += new System.Windows.RoutedEventHandler(this.ReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.ReportIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 39:
            this.DiagnosticButton = ((System.Windows.Controls.Button)(target));
            
            #line 651 "..\..\..\..\Views\MainWindow.xaml"
            this.DiagnosticButton.Click += new System.Windows.RoutedEventHandler(this.DiagnosticButton_Click);
            
            #line default
            #line hidden
            return;
            case 40:
            this.DiagnosticIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 41:
            this.LogoutButton = ((System.Windows.Controls.Button)(target));
            
            #line 678 "..\..\..\..\Views\MainWindow.xaml"
            this.LogoutButton.Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.LogoutIcon = ((SafeLink.Controls.FeatherIcon)(target));
            return;
            case 43:
            this.UserListColumn = ((System.Windows.Controls.ColumnDefinition)(target));
            return;
            case 44:
            this.ActiveUsersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 45:
            this.UserCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 46:
            this.UserListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 756 "..\..\..\..\Views\MainWindow.xaml"
            this.UserListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.UserListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 47:
            this.WelcomeMessage = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 48:
            this.WelcomeBrandText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.WelcomeTaglineText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.SystemReadyText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.WelcomeSubText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.ChatWindow = ((System.Windows.Controls.Grid)(target));
            return;
            case 53:
            this.ChatTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            
            #line 912 "..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShareFileButton_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            
            #line 916 "..\..\..\..\Views\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseChatButton_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.MessagesScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 57:
            this.MessagesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 58:
            this.ChatDragDropArea = ((SafeLink.Controls.DragDropArea)(target));
            return;
            case 59:
            this.AttachmentsArea = ((System.Windows.Controls.Border)(target));
            return;
            case 60:
            this.AttachmentsCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 61:
            this.ClearAttachmentsButton = ((System.Windows.Controls.Button)(target));
            
            #line 968 "..\..\..\..\Views\MainWindow.xaml"
            this.ClearAttachmentsButton.Click += new System.Windows.RoutedEventHandler(this.ClearAttachmentsButton_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            this.AttachmentsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 63:
            this.AttachmentButton = ((System.Windows.Controls.Button)(target));
            
            #line 1000 "..\..\..\..\Views\MainWindow.xaml"
            this.AttachmentButton.Click += new System.Windows.RoutedEventHandler(this.AttachmentButton_Click);
            
            #line default
            #line hidden
            return;
            case 64:
            this.EmojiButton = ((System.Windows.Controls.Button)(target));
            
            #line 1041 "..\..\..\..\Views\MainWindow.xaml"
            this.EmojiButton.Click += new System.Windows.RoutedEventHandler(this.EmojiButton_Click);
            
            #line default
            #line hidden
            return;
            case 65:
            this.MessageTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1091 "..\..\..\..\Views\MainWindow.xaml"
            this.MessageTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MessageTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 66:
            this.SendButton = ((System.Windows.Controls.Button)(target));
            
            #line 1104 "..\..\..\..\Views\MainWindow.xaml"
            this.SendButton.Click += new System.Windows.RoutedEventHandler(this.SendButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

