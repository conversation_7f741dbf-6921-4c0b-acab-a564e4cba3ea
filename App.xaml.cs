using System;
using System.Windows;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using System.Diagnostics;
using SafeLink.Core;
using SafeLink.Views;

namespace SafeLink
{
    public partial class App : Application
    {
        private DatabaseManager _databaseManager;
        private NetworkManager _networkManager;
        private static Mutex _mutex = null;

        private void Application_Startup(object sender, StartupEventArgs e)
        {
            // Check for single instance
            if (!CheckSingleInstance())
            {
                // Another instance is already running
                ShowExistingInstance();
                Shutdown();
                return;
            }

            StartApplication(e);
        }

        private bool CheckSingleInstance()
        {
            try
            {
                bool createdNew;
                // Use a more specific mutex name that includes the application path
                var mutexName = $"Global\\SafeLink_SingleInstance_{Environment.UserName}_{Environment.MachineName}";
                _mutex = new Mutex(true, mutexName, out createdNew);

                if (!createdNew)
                {
                    // Another instance is already running
                    System.Diagnostics.Debug.WriteLine("❌ Another SafeLink instance is already running");

                    // Try to release and recreate mutex to handle orphaned mutexes
                    try
                    {
                        _mutex?.ReleaseMutex();
                        _mutex?.Dispose();
                        _mutex = null;

                        // Wait a moment and try again
                        Thread.Sleep(100);
                        _mutex = new Mutex(true, mutexName, out createdNew);

                        if (!createdNew)
                        {
                            System.Diagnostics.Debug.WriteLine("❌ Confirmed: Another SafeLink instance is running");
                            return false;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("✅ Orphaned mutex cleared, proceeding with startup");
                            return true;
                        }
                    }
                    catch
                    {
                        return false;
                    }
                }

                System.Diagnostics.Debug.WriteLine("✅ SafeLink single instance check passed");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in single instance check: {ex.Message}");
                // If there's an error, allow the application to start
                return true;
            }
        }

        private void ShowExistingInstance()
        {
            try
            {
                // Find existing SafeLink processes
                var currentProcess = Process.GetCurrentProcess();
                var processes = Process.GetProcessesByName("SafeLink");

                System.Diagnostics.Debug.WriteLine($"🔍 Found {processes.Length} SafeLink processes (Current PID: {currentProcess.Id})");

                foreach (var process in processes)
                {
                    if (process.Id != currentProcess.Id)
                    {
                        System.Diagnostics.Debug.WriteLine($"🎯 Attempting to show existing instance (PID: {process.Id})");

                        // Found existing instance, try to bring it to front
                        var handle = process.MainWindowHandle;
                        if (handle != IntPtr.Zero)
                        {
                            // Show and activate the existing window
                            ShowWindow(handle, SW_RESTORE);
                            SetForegroundWindow(handle);
                            System.Diagnostics.Debug.WriteLine("✅ Existing SafeLink window activated");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("⚠️ Existing SafeLink process found but no main window handle");

                            // Try to wait a bit for the window to be created
                            for (int i = 0; i < 10; i++)
                            {
                                Thread.Sleep(500);
                                process.Refresh();
                                handle = process.MainWindowHandle;
                                if (handle != IntPtr.Zero)
                                {
                                    ShowWindow(handle, SW_RESTORE);
                                    SetForegroundWindow(handle);
                                    System.Diagnostics.Debug.WriteLine($"✅ Existing SafeLink window activated after {i + 1} attempts");
                                    break;
                                }
                            }
                        }
                        break;
                    }
                }

                // Clean up process references
                foreach (var process in processes)
                {
                    process.Dispose();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error showing existing instance: {ex.Message}");
            }
        }

        // Windows API imports for window management
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        private const int SW_RESTORE = 9;

        private void StartApplication(StartupEventArgs e)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🚀 Starting SafeLink application...");

                // Set up global exception handling first
                SetupExceptionHandling();

                // Initialize core services
                System.Diagnostics.Debug.WriteLine("🔧 Initializing services...");
                InitializeServices();

                System.Diagnostics.Debug.WriteLine("🔐 Creating login window...");
                // Show login window directly
                var loginWindow = new LoginWindow();
                loginWindow.LoadSavedCredentials();

                // Set as main window
                MainWindow = loginWindow;
                loginWindow.Show();

                System.Diagnostics.Debug.WriteLine("✅ Login window shown successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"🔴 CRITICAL ERROR: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"🔴 Stack trace: {ex.StackTrace}");

                // Show error in a simple MessageBox first
                try
                {
                    MessageBox.Show($"خطأ في بدء تشغيل SafeLink:\n\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}",
                        "SafeLink Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                catch
                {
                    // If even MessageBox fails, try a simpler message
                    try
                    {
                        MessageBox.Show($"Critical Error: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    catch
                    {
                        // Last resort - do nothing and let app exit
                    }
                }

                Shutdown();
            }
        }

        private void InitializeServices()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📁 Creating data directory...");
                // Create data directory if it doesn't exist
                var dataPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataPath))
                {
                    Directory.CreateDirectory(dataPath);
                    System.Diagnostics.Debug.WriteLine($"✅ Data directory created: {dataPath}");
                }

                System.Diagnostics.Debug.WriteLine("🗄️ Initializing database...");
                // Initialize database
                _databaseManager = new DatabaseManager();
                _databaseManager.Initialize();
                System.Diagnostics.Debug.WriteLine("✅ Database initialized successfully");

                System.Diagnostics.Debug.WriteLine("🌐 Initializing network manager...");
                // Initialize network manager
                _networkManager = new NetworkManager();
                _networkManager.Initialize();
                System.Diagnostics.Debug.WriteLine("✅ Network manager initialized successfully");

                System.Diagnostics.Debug.WriteLine("🔥 Checking firewall rules...");
                // تحقق من قواعد جدار الحماية عند البدء
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // تحقق من الحالة الحالية
                        var status = await FirewallManager.GetFirewallStatusAsync();
                        System.Diagnostics.Debug.WriteLine(status);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ Error checking firewall: {ex.Message}");
                    }
                });

                // Store in application properties for global access
                Current.Properties["DatabaseManager"] = _databaseManager;
                Current.Properties["NetworkManager"] = _networkManager;
                System.Diagnostics.Debug.WriteLine("✅ Services stored in application properties");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"🔴 Service initialization error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"🔴 Stack trace: {ex.StackTrace}");
                throw new Exception($"Failed to initialize services: {ex.Message}", ex);
            }
        }

        private void SetupExceptionHandling()
        {
            // Handle unhandled exceptions
            DispatcherUnhandledException += (sender, e) =>
            {
                MessageBox.Show($"An unexpected error occurred: {e.Exception.Message}",
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                e.Handled = true;
            };

            // Handle unhandled exceptions in background threads
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                var exception = e.ExceptionObject as Exception;
                MessageBox.Show($"A critical error occurred: {exception?.Message}",
                    "Critical Error", MessageBoxButton.OK, MessageBoxImage.Error);
            };
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // Clean up resources
                _networkManager?.Dispose();
                _databaseManager?.Dispose();

                // Release mutex
                if (_mutex != null)
                {
                    _mutex.ReleaseMutex();
                    _mutex.Dispose();
                    _mutex = null;
                }
            }
            catch (Exception ex)
            {
                // Log error but don't prevent shutdown
                System.Diagnostics.Debug.WriteLine($"Error during shutdown: {ex.Message}");
            }

            base.OnExit(e);
        }

        public static DatabaseManager GetDatabaseManager()
        {
            return Current.Properties["DatabaseManager"] as DatabaseManager;
        }

        public static NetworkManager GetNetworkManager()
        {
            return Current.Properties["NetworkManager"] as NetworkManager;
        }
    }
}
