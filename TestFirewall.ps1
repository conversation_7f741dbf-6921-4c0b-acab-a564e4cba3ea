# SafeLink Firewall Rules Test Script
# تحقق من قواعد جدار الحماية لـ SafeLink

Write-Host "🔥 SafeLink Firewall Rules Test" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# قائمة القواعد المطلوبة
$rules = @(
    "SafeLink_UDP_Inbound",
    "SafeLink_UDP_Outbound", 
    "SafeLink_TCP_Inbound",
    "SafeLink_TCP_Outbound"
)

Write-Host "📋 Checking firewall rules..." -ForegroundColor Yellow
Write-Host ""

foreach ($rule in $rules) {
    try {
        $result = netsh advfirewall firewall show rule name="$rule" 2>$null
        
        if ($result -match "No rules match the specified criteria") {
            Write-Host "❌ $rule - NOT FOUND" -ForegroundColor Red
        } else {
            Write-Host "✅ $rule - ACTIVE" -ForegroundColor Green
            
            # استخراج تفاصيل القاعدة
            if ($result -match "Direction:\s+(\w+)") {
                $direction = $matches[1]
                Write-Host "   Direction: $direction" -ForegroundColor Gray
            }

            if ($result -match "Protocol:\s+(\w+)") {
                $protocol = $matches[1]
                Write-Host "   Protocol: $protocol" -ForegroundColor Gray
            }

            if ($result -match "LocalPort:\s+([\d\-,]+)") {
                $ports = $matches[1]
                Write-Host "   Ports: $ports" -ForegroundColor Gray
            }
        }
        Write-Host ""
    }
    catch {
        Write-Host "❌ Error checking $rule : $($_.Exception.Message)" -ForegroundColor Red
        Write-Host ""
    }
}

Write-Host "🌐 Network Status:" -ForegroundColor Cyan
Write-Host "=================" -ForegroundColor Cyan

# تحقق من البورتات النشطة (4 أرقام!)
Write-Host "📡 Checking SafeLink ports (UDP)..." -ForegroundColor Yellow
$udpPorts = netstat -an | findstr "UDP" | findstr ":888"
if ($udpPorts) {
    Write-Host "✅ SafeLink UDP ports found:" -ForegroundColor Green
    $udpPorts | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ No SafeLink UDP ports found" -ForegroundColor Red
}
Write-Host ""

Write-Host "📡 Checking SafeLink ports (TCP)..." -ForegroundColor Yellow
$tcpPorts = netstat -an | findstr "TCP" | findstr ":888"
if ($tcpPorts) {
    Write-Host "✅ SafeLink TCP ports found:" -ForegroundColor Green
    $tcpPorts | ForEach-Object { Write-Host "   $_" -ForegroundColor Gray }
} else {
    Write-Host "❌ No SafeLink TCP ports found" -ForegroundColor Red
}
Write-Host ""

# تحقق من عمليات SafeLink
Write-Host "🚀 SafeLink Processes:" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
$processes = Get-Process | Where-Object {$_.ProcessName -like "*SafeLink*"}
if ($processes) {
    $processes | Format-Table ProcessName, Id, WorkingSet, StartTime -AutoSize
} else {
    Write-Host "❌ No SafeLink processes found" -ForegroundColor Red
}
Write-Host ""

# تحقق من حالة جدار الحماية العامة
Write-Host "🛡️ Windows Firewall Status:" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan
try {
    $firewallStatus = netsh advfirewall show allprofiles state
    Write-Host $firewallStatus -ForegroundColor Gray
} catch {
    Write-Host "❌ Error checking firewall status" -ForegroundColor Red
}
Write-Host ""

Write-Host "🎯 Summary:" -ForegroundColor Cyan
Write-Host "==========" -ForegroundColor Cyan

$foundRules = 0
foreach ($rule in $rules) {
    $result = netsh advfirewall firewall show rule name="$rule" 2>$null
    if (-not ($result -match "No rules match the specified criteria")) {
        $foundRules++
    }
}

Write-Host "📊 Firewall Rules: $foundRules/4 found" -ForegroundColor $(if ($foundRules -eq 4) { "Green" } else { "Yellow" })

if ($udpPorts) {
    Write-Host "📡 UDP Network: ✅ Active" -ForegroundColor Green
} else {
    Write-Host "📡 UDP Network: ❌ Inactive" -ForegroundColor Red
}

if ($processes) {
    Write-Host "🚀 SafeLink Process: ✅ Running" -ForegroundColor Green
} else {
    Write-Host "🚀 SafeLink Process: ❌ Not Running" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 Recommendations:" -ForegroundColor Cyan
Write-Host "===================" -ForegroundColor Cyan

if ($foundRules -lt 4) {
    Write-Host "⚠️  Missing firewall rules detected!" -ForegroundColor Yellow
    Write-Host "   Solution: Open SafeLink Developer Panel (Ctrl+Shift+D)" -ForegroundColor Gray
    Write-Host "   Click 'Add Firewall Rules' button" -ForegroundColor Gray
}

if (-not $udpPorts -and $processes) {
    Write-Host "⚠️  SafeLink running but no network activity" -ForegroundColor Yellow
    Write-Host "   Solution: Try logging in to start network discovery" -ForegroundColor Gray
}

if (-not $processes) {
    Write-Host "⚠️  SafeLink not running" -ForegroundColor Yellow
    Write-Host "   Solution: Run 'dotnet run --project SafeLink.csproj'" -ForegroundColor Gray
}

Write-Host ""
Write-Host "✅ Firewall test completed!" -ForegroundColor Green
