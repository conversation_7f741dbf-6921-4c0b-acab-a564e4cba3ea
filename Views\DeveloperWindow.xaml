<Window x:Class="SafeLink.Views.DeveloperWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:SafeLink.Controls"
        Title="SafeLink Developer Panel"
        Height="700" Width="900"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        WindowStartupLocation="CenterOwner"
        Icon="pack://application:,,,/Icons/SafeLink.ico">

    <Window.Resources>
        <!-- Professional Security Gradient -->
        <LinearGradientBrush x:Key="SecurityGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#0D1421" Offset="0"/>
            <GradientStop Color="#1A2332" Offset="0.3"/>
            <GradientStop Color="#243447" Offset="0.7"/>
            <GradientStop Color="#2C4A5C" Offset="1"/>
        </LinearGradientBrush>

        <!-- Accent Colors -->
        <SolidColorBrush x:Key="PrimaryAccent" Color="#00E5FF"/>
        <SolidColorBrush x:Key="SecondaryAccent" Color="#1DE9B6"/>
        <SolidColorBrush x:Key="WarningAccent" Color="#FFB74D"/>
        <SolidColorBrush x:Key="DangerAccent" Color="#FF5252"/>
        <SolidColorBrush x:Key="SuccessAccent" Color="#4CAF50"/>

        <!-- Professional Button Style -->
        <Style x:Key="ProfessionalButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryAccent}"/>
            <Setter Property="Foreground" Value="#0D1421"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="6">
                            <Border.Effect>
                                <DropShadowEffect Color="#00E5FF" BlurRadius="8" ShadowDepth="0" Opacity="0.4"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryAccent}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional TabControl Style -->
        <Style x:Key="ProfessionalTabControlStyle" TargetType="TabControl">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
        </Style>

        <!-- Professional TabItem Style -->
        <Style x:Key="ProfessionalTabItemStyle" TargetType="TabItem">
            <Setter Property="Background" Value="#20FFFFFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TabItem">
                        <Border Background="{TemplateBinding Background}" CornerRadius="8,8,0,0"
                               BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1,1,1,0">
                            <ContentPresenter ContentSource="Header" HorizontalAlignment="Center"
                                            VerticalAlignment="Center" Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{StaticResource PrimaryAccent}"/>
                                <Setter Property="Foreground" Value="#0D1421"/>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#30FFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="{StaticResource SecurityGradient}" CornerRadius="15"
           BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect Color="#00E5FF" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
        </Border.Effect>

        <Grid Margin="25">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Professional Header -->
            <Border Grid.Row="0" Background="{StaticResource PrimaryAccent}"
                   Padding="20,15" CornerRadius="10" Margin="0,0,0,25">
                <Border.Effect>
                    <DropShadowEffect Color="#00E5FF" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                </Border.Effect>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <controls:FeatherIcon IconName="tool" IconColor="#0D1421"
                                        Width="28" Height="28" Margin="0,0,12,0"/>
                    <TextBlock Text="SafeLink Developer Panel" FontSize="20" FontWeight="SemiBold"
                              Foreground="#0D1421" VerticalAlignment="Center"/>
                </StackPanel>
            </Border>

            <!-- Professional Content -->
            <TabControl Grid.Row="1" Style="{StaticResource ProfessionalTabControlStyle}">

                <!-- User Management Tab -->
                <TabItem Style="{StaticResource ProfessionalTabItemStyle}">
                    <TabItem.Header>
                        <StackPanel Orientation="Horizontal">
                            <controls:FeatherIcon IconName="users" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                            <TextBlock Text="Users" VerticalAlignment="Center"/>
                        </StackPanel>
                    </TabItem.Header>
                    <Border Background="#15FFFFFF" BorderBrush="{StaticResource PrimaryAccent}"
                           BorderThickness="1,0,1,1" CornerRadius="0,0,8,8" Padding="20">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                                <Button x:Name="RefreshUsersButton" Style="{StaticResource ProfessionalButtonStyle}"
                                       Click="RefreshUsersButton_Click" Width="160" Margin="0,0,15,0">
                                    <StackPanel Orientation="Horizontal">
                                        <controls:FeatherIcon IconName="refresh-cw" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="Refresh Users" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                                <Button x:Name="CreateUserButton" Background="{StaticResource WarningAccent}"
                                       Style="{StaticResource ProfessionalButtonStyle}"
                                       Click="CreateUserButton_Click" Width="160">
                                    <StackPanel Orientation="Horizontal">
                                        <controls:FeatherIcon IconName="user-plus" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="Create User" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>

                            <Border Grid.Row="1" Background="#20FFFFFF" BorderBrush="{StaticResource SecondaryAccent}"
                                   BorderThickness="1" CornerRadius="8">
                                <DataGrid x:Name="UsersDataGrid"
                                         Background="Transparent" Foreground="White"
                                         BorderThickness="0"
                                         AutoGenerateColumns="False" CanUserAddRows="False"
                                         GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                         FontSize="12">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60"/>
                                        <DataGridTextColumn Header="Username" Binding="{Binding Username}" Width="150"/>
                                        <DataGridTextColumn Header="Role" Binding="{Binding Role}" Width="100"/>
                                        <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="100"/>
                                        <DataGridTextColumn Header="Last Login" Binding="{Binding LastLogin}" Width="150"/>
                                        <DataGridTemplateColumn Header="Actions" Width="200">
                                            <DataGridTemplateColumn.CellTemplate>
                                                <DataTemplate>
                                                    <StackPanel Orientation="Horizontal">
                                                        <Button Background="{StaticResource WarningAccent}"
                                                               Foreground="#0D1421" FontWeight="SemiBold"
                                                               Click="ResetDeviceButton_Click" ToolTip="Reset Device"
                                                               Tag="{Binding Id}" Width="35" Height="30" Margin="2"
                                                               BorderThickness="0">
                                                            <controls:FeatherIcon IconName="refresh-cw" IconColor="#0D1421" Width="14" Height="14"/>
                                                        </Button>
                                                        <Button Background="{StaticResource DangerAccent}"
                                                               Foreground="White" FontWeight="SemiBold"
                                                               Click="DeleteUserButton_Click" ToolTip="Delete User"
                                                               Tag="{Binding Id}" Width="35" Height="30" Margin="2"
                                                               BorderThickness="0">
                                                            <controls:FeatherIcon IconName="trash-2" IconColor="White" Width="14" Height="14"/>
                                                        </Button>
                                                    </StackPanel>
                                                </DataTemplate>
                                            </DataGridTemplateColumn.CellTemplate>
                                        </DataGridTemplateColumn>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Border>
                        </Grid>
                    </Border>
                </TabItem>

            <!-- System Monitoring Tab -->
            <TabItem Style="{StaticResource ProfessionalTabItemStyle}">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="activity" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="System" VerticalAlignment="Center"/>
                    </StackPanel>
                </TabItem.Header>
                <Border Background="#15FFFFFF" BorderBrush="{StaticResource PrimaryAccent}"
                       BorderThickness="1,0,1,1" CornerRadius="0,0,8,8" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
                            <Button x:Name="RefreshSystemButton" Style="{StaticResource ProfessionalButtonStyle}"
                                   Click="RefreshSystemButton_Click" Width="180" Margin="0,0,15,0">
                                <StackPanel Orientation="Horizontal">
                                    <controls:FeatherIcon IconName="refresh-cw" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="Refresh System Info" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="CheckFirewallButton" Style="{StaticResource ProfessionalButtonStyle}"
                                   Click="CheckFirewallButton_Click" Width="160" Margin="0,0,15,0">
                                <StackPanel Orientation="Horizontal">
                                    <controls:FeatherIcon IconName="shield" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="Check Firewall" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="AddFirewallRulesButton" Style="{StaticResource ProfessionalButtonStyle}"
                                   Click="AddFirewallRulesButton_Click" Width="180">
                                <StackPanel Orientation="Horizontal">
                                    <controls:FeatherIcon IconName="shield-check" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                    <TextBlock Text="Add Firewall Rules" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>

                        <!-- Professional System Stats -->
                        <UniformGrid Grid.Row="1" Columns="4" Margin="0,0,0,25">
                            <Border Background="#20FFFFFF" BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1"
                                   CornerRadius="8" Margin="5" Padding="15">
                                <Border.Effect>
                                    <DropShadowEffect Color="#00E5FF" BlurRadius="6" ShadowDepth="0" Opacity="0.3"/>
                                </Border.Effect>
                                <StackPanel HorizontalAlignment="Center">
                                    <controls:FeatherIcon IconName="users" IconColor="{StaticResource PrimaryAccent}"
                                                        Width="24" Height="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                    <TextBlock Text="Connected Users" Foreground="{StaticResource PrimaryAccent}"
                                              FontWeight="Medium" HorizontalAlignment="Center" FontSize="11"/>
                                    <TextBlock x:Name="ConnectedUsersText" Text="0"
                                              Foreground="White" FontSize="18" FontWeight="Bold"
                                              HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>

                            <Border Background="#20FFFFFF" BorderBrush="{StaticResource SecondaryAccent}" BorderThickness="1"
                                   CornerRadius="8" Margin="5" Padding="15">
                                <Border.Effect>
                                    <DropShadowEffect Color="#1DE9B6" BlurRadius="6" ShadowDepth="0" Opacity="0.3"/>
                                </Border.Effect>
                                <StackPanel HorizontalAlignment="Center">
                                    <controls:FeatherIcon IconName="clock" IconColor="{StaticResource SecondaryAccent}"
                                                        Width="24" Height="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                    <TextBlock Text="Uptime" Foreground="{StaticResource SecondaryAccent}"
                                              FontWeight="Medium" HorizontalAlignment="Center" FontSize="11"/>
                                    <TextBlock x:Name="UptimeText" Text="--"
                                              Foreground="White" FontSize="12" FontWeight="Bold"
                                              HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>

                            <Border Background="#20FFFFFF" BorderBrush="{StaticResource WarningAccent}" BorderThickness="1"
                                   CornerRadius="8" Margin="5" Padding="15">
                                <Border.Effect>
                                    <DropShadowEffect Color="#FFB74D" BlurRadius="6" ShadowDepth="0" Opacity="0.3"/>
                                </Border.Effect>
                                <StackPanel HorizontalAlignment="Center">
                                    <controls:FeatherIcon IconName="hard-drive" IconColor="{StaticResource WarningAccent}"
                                                        Width="24" Height="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                    <TextBlock Text="Memory" Foreground="{StaticResource WarningAccent}"
                                              FontWeight="Medium" HorizontalAlignment="Center" FontSize="11"/>
                                    <TextBlock x:Name="MemoryUsageText" Text="--"
                                              Foreground="White" FontSize="12" FontWeight="Bold"
                                              HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>

                            <Border Background="#20FFFFFF" BorderBrush="{StaticResource DangerAccent}" BorderThickness="1"
                                   CornerRadius="8" Margin="5" Padding="15">
                                <Border.Effect>
                                    <DropShadowEffect Color="#FF5252" BlurRadius="6" ShadowDepth="0" Opacity="0.3"/>
                                </Border.Effect>
                                <StackPanel HorizontalAlignment="Center">
                                    <controls:FeatherIcon IconName="cpu" IconColor="{StaticResource DangerAccent}"
                                                        Width="24" Height="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                    <TextBlock Text="CPU" Foreground="{StaticResource DangerAccent}"
                                              FontWeight="Medium" HorizontalAlignment="Center" FontSize="11"/>
                                    <TextBlock x:Name="CpuUsageText" Text="--"
                                              Foreground="White" FontSize="12" FontWeight="Bold"
                                              HorizontalAlignment="Center" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>
                        </UniformGrid>

                        <!-- Professional System Details -->
                        <Border Grid.Row="2" Background="#20FFFFFF" BorderBrush="{StaticResource SecondaryAccent}"
                               BorderThickness="1" CornerRadius="8">
                            <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="15">
                                <StackPanel x:Name="SystemDetailsPanel"/>
                            </ScrollViewer>
                        </Border>
                    </Grid>
                </Border>
            </TabItem>

            <!-- Remote Control Tab -->
            <TabItem Style="{StaticResource ProfessionalTabItemStyle}">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="monitor" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Remote Control" VerticalAlignment="Center"/>
                    </StackPanel>
                </TabItem.Header>
                <Border Background="#15FFFFFF" BorderBrush="{StaticResource PrimaryAccent}"
                       BorderThickness="1,0,1,1" CornerRadius="0,0,8,8" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Device Selection -->
                        <Border Grid.Row="0" Background="#20FFFFFF" BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1"
                               CornerRadius="8" Padding="15" Margin="0,0,0,20">
                            <StackPanel>
                                <TextBlock Text="Select Target Device" FontWeight="SemiBold" Foreground="White" FontSize="14" Margin="0,0,0,10"/>
                                <ComboBox x:Name="TargetDeviceComboBox" Background="#1A1A2E" Foreground="White"
                                         BorderBrush="{StaticResource PrimaryAccent}" Height="35" FontSize="12"/>
                                <Button x:Name="RefreshDevicesButton" Style="{StaticResource ProfessionalButtonStyle}"
                                       Click="RefreshDevicesButton_Click" Width="150" HorizontalAlignment="Left" Margin="0,10,0,0">
                                    <StackPanel Orientation="Horizontal">
                                        <controls:FeatherIcon IconName="refresh-cw" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="Refresh Devices" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Button>
                            </StackPanel>
                        </Border>

                        <!-- Command Execution -->
                        <Border Grid.Row="1" Background="#20FFFFFF" BorderBrush="{StaticResource WarningAccent}" BorderThickness="1"
                               CornerRadius="8" Padding="15" Margin="0,0,0,20">
                            <StackPanel>
                                <TextBlock Text="Remote Command Execution" FontWeight="SemiBold" Foreground="White" FontSize="14" Margin="0,0,0,10"/>
                                <ComboBox x:Name="CommandTypeComboBox" Background="#1A1A2E" Foreground="White"
                                         BorderBrush="{StaticResource WarningAccent}" Height="35" FontSize="12" Margin="0,0,0,10">
                                    <ComboBoxItem Content="PowerShell Command"/>
                                    <ComboBoxItem Content="CMD Command"/>
                                    <ComboBoxItem Content="System Restart"/>
                                    <ComboBoxItem Content="System Shutdown"/>
                                    <ComboBoxItem Content="Get System Info"/>
                                    <ComboBoxItem Content="Get Process List"/>
                                    <ComboBoxItem Content="Kill Process"/>
                                </ComboBox>
                                <TextBox x:Name="CommandTextBox" Background="#1A1A2E" Foreground="White"
                                        BorderBrush="{StaticResource WarningAccent}" Height="35" FontSize="12"
                                        Margin="0,0,0,10" Text="Enter command here..."/>
                                <StackPanel Orientation="Horizontal">
                                    <Button x:Name="ExecuteCommandButton" Background="{StaticResource DangerAccent}"
                                           Style="{StaticResource ProfessionalButtonStyle}"
                                           Click="ExecuteCommandButton_Click" Width="150" Margin="0,0,10,0">
                                        <StackPanel Orientation="Horizontal">
                                            <controls:FeatherIcon IconName="play" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="Execute" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                    <Button x:Name="ClearOutputButton" Style="{StaticResource ProfessionalButtonStyle}"
                                           Click="ClearOutputButton_Click" Width="120">
                                        <StackPanel Orientation="Horizontal">
                                            <controls:FeatherIcon IconName="trash-2" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="Clear" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Command Output -->
                        <Border Grid.Row="2" Background="#20FFFFFF" BorderBrush="{StaticResource SecondaryAccent}" BorderThickness="1"
                               CornerRadius="8" Padding="15">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <TextBlock x:Name="CommandOutputTextBlock" Background="#0D1421" Foreground="#1DE9B6"
                                          FontFamily="Consolas" FontSize="11" Padding="10" TextWrapping="Wrap"
                                          Text="Command output will appear here..."/>
                            </ScrollViewer>
                        </Border>
                    </Grid>
                </Border>
            </TabItem>

            <!-- Problem Reports Tab -->
            <TabItem Style="{StaticResource ProfessionalTabItemStyle}">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="alert-triangle" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Reports" VerticalAlignment="Center"/>
                    </StackPanel>
                </TabItem.Header>
                <Border Background="#15FFFFFF" BorderBrush="{StaticResource PrimaryAccent}"
                       BorderThickness="1,0,1,1" CornerRadius="0,0,8,8" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Button Grid.Row="0" x:Name="RefreshReportsButton" Style="{StaticResource ProfessionalButtonStyle}"
                               Click="RefreshReportsButton_Click" Width="160" HorizontalAlignment="Left" Margin="0,0,0,20">
                            <StackPanel Orientation="Horizontal">
                                <controls:FeatherIcon IconName="refresh-cw" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                <TextBlock Text="Refresh Reports" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <Border Grid.Row="1" Background="#20FFFFFF" BorderBrush="{StaticResource WarningAccent}"
                               BorderThickness="1" CornerRadius="8">
                            <DataGrid x:Name="ReportsDataGrid"
                                     Background="Transparent" Foreground="White"
                                     BorderThickness="0"
                                     AutoGenerateColumns="False" CanUserAddRows="False"
                                     GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                     FontSize="12">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="50"/>
                                    <DataGridTextColumn Header="User" Binding="{Binding Username}" Width="100"/>
                                    <DataGridTextColumn Header="Title" Binding="{Binding Title}" Width="200"/>
                                    <DataGridTextColumn Header="Priority" Binding="{Binding Priority}" Width="80"/>
                                    <DataGridTextColumn Header="Status" Binding="{Binding Status}" Width="80"/>
                                    <DataGridTextColumn Header="Date" Binding="{Binding ReportDate}" Width="120"/>
                                    <DataGridTemplateColumn Header="Actions" Width="150">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal">
                                                    <Button Background="{StaticResource PrimaryAccent}"
                                                           Foreground="#0D1421" FontWeight="SemiBold"
                                                           Click="ViewReportButton_Click" ToolTip="View Report"
                                                           Tag="{Binding Id}" Width="35" Height="30" Margin="2"
                                                           BorderThickness="0">
                                                        <controls:FeatherIcon IconName="eye" IconColor="#0D1421" Width="14" Height="14"/>
                                                    </Button>
                                                    <Button Background="{StaticResource SuccessAccent}"
                                                           Foreground="White" FontWeight="SemiBold"
                                                           Click="ResolveReportButton_Click" ToolTip="Resolve"
                                                           Tag="{Binding Id}" Width="35" Height="30" Margin="2"
                                                           BorderThickness="0">
                                                        <controls:FeatherIcon IconName="check" IconColor="White" Width="14" Height="14"/>
                                                    </Button>
                                                </StackPanel>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>
                    </Grid>
                </Border>
            </TabItem>

            <!-- Update Management Tab -->
            <TabItem Style="{StaticResource ProfessionalTabItemStyle}">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <controls:FeatherIcon IconName="package" IconColor="White" Width="16" Height="16" Margin="0,0,8,0"/>
                        <TextBlock Text="Updates" VerticalAlignment="Center"/>
                    </StackPanel>
                </TabItem.Header>
                <Border Background="#15FFFFFF" BorderBrush="{StaticResource PrimaryAccent}"
                       BorderThickness="1,0,1,1" CornerRadius="0,0,8,8" Padding="20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Professional Update Controls -->
                        <Border Grid.Row="0" Background="#20FFFFFF" BorderBrush="{StaticResource PrimaryAccent}" BorderThickness="1"
                               CornerRadius="8" Padding="20" Margin="0,0,0,20">
                            <Border.Effect>
                                <DropShadowEffect Color="#00E5FF" BlurRadius="8" ShadowDepth="0" Opacity="0.3"/>
                            </Border.Effect>
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <controls:FeatherIcon IconName="package" IconColor="{StaticResource PrimaryAccent}"
                                                        Width="24" Height="24" Margin="0,0,12,0"/>
                                    <TextBlock Text="Update Management" FontSize="16" FontWeight="SemiBold"
                                              Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>

                                <TextBlock Text="Send update commands to all connected devices"
                                          Foreground="#B0BEC5" Margin="0,0,0,20" FontSize="12"/>

                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <Button Style="{StaticResource ProfessionalButtonStyle}"
                                           Click="CreateUpdatePackage_Click" Width="220" Margin="0,0,15,0">
                                        <StackPanel Orientation="Horizontal">
                                            <controls:FeatherIcon IconName="package" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="Create Update Package" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>

                                    <Button Background="{StaticResource WarningAccent}" Style="{StaticResource ProfessionalButtonStyle}"
                                           Click="SendUpdateCommand_Click" Width="220">
                                        <StackPanel Orientation="Horizontal">
                                            <controls:FeatherIcon IconName="send" IconColor="#0D1421" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="Send Update Command" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>

                                <Border Background="#20FFB74D" BorderBrush="{StaticResource WarningAccent}" BorderThickness="1"
                                       CornerRadius="6" Padding="12,8">
                                    <StackPanel Orientation="Horizontal">
                                        <controls:FeatherIcon IconName="alert-triangle" IconColor="{StaticResource WarningAccent}"
                                                            Width="16" Height="16" Margin="0,0,8,0"/>
                                        <TextBlock Text="Create package first, then send update command to all devices"
                                                  Foreground="{StaticResource WarningAccent}" FontSize="11" FontWeight="Medium"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>

                        <!-- Professional Update Status -->
                        <Border Grid.Row="1" Background="#20FFFFFF" BorderBrush="{StaticResource SecondaryAccent}" BorderThickness="1"
                               CornerRadius="8" Padding="20">
                            <Border.Effect>
                                <DropShadowEffect Color="#1DE9B6" BlurRadius="8" ShadowDepth="0" Opacity="0.3"/>
                            </Border.Effect>
                            <StackPanel>
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <controls:FeatherIcon IconName="info" IconColor="{StaticResource SecondaryAccent}"
                                                        Width="24" Height="24" Margin="0,0,12,0"/>
                                    <TextBlock Text="Update Status" FontSize="16" FontWeight="SemiBold"
                                              Foreground="White" VerticalAlignment="Center"/>
                                </StackPanel>

                                <Grid Margin="0,0,0,20">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>

                                    <TextBlock Grid.Row="0" Grid.Column="0" Text="Current Version:" Foreground="#B0BEC5" FontSize="12" Margin="0,0,15,8"/>
                                    <TextBlock Grid.Row="0" Grid.Column="1" Text="1.0.0" Foreground="White" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <TextBlock Grid.Row="1" Grid.Column="0" Text="Update Folder:" Foreground="#B0BEC5" FontSize="12" Margin="0,0,15,8"/>
                                    <TextBlock Grid.Row="1" Grid.Column="1" Text="./Update" Foreground="{StaticResource PrimaryAccent}" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <TextBlock Grid.Row="2" Grid.Column="0" Text="Network Port:" Foreground="#B0BEC5" FontSize="12" Margin="0,0,15,8"/>
                                    <TextBlock Grid.Row="2" Grid.Column="1" Text="8891 (UDP)" Foreground="White" FontSize="12" FontWeight="Medium" Margin="0,0,0,8"/>

                                    <TextBlock Grid.Row="3" Grid.Column="0" Text="Update Method:" Foreground="#B0BEC5" FontSize="12" Margin="0,0,15,0"/>
                                    <TextBlock Grid.Row="3" Grid.Column="1" Text="Direct Network Broadcast" Foreground="White" FontSize="12" FontWeight="Medium"/>
                                </Grid>

                                <Border Background="#20FFFFFF" BorderBrush="{StaticResource WarningAccent}" BorderThickness="1"
                                       CornerRadius="6" Padding="15">
                                    <StackPanel>
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                            <controls:FeatherIcon IconName="list" IconColor="{StaticResource WarningAccent}"
                                                                Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="Update Process" FontWeight="SemiBold"
                                                      Foreground="{StaticResource WarningAccent}" FontSize="12"/>
                                        </StackPanel>
                                        <TextBlock TextWrapping="Wrap" Foreground="#B0BEC5" FontSize="11" LineHeight="18">
                                            1. Copy updated SafeLink files to ./Update folder<LineBreak/>
                                            2. Click 'Create Update Package' to generate version.json<LineBreak/>
                                            3. Click 'Send Update Command' to notify all devices<LineBreak/>
                                            4. Devices will automatically check and install updates<LineBreak/>
                                            5. No external server required - works on local network
                                        </TextBlock>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>
                    </Grid>
                </Border>
            </TabItem>
            </TabControl>
        </Grid>
    </Border>
</Window>
