using System;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Effects;
using SafeLink.Core;

namespace SafeLink.Views
{
    public partial class UnifiedMessageDialog : Window
    {
        public bool Result { get; private set; } = false;

        public UnifiedMessageDialog()
        {
            InitializeComponent();

            // Subscribe to language changes
            LanguageManager.LanguageChanged += UpdateLanguage;
            UpdateLanguage(); // Initial language setup

            // Set focus to No button by default
            Loaded += (s, e) => NoButton.Focus();

            // Handle Escape key to close as No
            KeyDown += (s, e) =>
            {
                if (e.Key == System.Windows.Input.Key.Escape)
                {
                    Result = false;
                    Close();
                }
            };

            // Handle window closing to unsubscribe from events
            Closing += (s, e) => LanguageManager.LanguageChanged -= UpdateLanguage;
        }

        private void YesButton_Click(object sender, RoutedEventArgs e)
        {
            Result = true;
            Close();
        }

        private void NoButton_Click(object sender, RoutedEventArgs e)
        {
            Result = false;
            Close();
        }

        private void UpdateLanguage()
        {
            try
            {
                var currentLanguage = LanguageManager.CurrentLanguage;
                
                if (currentLanguage == "ar")
                {
                    FlowDirection = FlowDirection.RightToLeft;
                }
                else
                {
                    FlowDirection = FlowDirection.LeftToRight;
                }
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to update language: {ex.Message}");
            }
        }

        public void SetupDialog(MessageDialogType type, string title = "SafeLink", string customMessage = null, string customExplanation = null)
        {
            Title = title;
            
            switch (type)
            {
                case MessageDialogType.CloseProgram:
                    SetupCloseProgram();
                    break;
                case MessageDialogType.SaveData:
                    SetupSaveData();
                    break;
                case MessageDialogType.DeleteData:
                    SetupDeleteData();
                    break;
                case MessageDialogType.NetworkDisconnect:
                    SetupNetworkDisconnect();
                    break;
                case MessageDialogType.Custom:
                    SetupCustom(customMessage, customExplanation);
                    break;
            }
        }

        private void SetupCloseProgram()
        {
            var currentLanguage = LanguageManager.CurrentLanguage;

            // Shield setup
            MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0xFF, 0x52, 0x52));
            
            if (currentLanguage == "ar")
            {
                MainQuestionText.Text = "هل تريد إغلاق البرنامج؟";
                ExplanationTitle.Text = "ماذا سيحدث:";
                ExplanationText.Text = "• سيتم قطع جميع الاتصالات النشطة\n• سيتم حفظ الرسائل المرسلة\n• سيتم إغلاق جميع النوافذ المفتوحة";
                SubMessageText.Text = "هذا الإجراء آمن ولن يؤثر على بياناتك المحفوظة";
                YesText.Text = "إغلاق";
                NoText.Text = "إلغاء";
            }
            else
            {
                MainQuestionText.Text = "Do you want to close the program?";
                ExplanationTitle.Text = "What will happen:";
                ExplanationText.Text = "• All active connections will be terminated\n• Sent messages will be saved\n• All open windows will be closed";
                SubMessageText.Text = "This action is safe and won't affect your saved data";
                YesText.Text = "Close";
                NoText.Text = "Cancel";
            }
            
            YesIcon.IconName = "power";
            NoIcon.IconName = "x";
        }

        private void SetupSaveData()
        {
            var currentLanguage = LanguageManager.CurrentLanguage;

            // Shield setup
            MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0x00, 0xE5, 0xFF));
            
            if (currentLanguage == "ar")
            {
                MainQuestionText.Text = "هل تريد حفظ التغييرات؟";
                ExplanationTitle.Text = "ماذا سيحدث:";
                ExplanationText.Text = "• سيتم حفظ جميع الإعدادات الحالية\n• سيتم تحديث ملف التكوين\n• ستصبح التغييرات دائمة";
                SubMessageText.Text = "يمكنك تغيير هذه الإعدادات لاحقاً من قائمة الإعدادات";
                YesText.Text = "حفظ";
                NoText.Text = "إلغاء";
            }
            else
            {
                MainQuestionText.Text = "Do you want to save changes?";
                ExplanationTitle.Text = "What will happen:";
                ExplanationText.Text = "• All current settings will be saved\n• Configuration file will be updated\n• Changes will become permanent";
                SubMessageText.Text = "You can change these settings later from the settings menu";
                YesText.Text = "Save";
                NoText.Text = "Cancel";
            }
            
            YesIcon.IconName = "save";
            NoIcon.IconName = "x";
        }

        private void SetupDeleteData()
        {
            var currentLanguage = LanguageManager.CurrentLanguage;

            // Shield setup
            MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0xFF, 0x52, 0x52));
            
            if (currentLanguage == "ar")
            {
                MainQuestionText.Text = "هل تريد حذف هذه البيانات؟";
                ExplanationTitle.Text = "تحذير - هذا الإجراء لا يمكن التراجع عنه:";
                ExplanationText.Text = "• سيتم حذف البيانات نهائياً\n• لن تتمكن من استرداد البيانات المحذوفة\n• قد تحتاج لإعادة إدخال المعلومات";
                SubMessageText.Text = "تأكد من أنك تريد المتابعة قبل الضغط على حذف";
                YesText.Text = "حذف";
                NoText.Text = "إلغاء";
            }
            else
            {
                MainQuestionText.Text = "Do you want to delete this data?";
                ExplanationTitle.Text = "Warning - This action cannot be undone:";
                ExplanationText.Text = "• Data will be permanently deleted\n• You won't be able to recover deleted data\n• You may need to re-enter information";
                SubMessageText.Text = "Make sure you want to continue before clicking delete";
                YesText.Text = "Delete";
                NoText.Text = "Cancel";
            }
            
            YesIcon.IconName = "trash-2";
            NoIcon.IconName = "x";
            SubMessageText.Foreground = new SolidColorBrush(Color.FromRgb(0xFF, 0x52, 0x52));
        }

        private void SetupNetworkDisconnect()
        {
            var currentLanguage = LanguageManager.CurrentLanguage;

            // Shield setup
            MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0xFF, 0xB7, 0x4D));
            
            if (currentLanguage == "ar")
            {
                MainQuestionText.Text = "هل تريد قطع الاتصال بالشبكة؟";
                ExplanationTitle.Text = "ماذا سيحدث:";
                ExplanationText.Text = "• سيتم قطع الاتصال مع جميع المستخدمين\n• ستتوقف مزامنة الرسائل\n• ستعمل في وضع عدم الاتصال";
                SubMessageText.Text = "يمكنك إعادة الاتصال في أي وقت من الإعدادات";
                YesText.Text = "قطع الاتصال";
                NoText.Text = "إلغاء";
            }
            else
            {
                MainQuestionText.Text = "Do you want to disconnect from network?";
                ExplanationTitle.Text = "What will happen:";
                ExplanationText.Text = "• Connection with all users will be terminated\n• Message synchronization will stop\n• You'll work in offline mode";
                SubMessageText.Text = "You can reconnect anytime from settings";
                YesText.Text = "Disconnect";
                NoText.Text = "Cancel";
            }
            
            YesIcon.IconName = "wifi-off";
            NoIcon.IconName = "x";
        }

        private void SetupCustom(string message, string explanation)
        {
            var currentLanguage = LanguageManager.CurrentLanguage;

            // Shield setup
            MessageShield.ShieldColor = new SolidColorBrush(Color.FromRgb(0x00, 0xE5, 0xFF));

            // Use provided message or fallback to language-appropriate default
            MainQuestionText.Text = message ?? LanguageManager.GetString("FactoryResetQuestion");
            ExplanationText.Text = explanation ?? LanguageManager.GetString("FactoryResetExplanation");

            // Set language-appropriate texts
            ExplanationTitle.Text = LanguageManager.GetString("FactoryResetDetails");
            YesText.Text = LanguageManager.GetString("Yes");
            NoText.Text = LanguageManager.GetString("No");

            if (currentLanguage == "ar")
            {
                SubMessageText.Text = "تأكد من اختيارك قبل المتابعة";
            }
            else
            {
                SubMessageText.Text = "Make sure of your choice before continuing";
            }
            
            YesIcon.IconName = "check";
            NoIcon.IconName = "x";
        }

        public static bool ShowDialog(Window owner, MessageDialogType type, string title = "SafeLink", string customMessage = null, string customExplanation = null)
        {
            try
            {
                var dialog = new UnifiedMessageDialog();
                dialog.SetupDialog(type, title, customMessage, customExplanation);

                if (owner != null)
                {
                    dialog.Owner = owner;
                }

                dialog.ShowDialog();
                return dialog.Result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing unified message dialog: {ex.Message}");
                return false;
            }
        }
    }

    public enum MessageDialogType
    {
        CloseProgram,
        SaveData,
        DeleteData,
        NetworkDisconnect,
        Custom
    }
}
